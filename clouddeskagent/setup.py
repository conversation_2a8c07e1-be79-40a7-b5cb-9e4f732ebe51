#!/usr/bin/env python3
"""
CloudDeskAgent Setup Script
"""

from setuptools import setup, find_packages
import os

# Read requirements
def read_requirements():
    requirements = []
    # Use Linux-specific requirements if available, otherwise fall back to main requirements
    req_file = 'requirements-linux.txt' if os.path.exists('requirements-linux.txt') else 'requirements.txt'
    if os.path.exists(req_file):
        with open(req_file, 'r') as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return requirements

# Read long description
def read_long_description():
    if os.path.exists('README.md'):
        with open('README.md', 'r', encoding='utf-8') as f:
            return f.read()
    return "CloudDeskAgent - Virtual Desktop Interface Agent"

setup(
    name="clouddeskagent",
    version="1.0.0",
    description="Virtual Desktop Interface Agent",
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    author="CloudDesk Team",
    packages=find_packages(),
    install_requires=read_requirements(),
    python_requires=">=3.8",
    entry_points={
        'console_scripts': [
            'clouddeskagent=clouddeskagent.__main__:main',
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: POSIX :: Linux",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: System :: Networking",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    ],
    include_package_data=True,
    package_data={
        'clouddeskagent': ['*.json', '*.png'],
    },
)
