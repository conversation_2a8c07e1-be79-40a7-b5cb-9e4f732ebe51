openapi: 3.0.0
info:
  title: CloudDeskAgent API
  description: API documentation for CloudDeskAgent
  version: 1.0.0
servers:
- url: http://127.0.0.1:8765
  description: Local agent server
components:
  securitySchemes:
    authToken:
      type: apiKey
      in: header
      name: X-Auth-Token
paths:
  /traylogo/md5:
    get:
      summary: Return the MD5 checksum of the tray icon logo.
      description: Return the MD5 checksum of the tray icon logo.
      operationId: get_traylogo_md5
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
        '404':
          description: Error 404
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /version:
    get:
      summary: Return the current version
      description: Return the current version
      operationId: get_version
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
  /status:
    get:
      summary: ''
      description: "\n    Return the current status and health information of the\
        \ CloudDeskAgent.\n    Includes version, active sessions, system info, and\
        \ service status.\n    "
      operationId: get_status
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
        '500':
          description: Error 500
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /traylogo:
    get:
      summary: Return the tray icon logo as a PNG file.
      description: Return the tray icon logo as a PNG file.
      operationId: get_traylogo_get
      responses:
        '200':
          description: File response
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '404':
          description: Error 404
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
    post:
      summary: ''
      description: "\n    Accept a new logo PNG file and store it.\n    Expects a\
        \ multipart/form field named 'logo'.\n    Returns the new md5.\n    "
      operationId: post_traylogo_post
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                logo:
                  type: string
                  format: binary
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Error 400
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /get-auth-token:
    get:
      summary: Get Auth Token Once
      description: Get Auth Token Once
      operationId: get_get_auth_token_once
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
        '403':
          description: Error 403
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /public-key:
    get:
      summary: Get Public Key
      description: Get Public Key
      operationId: get_get_public_key
      security:
      - authToken: []
      parameters:
      - name: X-Auth-Token
        in: header
        required: true
        schema:
          type: string
        description: 'Header parameter: X-Auth-Token'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
        '401':
          description: Error 401
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  /start:
    post:
      summary: Start Vpn And Rdp
      description: Start Vpn And Rdp
      operationId: post_start_vpn_and_rdp
      security:
      - authToken: []
      parameters:
      - name: X-Auth-Token
        in: header
        required: true
        schema:
          type: string
        description: 'Header parameter: X-Auth-Token'
      - name: X-Session-ID
        in: header
        required: false
        schema:
          type: string
        description: 'Header parameter: X-Session-ID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                wg_config:
                  type: string
                rdp_target_ip:
                  type: string
                rdp_target_port:
                  type: string
              required:
              - wg_config
              - rdp_target_ip
              - rdp_target_port
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
        '401':
          description: Error 401
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '400':
          description: Error 400
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '403':
          description: Error 403
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
