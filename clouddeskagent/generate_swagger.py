#!/usr/bin/env python3
"""
Clean Automatic Swagger Generator for CloudDeskAgent
Fully introspective - no hardcoded API definitions
"""

import os
import sys
import json
import yaml
import inspect
import re
import ast
from typing import Dict, Any, List

# Add clouddeskagent to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'clouddeskagent'))

class SwaggerAnalyzer:
    def __init__(self):
        self.security_endpoints = set()
        
    def analyze_function_source(self, func) -> Dict[str, Any]:
        """Analyze function source code for parameters and responses"""
        try:
            source = inspect.getsource(func)
            tree = ast.parse(source)
            
            info = {
                'header_params': [],
                'json_params': [],
                'query_params': [],
                'file_params': [],
                'response_types': [],
                'status_codes': [],
                'requires_auth': False
            }
            
            # Analyze AST nodes for parameter extraction
            for node in ast.walk(tree):
                # Look for request.headers.get() calls
                if (isinstance(node, ast.Call) and
                    isinstance(node.func, ast.Attribute) and
                    isinstance(node.func.value, ast.Attribute) and
                    isinstance(node.func.value.value, ast.Name) and
                    node.func.value.value.id == 'request' and
                    node.func.value.attr == 'headers' and
                    node.func.attr == 'get'):
                    
                    if node.args:
                        param_name = self._extract_string_value(node.args[0])
                        if param_name:
                            info['header_params'].append(param_name)
                
                # Look for data.get() calls (JSON body parameters)
                if (isinstance(node, ast.Call) and
                    isinstance(node.func, ast.Attribute) and
                    isinstance(node.func.value, ast.Name) and
                    node.func.value.id == 'data' and
                    node.func.attr == 'get'):
                    
                    if node.args:
                        param_name = self._extract_string_value(node.args[0])
                        if param_name:
                            info['json_params'].append(param_name)
                
                # Look for request.args.get() calls (query parameters)
                if (isinstance(node, ast.Call) and
                    isinstance(node.func, ast.Attribute) and
                    isinstance(node.func.value, ast.Attribute) and
                    isinstance(node.func.value.value, ast.Name) and
                    node.func.value.value.id == 'request' and
                    node.func.value.attr == 'args' and
                    node.func.attr == 'get'):
                    
                    if node.args:
                        param_name = self._extract_string_value(node.args[0])
                        if param_name:
                            info['query_params'].append(param_name)
                
                # Look for request.files.get() calls
                if (isinstance(node, ast.Call) and
                    isinstance(node.func, ast.Attribute) and
                    isinstance(node.func.value, ast.Attribute) and
                    isinstance(node.func.value.value, ast.Name) and
                    node.func.value.value.id == 'request' and
                    node.func.value.attr == 'files' and
                    node.func.attr == 'get'):
                    
                    if node.args:
                        param_name = self._extract_string_value(node.args[0])
                        if param_name:
                            info['file_params'].append(param_name)
                
                # Look for jsonify calls and return statements
                if isinstance(node, ast.Call):
                    if (isinstance(node.func, ast.Name) and 
                        node.func.id == 'jsonify'):
                        info['response_types'].append('json')
                    elif (isinstance(node.func, ast.Name) and 
                          node.func.id == 'send_file'):
                        info['response_types'].append('file')
                
                # Look for status codes in return tuples
                if isinstance(node, ast.Return):
                    if isinstance(node.value, ast.Tuple):
                        for elt in node.value.elts:
                            if isinstance(elt, ast.Constant) and isinstance(elt.value, int):
                                info['status_codes'].append(elt.value)
            
            # Check for auth token validation
            if 'AUTH_TOKEN' in source or 'X-Auth-Token' in source:
                info['requires_auth'] = True
                
            return info
            
        except Exception:
            return {
                'header_params': [], 'json_params': [], 'query_params': [], 'file_params': [],
                'response_types': ['json'], 'status_codes': [200], 'requires_auth': False
            }
    
    def _extract_string_value(self, node):
        """Extract string value from AST node (compatible with different Python versions)"""
        if hasattr(node, 's'):  # Python < 3.8
            return node.s
        elif hasattr(node, 'value') and isinstance(node.value, str):  # Python >= 3.8
            return node.value
        return None
    
    def _analyze_function_ast(self, tree, source):
        """Analyze function AST for parameters and patterns"""
        info = {
            'header_params': [],
            'json_params': [],
            'query_params': [],
            'file_params': [],
            'response_types': [],
            'status_codes': [],
            'requires_auth': False
        }
        
        # Analyze AST nodes for parameter extraction
        for node in ast.walk(tree):
            # Look for request.headers.get() calls
            if (isinstance(node, ast.Call) and
                isinstance(node.func, ast.Attribute) and
                isinstance(node.func.value, ast.Attribute) and
                isinstance(node.func.value.value, ast.Name) and
                node.func.value.value.id == 'request' and
                node.func.value.attr == 'headers' and
                node.func.attr == 'get'):
                
                if node.args:
                    param_name = self._extract_string_value(node.args[0])
                    if param_name and param_name not in info['header_params']:
                        info['header_params'].append(param_name)
            
            # Look for data.get() calls (JSON body parameters)
            if (isinstance(node, ast.Call) and
                isinstance(node.func, ast.Attribute) and
                isinstance(node.func.value, ast.Name) and
                node.func.value.id == 'data' and
                node.func.attr == 'get'):
                
                if node.args:
                    param_name = self._extract_string_value(node.args[0])
                    if param_name and param_name not in info['json_params']:
                        info['json_params'].append(param_name)
            
            # Look for request.args.get() calls (query parameters)
            if (isinstance(node, ast.Call) and
                isinstance(node.func, ast.Attribute) and
                isinstance(node.func.value, ast.Attribute) and
                isinstance(node.func.value.value, ast.Name) and
                node.func.value.value.id == 'request' and
                node.func.value.attr == 'args' and
                node.func.attr == 'get'):
                
                if node.args:
                    param_name = self._extract_string_value(node.args[0])
                    if param_name and param_name not in info['query_params']:
                        info['query_params'].append(param_name)
            
            # Look for request.files.get() calls
            if (isinstance(node, ast.Call) and
                isinstance(node.func, ast.Attribute) and
                isinstance(node.func.value, ast.Attribute) and
                isinstance(node.func.value.value, ast.Name) and
                node.func.value.value.id == 'request' and
                node.func.value.attr == 'files' and
                node.func.attr == 'get'):
                
                if node.args:
                    param_name = self._extract_string_value(node.args[0])
                    if param_name and param_name not in info['file_params']:
                        info['file_params'].append(param_name)
            
            # Look for jsonify calls and return statements
            if isinstance(node, ast.Call):
                if (isinstance(node.func, ast.Name) and 
                    node.func.id == 'jsonify'):
                    if 'json' not in info['response_types']:
                        info['response_types'].append('json')
                elif (isinstance(node.func, ast.Name) and 
                      node.func.id == 'send_file'):
                    if 'file' not in info['response_types']:
                        info['response_types'].append('file')
            
            # Look for status codes in return tuples
            if isinstance(node, ast.Return):
                if isinstance(node.value, ast.Tuple):
                    for elt in node.value.elts:
                        if isinstance(elt, ast.Constant) and isinstance(elt.value, int):
                            if elt.value not in info['status_codes']:
                                info['status_codes'].append(elt.value)
        
        # Check for auth token validation
        if 'AUTH_TOKEN' in source or 'X-Auth-Token' in source:
            info['requires_auth'] = True
        
        # Default response type if none found
        if not info['response_types']:
            info['response_types'] = ['json']
        
        # Default status code if none found
        if not info['status_codes']:
            info['status_codes'] = [200]
            
        return info
    
    def extract_routes(self, app) -> List[Dict[str, Any]]:
        """Extract all route information dynamically"""
        routes = []
        
        for rule in app.url_map.iter_rules():
            if rule.endpoint == 'static':
                continue
                
            view_func = app.view_functions[rule.endpoint]
            docstring = inspect.getdoc(view_func) or ""
            
            # Analyze function source
            func_info = self.analyze_function_source(view_func)
            
            # Extract summary from docstring first line
            summary = docstring.split('\n')[0] if docstring else f"{rule.endpoint.replace('_', ' ').title()}"
            
            route = {
                'path': rule.rule,
                'methods': list(rule.methods - {'HEAD', 'OPTIONS'}),
                'endpoint': rule.endpoint,
                'function_name': view_func.__name__,
                'summary': summary,
                'description': docstring,
                'requires_auth': func_info['requires_auth'],
                'request_params': func_info['request_params'],
                'response_types': func_info['response_types'],
                'status_codes': func_info['status_codes'] or [200]
            }
            
            routes.append(route)
            
        return routes
    
    def generate_schema(self, route: Dict[str, Any], method: str) -> Dict[str, Any]:
        """Generate OpenAPI schema for a route/method combination"""
        operation = {
            "summary": route['summary'],
            "description": route['description'] or route['summary'],
            "operationId": f"{method.lower()}_{route['function_name']}",
            "responses": {}
        }
        
        # Add security if required
        if route['requires_auth']:
            operation["security"] = [{"authToken": []}]
        
        # Generate request body if needed
        if method.upper() in ['POST', 'PUT', 'PATCH']:
            if 'json_body' in route['request_params']:
                operation["requestBody"] = {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {"type": "object"}
                        }
                    }
                }
            elif 'file_upload' in route['request_params']:
                operation["requestBody"] = {
                    "required": True,
                    "content": {
                        "multipart/form-data": {
                            "schema": {"type": "object"}
                        }
                    }
                }
        
        # Generate responses
        for status_code in route['status_codes']:
            if 'json' in route['response_types']:
                operation["responses"][str(status_code)] = {
                    "description": f"Response {status_code}",
                    "content": {
                        "application/json": {
                            "schema": {"type": "object"}
                        }
                    }
                }
            elif 'file' in route['response_types']:
                operation["responses"][str(status_code)] = {
                    "description": f"File response {status_code}",
                    "content": {
                        "application/octet-stream": {
                            "schema": {"type": "string", "format": "binary"}
                        }
                    }
                }
            else:
                operation["responses"][str(status_code)] = {
                    "description": f"Response {status_code}"
                }
        
        # Default 200 response if none found
        if not operation["responses"]:
            operation["responses"]["200"] = {
                "description": "Success",
                "content": {
                    "application/json": {
                        "schema": {"type": "object"}
                    }
                }
            }
        
        return operation
    
    def generate_openapi_spec(self, routes: List[Dict[str, Any]], app_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate complete OpenAPI specification"""
        spec = {
            "openapi": "3.0.0",
            "info": {
                "title": app_info.get("title", "CloudDeskAgent API"),
                "description": app_info.get("description", "Auto-generated API documentation"),
                "version": app_info.get("version", "1.0.0")
            },
            "servers": [
                {
                    "url": app_info.get("server_url", "http://127.0.0.1:8765"),
                    "description": "Local agent server"
                }
            ],
            "components": {
                "securitySchemes": {
                    "authToken": {
                        "type": "apiKey",
                        "in": "header",
                        "name": "X-Auth-Token"
                    }
                }
            },
            "paths": {}
        }
        
        # Build paths
        for route in routes:
            path = route['path']
            if path not in spec['paths']:
                spec['paths'][path] = {}
            
            for method in route['methods']:
                method_lower = method.lower()
                spec['paths'][path][method_lower] = self.generate_schema(route, method)
        
        return spec
    
    def generate_openapi_spec_from_routes(self, routes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate OpenAPI spec from analyzed routes"""
        spec = {
            "openapi": "3.0.0",
            "info": {
                "title": "CloudDeskAgent API",
                "description": "API documentation for CloudDeskAgent",
                "version": "1.0.0"
            },
            "servers": [
                {
                    "url": "http://127.0.0.1:8765",
                    "description": "Local agent server"
                }
            ],
            "components": {
                "securitySchemes": {
                    "authToken": {
                        "type": "apiKey",
                        "in": "header",
                        "name": "X-Auth-Token"
                    }
                }
            },
            "paths": {}
        }
        
        # Build paths from routes
        for route in routes:
            path = route['path']
            if path not in spec['paths']:
                spec['paths'][path] = {}
            
            for method in route['methods']:
                method_lower = method.lower()
                
                operation = {
                    "summary": route['summary'],
                    "description": route['description'] or route['summary'],
                    "operationId": f"{method_lower}_{route['function_name']}"
                }
                
                # Add security if required
                if route['requires_auth']:
                    operation["security"] = [{"authToken": []}]
                
                # Add parameters
                parameters = []
                
                # Add header parameters
                for header_param in route['header_params']:
                    parameters.append({
                        "name": header_param,
                        "in": "header",
                        "required": header_param == "X-Auth-Token",
                        "schema": {"type": "string"},
                        "description": f"Header parameter: {header_param}"
                    })
                
                # Add query parameters
                for query_param in route['query_params']:
                    parameters.append({
                        "name": query_param,
                        "in": "query",
                        "required": False,
                        "schema": {"type": "string"},
                        "description": f"Query parameter: {query_param}"
                    })
                
                if parameters:
                    operation["parameters"] = parameters
                
                # Add request body for POST/PUT methods
                if method.upper() in ['POST', 'PUT', 'PATCH']:
                    if route['json_params']:
                        # Build JSON schema from detected parameters
                        properties = {}
                        required = []
                        for param in route['json_params']:
                            properties[param] = {"type": "string"}
                            # Common required parameters
                            if param in ['wg_config', 'rdp_target_ip', 'rdp_target_port']:
                                required.append(param)
                        
                        schema = {
                            "type": "object",
                            "properties": properties
                        }
                        if required:
                            schema["required"] = required
                        
                        operation["requestBody"] = {
                            "required": True,
                            "content": {
                                "application/json": {
                                    "schema": schema
                                }
                            }
                        }
                    elif route['file_params']:
                        # Build multipart schema from detected file parameters
                        properties = {}
                        for param in route['file_params']:
                            properties[param] = {
                                "type": "string",
                                "format": "binary"
                            }
                        
                        operation["requestBody"] = {
                            "required": True,
                            "content": {
                                "multipart/form-data": {
                                    "schema": {
                                        "type": "object",
                                        "properties": properties
                                    }
                                }
                            }
                        }
                
                # Add responses based on detected response types
                responses = {}
                
                if 'file' in route['response_types']:
                    responses["200"] = {
                        "description": "File response",
                        "content": {
                            "application/octet-stream": {
                                "schema": {"type": "string", "format": "binary"}
                            }
                        }
                    }
                    responses["404"] = {"description": "File not found"}
                else:
                    responses["200"] = {
                        "description": "Success",
                        "content": {
                            "application/json": {
                                "schema": {"type": "object"}
                            }
                        }
                    }
                
                # Add error responses based on detected status codes
                for status_code in route['status_codes']:
                    if status_code != 200:
                        responses[str(status_code)] = {
                            "description": f"Error {status_code}",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "error": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                
                # Add auth error for protected endpoints
                if route['requires_auth'] and "401" not in responses:
                    responses["401"] = {
                        "description": "Unauthorized",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "error": {"type": "string"}
                                    }
                                }
                            }
                        }
                    }
                
                operation["responses"] = responses
                
                spec['paths'][path][method_lower] = operation
        
        return spec

    def analyze_source_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Analyze Flask routes from source file without importing"""
        routes = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # Look for @app.route decorators
                    for decorator in node.decorator_list:
                        if (isinstance(decorator, ast.Call) and
                            isinstance(decorator.func, ast.Attribute) and
                            decorator.func.attr == 'route'):
                            
                            # Extract route path
                            if decorator.args:
                                arg = decorator.args[0]
                                if hasattr(arg, 's'):  # Python < 3.8
                                    path = arg.s
                                elif hasattr(arg, 'value'):  # Python >= 3.8
                                    path = arg.value
                                else:
                                    continue
                            else:
                                continue
                            
                            # Extract methods
                            methods = ['GET']  # default
                            for keyword in decorator.keywords:
                                if keyword.arg == 'methods':
                                    if isinstance(keyword.value, ast.List):
                                        methods = []
                                        for elt in keyword.value.elts:
                                            if hasattr(elt, 's'):  # Python < 3.8
                                                methods.append(elt.s)
                                            elif hasattr(elt, 'value'):  # Python >= 3.8
                                                methods.append(elt.value)
                            
                            # Get docstring
                            docstring = ""
                            if (node.body and isinstance(node.body[0], ast.Expr) and
                                isinstance(node.body[0].value, ast.Constant)):
                                docstring = node.body[0].value.value
                            
                            # Analyze function parameters using enhanced detection
                            func_source = ast.get_source_segment(content, node)
                            if func_source:
                                # Create a mini function to analyze
                                try:
                                    func_tree = ast.parse(func_source)
                                    func_info = self._analyze_function_ast(func_tree, func_source)
                                except:
                                    func_info = {
                                        'header_params': [], 'json_params': [], 'query_params': [], 'file_params': [],
                                        'response_types': ['json'], 'status_codes': [200], 'requires_auth': False
                                    }
                            else:
                                func_info = {
                                    'header_params': [], 'json_params': [], 'query_params': [], 'file_params': [],
                                    'response_types': ['json'], 'status_codes': [200], 'requires_auth': False
                                }
                            
                            summary = docstring.split('\n')[0] if docstring else f"{node.name.replace('_', ' ').title()}"
                            
                            route = {
                                'path': path,
                                'methods': methods,
                                'function_name': node.name,
                                'summary': summary,
                                'description': docstring,
                                'requires_auth': func_info['requires_auth'],
                                'header_params': func_info['header_params'],
                                'json_params': func_info['json_params'],
                                'query_params': func_info['query_params'],
                                'file_params': func_info['file_params'],
                                'response_types': func_info['response_types'],
                                'status_codes': func_info['status_codes']
                            }
                            
                            routes.append(route)
                            break
            
            return routes
            
        except Exception as e:
            print(f"Error analyzing source file: {e}")
            return []

def main():
    """Generate swagger documentation automatically"""
    try:
        analyzer = SwaggerAnalyzer()
        
        # Analyze source file directly instead of importing
        main_file = os.path.join(os.path.dirname(__file__), 'clouddeskagent', '__main__.py')
        print(f"Analyzing source file: {main_file}")
        
        routes = analyzer.analyze_source_file(main_file)
        
        print(f"Found {len(routes)} routes:")
        for route in routes:
            methods_str = ', '.join(route['methods'])
            auth_str = " [AUTH]" if route['requires_auth'] else ""
            print(f"  {methods_str} {route['path']}{auth_str}")
        
        # Generate OpenAPI spec
        spec = analyzer.generate_openapi_spec_from_routes(routes)
        
        # Write YAML
        output_path = os.path.join(os.path.dirname(__file__), 'swagger.yaml')
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(spec, f, default_flow_style=False, sort_keys=False)
        
        print(f"\nGenerated: {output_path}")
        print(f"Documented {len(spec['paths'])} endpoints")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
