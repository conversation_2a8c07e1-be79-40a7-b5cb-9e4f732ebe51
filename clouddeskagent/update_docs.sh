#!/bin/bash
# CloudDeskAgent Documentation Update Script
# Automatically generates swagger.yaml from current API implementation

set -e

echo "🔄 Updating CloudDeskAgent API documentation..."

# Generate swagger documentation
python3 generate_swagger.py

echo "✅ Documentation updated successfully!"
echo "📄 Generated files:"
echo "   - swagger.yaml (OpenAPI 3.0 specification)"

# Optionally validate the generated YAML
if command -v yamllint &> /dev/null; then
    echo "🔍 Validating YAML syntax..."
    yamllint swagger.yaml && echo "✅ YAML syntax is valid"
else
    echo "💡 Install yamllint for YAML validation: pip install yamllint"
fi

echo "🚀 API documentation is ready!"
