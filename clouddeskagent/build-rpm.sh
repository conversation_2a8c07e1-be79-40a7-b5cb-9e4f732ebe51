#!/bin/bash
# CloudDeskAgent RPM Package Builder - Production Ready & Containerized
# Version: 5.0.0
# Description: Fully containerized, CI/CD-ready RPM builder.
# No manual dependencies needed. Just run it.

set -euo pipefail

# --- Configuration ---
readonly APP_NAME="clouddeskagent"
readonly APP_VERSION="1.0.0"
readonly CONTAINER_IMAGE="fedora:latest"
readonly BUILD_DIR="build-rpm"

# --- Colors ---
readonly BLUE='\033[0;34m'
readonly GREEN='\033[0;32m'
readonly RED='\033[0;31m'
readonly NC='\033[0m' # No Color

# --- Logging ---
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }

# --- Core Functions ---
detect_container_runtime() {
    if command -v podman &>/dev/null; then
        echo "podman"
    elif command -v docker &>/dev/null; then
        echo "docker"
    else
        log_error "No container runtime found. Please install 'podman' or 'docker'."
        exit 1
    fi
}

create_build_script() {
    # This script runs *inside* the container
    cat > "$BUILD_DIR/build-inside-container.sh" << 'EOF'
#!/bin/bash
set -euo pipefail

APP_NAME="clouddeskagent"
APP_VERSION="1.0.0"

echo "[CONT] Installing build dependencies..."
dnf install -y rpm-build rpmdevtools python3-devel python3-pip python3-tkinter python3-pillow wireguard-tools remmina

echo "[CONT] Setting up RPM build environment..."
rpmdev-setuptree

echo "[CONT] Creating source tarball..."
TEMP_SOURCE_DIR=$(mktemp -d)
mkdir -p "$TEMP_SOURCE_DIR/$APP_NAME-$APP_VERSION"

cp -r /workspace/clouddeskagent "$TEMP_SOURCE_DIR/$APP_NAME-$APP_VERSION/"
cp /workspace/requirements-linux.txt "$TEMP_SOURCE_DIR/$APP_NAME-$APP_VERSION/"
cp /workspace/config.json "$TEMP_SOURCE_DIR/$APP_NAME-$APP_VERSION/"

(cd "$TEMP_SOURCE_DIR" && tar -czf "/root/rpmbuild/SOURCES/$APP_NAME-$APP_VERSION.tar.gz" "$APP_NAME-$APP_VERSION")
rm -rf "$TEMP_SOURCE_DIR"
echo "[CONT] Source tarball created."

echo "[CONT] Creating SPEC file..."
CHANGELOG_DATE=$(date "+%a %b %d %Y")

cat > "/root/rpmbuild/SPECS/$APP_NAME.spec" << SPEC_EOF
Name:           $APP_NAME
Version:        $APP_VERSION
Release:        1%{?dist}
Summary:        CloudDeskAgent - Virtual Desktop Interface Agent
License:        Proprietary
URL:            https://github.com/HaSanAlkholy/agent
Source0:        %{name}-%{version}.tar.gz
BuildArch:      noarch

BuildRequires:  python3-devel
BuildRequires:  python3-pillow

Requires:       python3
Requires:       python3-tkinter
Requires:       wireguard-tools
Requires:       remmina
Requires:       python3-pip

%description
CloudDeskAgent is a Virtual Desktop Interface Agent that enables secure VPN
connections and RDP sessions via WireGuard and Remmina.

%prep
%autosetup

%build
# No build actions needed for a pure Python application

%install
mkdir -p %{buildroot}/opt/$APP_NAME
mkdir -p %{buildroot}/usr/local/bin
mkdir -p %{buildroot}/usr/share/applications
mkdir -p %{buildroot}/usr/share/icons/hicolor/48x48/apps

cp -r clouddeskagent %{buildroot}/opt/$APP_NAME/
cp requirements-linux.txt %{buildroot}/opt/$APP_NAME/
cp config.json %{buildroot}/opt/$APP_NAME/

# Create launcher script
cat > %{buildroot}/usr/local/bin/$APP_NAME << LAUNCHER_EOF
#!/bin/bash
set -e
INSTALL_DIR="/opt/$APP_NAME"
VENV_DIR="\$INSTALL_DIR/venv"
CONFIG_DIR="\$HOME/.clouddeskagent"

mkdir -p "\$CONFIG_DIR"

if [ ! -d "\$VENV_DIR" ]; then
    echo "Setting up CloudDeskAgent virtual environment..."
    python3 -m venv --system-site-packages "\$VENV_DIR"
    "\$VENV_DIR/bin/pip" install --upgrade pip
    "\$VENV_DIR/bin/pip" install -r "\$INSTALL_DIR/requirements-linux.txt"
fi

cd "\$INSTALL_DIR"
exec "\$VENV_DIR/bin/python" -m clouddeskagent "\$@"
LAUNCHER_EOF

chmod +x %{buildroot}/usr/local/bin/$APP_NAME

# Create desktop entry
cat > %{buildroot}/usr/share/applications/$APP_NAME.desktop << DESKTOP_EOF
[Desktop Entry]
Type=Application
Name=CloudDesk Agent
Comment=Virtual Desktop Interface Agent
Exec=/usr/local/bin/$APP_NAME
Icon=$APP_NAME
Terminal=false
Categories=Network;RemoteAccess;
DESKTOP_EOF

# Create icon with Pillow
python3 -c "
from PIL import Image, ImageDraw
img = Image.new('RGBA', (48, 48), (0, 0, 0, 0))
draw = ImageDraw.Draw(img)
draw.ellipse([5, 10, 43, 38], fill=(70, 130, 180, 255))
draw.ellipse([15, 5, 33, 25], fill=(70, 130, 180, 255))
draw.text((15, 18), 'CD', fill='white')
img.save('%{buildroot}/usr/share/icons/hicolor/48x48/apps/$APP_NAME.png')
"

%post
echo "CloudDeskAgent installed successfully!"
SUDOERS_FILE="/etc/sudoers.d/$APP_NAME-wireguard"
if [ ! -f "\$SUDOERS_FILE" ]; then
    echo "ALL ALL=(ALL) NOPASSWD: /usr/bin/wg-quick" > "\$SUDOERS_FILE"
    chmod 440 "\$SUDOERS_FILE"
fi
update-desktop-database /usr/share/applications &>/dev/null || true

%preun
if [ \$1 -eq 0 ]; then
    pkill -f "python.*$APP_NAME" &>/dev/null || true
    rm -rf /opt/$APP_NAME/venv &>/dev/null || true
fi

%postun
if [ \$1 -eq 0 ]; then
    rm -f /etc/sudoers.d/$APP_NAME-wireguard &>/dev/null || true
    update-desktop-database /usr/share/applications &>/dev/null || true
fi

%files
/opt/$APP_NAME/
/usr/local/bin/$APP_NAME
/usr/share/applications/$APP_NAME.desktop
/usr/share/icons/hicolor/48x48/apps/$APP_NAME.png

%changelog
* $CHANGELOG_DATE GIG.TECH NV <<EMAIL>> - $APP_VERSION-1
- Initial RPM package release

SPEC_EOF

echo "[CONT] Building RPM package..."
rpmbuild -ba "/root/rpmbuild/SPECS/$APP_NAME.spec"

echo "[CONT] Copying RPM to output directory..."
cp /root/rpmbuild/RPMS/noarch/*.rpm /output/

echo "[CONT] Build complete."
EOF

    chmod +x "$BUILD_DIR/build-inside-container.sh"
}

build() {
    local runtime=$(detect_container_runtime)
    log_info "Using '$runtime' container runtime."

    rm -rf "$BUILD_DIR"
    mkdir -p "$BUILD_DIR"
    
    create_build_script

    log_info "Running build inside container..."
    $runtime run --rm --privileged \
        -v "$(pwd):/workspace:ro" \
        -v "$(pwd)/$BUILD_DIR:/output" \
        "$CONTAINER_IMAGE" \
        bash /output/build-inside-container.sh

    local rpm_file=$(find "$BUILD_DIR" -name "*.rpm" -type f 2>/dev/null | head -1)
    if [ -n "$rpm_file" ]; then
        cp "$rpm_file" .
        local rpm_name=$(basename "$rpm_file")
        log_success "RPM build successful: $rpm_name"
        log_info "To install: sudo dnf install ./$rpm_name"
    else
        log_error "RPM build failed. See container logs for details."
        exit 1
    fi
}

clean() {
    log_info "Cleaning up build artifacts..."
    rm -rf "$BUILD_DIR"
    rm -f ${APP_NAME}-*.rpm
    log_success "Cleanup complete."
}

usage() {
    echo "CloudDeskAgent RPM Builder v5.0.0 - Production Ready"
    echo "Usage: $0 [build|clean|help]"
    echo
    echo "Commands:"
    echo "  build     Build RPM package using a container (default)"
    echo "  clean     Remove all build artifacts"
    echo "  help      Show this help message"
}

main() {
    case "${1:-build}" in
        build|"")
            build
            ;;
        clean)
            clean
            ;;
        help|-h|--help)
            usage
            ;;
        *)
            log_error "Unknown command: $1"
            usage
            exit 1
            ;;
    esac
}

main "$@"
