# CloudDeskAgent Linux Packaging

This document outlines the ongoing project to create a robust, multi-format Linux packaging system for the CloudDeskAgent application.

## 1. Project Goal

The primary objective is to transition from a single AppImage package to a modern, multi-format build system that supports native packages like DEB (for Ubuntu/Debian) and RPM (for Fedora/CentOS). This will provide better system integration, dependency management, and a more seamless user experience.

The build system must support multiple CPU architectures, including `amd64`, `arm64`, and `armhf` (for Raspberry Pi).

## 2. Initial Problem & Investigation

The original AppImage build had a critical issue where the system tray icon would not appear on many Linux distributions. This was caused by the `pystray` library being unable to find the necessary GTK/`gi` system libraries within the sandboxed AppImage environment.

After investigation, we determined that native packaging was the best solution, as it allows the application to correctly integrate with the host system's libraries.

## 3. Our Plan

We are tackling the packaging one format at a time to ensure a clean, modular, and production-ready build system.

1.  **DEB Packages (Completed)**: Create a fully functional build system for Ubuntu/Debian.
2.  **RPM Packages (Next)**: Develop a similar build script for Fedora/CentOS.
3.  **Snap Packages (Future)**: Investigate and implement Snap packaging.

## 4. What We've Achieved (DEB Packaging)

We have successfully created a robust build system for DEB packages.

*   **`build-deb.sh`**: A single, well-documented script that handles the entire build process.
*   **Multi-Architecture Support**: The script can build packages for `amd64`, `arm64`, and `armhf`.
*   **System Tray Fix**: The system tray icon issue was resolved by creating a Python virtual environment (`venv`) during installation. This `venv` is configured with `--system-site-packages` to allow it to access the host system's GTK libraries, fixing the `gi` module error.
*   **Desktop Integration**: The package installs a `.desktop` file for application menu integration and a launcher script in `/usr/local/bin`.

### Known Issue: Ghost Icon

We encountered an issue where uninstalling the package would sometimes leave a non-functional "ghost" icon in the application menu. This is caused by the desktop environment's caching.

**Solution**: To fully remove the icon, the user must manually clear the desktop database cache after uninstalling the package. The recommended cleanup process is:

```bash
# 1. Purge the package
sudo apt-get purge -y clouddeskagent

# 2. Check for and remove any leftover .desktop files
sudo rm /usr/share/applications/clouddeskagent.desktop
rm ~/.local/share/applications/clouddeskagent.desktop

# 3. Update the desktop database
sudo update-desktop-database
```

## 5. VPN Issues Resolved (Latest Updates)

### Critical VPN Shutdown Bug Fixed

We identified and resolved a critical issue where the VPN would shut down immediately after launching Remmina RDP client:

**Root Cause**: Remmina uses single-instance architecture. When launching a new RDP session, it communicates with the existing Remmina process and the launcher exits immediately, causing the VPN monitor to incorrectly detect "RDP closed" and shut down the VPN.

**Solution Implemented**:
- **Fixed RDP Monitoring Architecture**: Replaced process-based monitoring with network-based monitoring
- **Socket Connectivity Checks**: Monitor actual RDP connections using socket tests and netstat
- **Intelligent Session Detection**: VPN stays active as long as RDP connection exists
- **Robust Error Handling**: Graceful fallbacks and double-verification to prevent false positives

### Configuration Path Bug Fixed

**Issue**: Config file was created in system directory instead of user directory, causing permission issues and preventing users from accessing their auth tokens.

**Solution**: Fixed CONFIG_PATH to use `~/.clouddeskagent/config.json` for proper user-specific configuration.

### Desktop Integration Improvements

- **Fixed App Menu Icon**: Now uses proper CloudDeskAgent.png instead of fallback logo
- **Improved App Name**: Changed from "CloudDeskAgent" to "CloudDesk Agent" for better readability
- **Enhanced Description**: More descriptive comment in desktop entry

## 6. Multi-Architecture DEB Support Completed

**✅ MAJOR UPDATE**: The DEB build system now supports **all architectures simultaneously**.

### Enhanced Build System Features
- **Multi-Architecture Support**: `./build-deb.sh --all` builds for amd64, arm64, and armhf
- **Single Architecture Builds**: `./build-deb.sh amd64` for specific targets
- **Production Ready**: All packages tested and verified for cross-architecture compatibility
- **Raspberry Pi Support**: Full support for Pi 2/3/4/5 (both 32-bit armhf and 64-bit arm64)

### Architecture Coverage
| Architecture | Target Hardware | Package Size | Status |
|--------------|-----------------|--------------|---------|
| `amd64` | Intel/AMD 64-bit PCs | 216KB | ✅ Tested |
| `arm64` | ARM 64-bit (Pi 3/4/5, Apple Silicon) | 216KB | ✅ Verified |
| `armhf` | ARM 32-bit (Pi 2/3/4) | 216KB | ✅ Verified |

### Current Status Summary
The DEB packaging system is **complete and production-ready** with all critical issues resolved:

- ✅ **Multi-Architecture Support**: All major architectures covered
- ✅ **VPN Functionality**: Fully working with proper session management  
- ✅ **RDP Integration**: Stable Remmina integration with correct monitoring
- ✅ **User Experience**: Proper config file handling and auth token access
- ✅ **Desktop Integration**: Correct icon and menu entry
- ✅ **Cross-Platform Compatibility**: Pure Python approach ensures identical functionality

## 7. RPM Packaging Research & Planning

**Research completed** for the next phase: RPM packaging system development.

### Key Research Findings

**Architecture Mapping** (RPM uses different names than DEB):
| DEB Architecture | RPM Architecture | Target Hardware |
|------------------|------------------|-----------------|
| `amd64` | `x86_64` | Intel/AMD 64-bit |
| `arm64` | `aarch64` | ARM 64-bit |
| `armhf` | `armv7hl` | ARM 32-bit |

**Critical Insight**: For pure Python applications like CloudDeskAgent, RPM best practices recommend using `BuildArch: noarch` to create a **single universal package** instead of multiple architecture-specific packages.

### RPM Implementation Strategy

**Fundamental Difference from DEB Approach**:
- **DEB**: 3 architecture-specific packages (current working approach)
- **RPM**: 1 universal `noarch` package (RPM best practice for Python apps)

**Technical Requirements**:
- SPEC file with `BuildArch: noarch`
- Python macros: `%{python3_sitelib}`, `%pyproject_wheel`
- Scriptlets: `%post`, `%preun` for installation/removal
- Different dependencies: `python3-devel`, `wireguard-tools`, `remmina`
- SELinux considerations for Fedora/RHEL

### Next Phase Plan

The next development phase will implement:
1. **`build-rpm.sh`** script following RPM best practices
2. **Universal `noarch` package** approach for better maintainability
3. **Fedora/CentOS/RHEL compatibility** testing
4. **Consistent functionality** with DEB packages
