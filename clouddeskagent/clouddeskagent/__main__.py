# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REP<PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import signal
import threading
import subprocess
import os
import json
import sys
import socket

# Force pystray to use the AppIndicator backend on Linux for better compatibility.
if sys.platform == "linux":
    os.environ['PYSTRAY_BACKEND'] = 'appindicator'
import logging
import time
import sys
import base64
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from cryptography.hazmat.primitives.asymmetric import x25519
from cryptography.hazmat.primitives import serialization

from .lib import *
import platform
from threading import Lock
from werkzeug.serving import make_server

# Windows-specific imports (conditional)
if platform.system() == "Windows":
    import win32file
    import win32serviceutil
    import pywintypes
    import ctypes
    import win32event
    import win32api
    import winerror
    import winreg
    import servicemanager
    from clouddeskagent.wg_quick_win import *

    # Windows-specific constants
    ELEVATED_FLAG = "--elevated"
    CREATE_NO_WINDOW = getattr(subprocess, "CREATE_NO_WINDOW", 0)


lock = Lock()
sessions = {}  # { session_id: { "config": Path, "interface": str } }
CURRENT_VERSION = "_VERSION_"

# === Logging setup ===
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] %(levelname)s - %(message)s",
    # filename="logs.txt",
    # filemode="a",
    handlers=[logging.StreamHandler()],
)

# === Load Config ===
CONFIG_DIR = os.path.expanduser("~/.clouddeskagent")
CONFIG_PATH = os.path.join(CONFIG_DIR, "config.json")
SESSION_DIR = os.path.join(CONFIG_DIR, "sessions")
os.makedirs(CONFIG_DIR, exist_ok=True)
os.makedirs(SESSION_DIR, exist_ok=True)

if os.path.exists(CONFIG_PATH):
    # Config exists, load it and reset the token_issued flag for the new session.
    with open(CONFIG_PATH, 'r+') as f:
        config_data = json.load(f)
        if config_data.get("token_issued", False):
            config_data["token_issued"] = False
            f.seek(0)
            json.dump(config_data, f, indent=2)
            f.truncate()
            logging.info("Reset one-time auth token flag on startup.")
else:
    import secrets

    # Generate secure random auth token
    auth_token = secrets.token_urlsafe(32)

    private_key = x25519.X25519PrivateKey.generate()
    public_key = private_key.public_key().public_bytes(
        encoding=serialization.Encoding.Raw, format=serialization.PublicFormat.Raw
    )

    with open(CONFIG_PATH, "w") as f:
        json.dump(
            {
                "auth_token": auth_token,
                "private_key": base64.b64encode(
                    private_key.private_bytes(
                        encoding=serialization.Encoding.Raw,
                        format=serialization.PrivateFormat.Raw,
                        encryption_algorithm=serialization.NoEncryption(),
                    )
                ).decode(),
                "public_key": base64.b64encode(public_key).decode(),
                "token_issued": False # One-time token dispenser flag
            },
            f,
            indent=2
        )

    logging.info(f"Generated new configuration with secure auth token")
    logging.info(f"Auth token: {auth_token}")
    logging.info("Save this token - you'll need it to connect from the web portal")

with open(CONFIG_PATH) as f:
    config = json.load(f)
AUTH_TOKEN = config["auth_token"]
PRIVATE_KEY = base64.b64decode(config["private_key"])
PUBLIC_KEY = config["public_key"]

check_dependencies()

# Track application start time for uptime calculation
start_time = time.time()

# === Flask HTTP server ===
app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}}, allow_headers=["Content-Type", "X-Auth-Token", "X-Session-ID"])

sessions = {}  # session_id -> {"vpn": Popen, "rdp": Popen, "config": str}
lock = threading.Lock()

# Logo path - using the one from lib.py
LOGO_PATH = "logo.png"


@app.route("/traylogo/md5", methods=["GET"])
def traylogo_md5():
    """Return the MD5 checksum of the tray icon logo."""
    md5 = get_logo_md5()
    if md5 is None:
        return jsonify({"error": "Logo not found"}), 404
    return jsonify({"md5": md5})


@app.route("/version", methods=["GET"])
def version():
    """Return the current version"""
    return jsonify({"version": CURRENT_VERSION})


@app.route("/status", methods=["GET"])
def status():
    """
    Return the current status and health information of the CloudDeskAgent.
    Includes version, active sessions, system info, and service status.
    """
    try:
        # Get active sessions count
        with lock:
            active_sessions = len(sessions)
            session_details = {}
            for session_id, session_data in sessions.items():
                session_details[session_id] = {
                    "interface": session_data.get("interface", "unknown"),
                    "config_exists": os.path.exists(session_data.get("config", "")) if session_data.get("config") else False
                }
        
        # Get system information
        system_info = {
            "platform": platform.system(),
            "architecture": platform.machine(),
            "python_version": platform.python_version()
        }
        
        # Check dependencies status
        deps_status = {}
        try:
            if platform.system() == "Linux":
                # Check WireGuard
                result = subprocess.run(["which", "wg"], capture_output=True, text=True)
                deps_status["wireguard"] = result.returncode == 0
                
                # Check Remmina
                result = subprocess.run(["which", "remmina"], capture_output=True, text=True)
                deps_status["remmina"] = result.returncode == 0
                
            elif platform.system() == "Windows":
                deps_status["wireguard"] = is_wireguard_installed()
                
            elif platform.system() == "Darwin":  # macOS
                # Check WireGuard
                result = subprocess.run(["which", "wg"], capture_output=True, text=True)
                deps_status["wireguard"] = result.returncode == 0
                
        except Exception as e:
            deps_status["error"] = str(e)
        
        status_data = {
            "status": "healthy",
            "version": CURRENT_VERSION,
            "uptime_seconds": int(time.time() - start_time) if 'start_time' in globals() else 0,
            "active_sessions": active_sessions,
            "session_details": session_details,
            "system_info": system_info,
            "dependencies": deps_status,
            "config_dir": CONFIG_DIR,
            "auth_token_issued": config.get("token_issued", False)
        }
        
        return jsonify(status_data)
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e),
            "version": CURRENT_VERSION
        }), 500


@app.route("/traylogo", methods=["GET"])
def traylogo_get():
    """Return the tray icon logo as a PNG file."""
    if not os.path.exists(LOGO_PATH):
        return "Not found", 404
    return send_file(LOGO_PATH, mimetype="image/png")


@app.route("/traylogo", methods=["POST"])
def traylogo_post():
    """
    Accept a new logo PNG file and store it.
    Expects a multipart/form field named 'logo'.
    Returns the new md5.
    """
    file = request.files.get("logo")
    if not file:
        return jsonify({"error": "No file uploaded"}), 400

    # Save to logo.png in current directory
    logo_save_path = "logo.png"
    os.makedirs(os.path.dirname(logo_save_path) if os.path.dirname(logo_save_path) else ".", exist_ok=True)
    file.save(logo_save_path)
    return jsonify({"status": "ok", "md5": get_logo_md5()})


@app.route("/get-auth-token", methods=["GET"])
def get_auth_token_once():
    with lock:
        # Read the config inside the lock to get the latest state
        with open(CONFIG_PATH, 'r') as f:
            current_config = json.load(f)

        if current_config.get("token_issued", False):
            logging.warning("Attempt to access already-issued one-time token.")
            return jsonify({"error": "Auth token has already been issued."}), 403

        # Issue the token and update the config
        token = current_config["auth_token"]
        current_config["token_issued"] = True

        with open(CONFIG_PATH, 'w') as f:
            json.dump(current_config, f, indent=2)
        
        # Update the global config object as well to reflect the change in the running instance
        global config
        config = current_config

        logging.info("One-time auth token has been issued.")
        return jsonify({"auth_token": token})


@app.route("/public-key", methods=["GET"])
def get_public_key():
    token = request.headers.get("X-Auth-Token")
    if token != AUTH_TOKEN:
        logging.warning("Unauthorized public key request.")
        return jsonify({"error": "Unauthorized"}), 401
    return jsonify({"public_key": PUBLIC_KEY})


@app.route("/start", methods=["POST"])
def start_vpn_and_rdp():
    token = request.headers.get("X-Auth-Token")
    if token != AUTH_TOKEN:
        logging.warning("Unauthorized access attempt.")
        return jsonify({"error": "Unauthorized"}), 401

    session_id = request.headers.get("X-Session-ID")
    if not session_id:
        return jsonify({"error": "Missing session ID"}), 400

    data = request.get_json()
    if not data:
        return jsonify({"error": "Missing request body"}), 400

    wg_config = data.get("wg_config")
    rdp_target_ip = data.get("rdp_target_ip")
    rdp_target_port = data.get("rdp_target_port")

    if not wg_config or not rdp_target_ip or not rdp_target_port:
        return jsonify({"error": "Missing WireGuard config or RDP target"}), 400

    logging.info(f"Received start request for session: {session_id}")

    confirmed = confirm_popup(f"VPN + RDP requested for session {session_id}. Continue?")
    if not confirmed:
        logging.info(f"User cancelled session {session_id}")
        return jsonify({"status": "cancelled by user"}), 403

    system = platform.system()
    if system == "Linux":
        return start_linux(wg_config, session_id, rdp_target_ip, rdp_target_port)
    elif system == "Darwin":
        return start_osx(wg_config, session_id, rdp_target_ip, rdp_target_port)
    elif system == "Windows":
        return start_windows(wg_config, session_id, rdp_target_ip, rdp_target_port)
    else:
        raise NotImplementedError()


def start_osx(wg_config, session_id, rdp_target_ip, rdp_target_port):
    script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "wg-quick-py.py"))
    python_exec = sys.executable  # use the current Python interpreter
    vpn_process = None
    rdp_file = os.path.join(SESSION_DIR, f"{session_id}.rdp")

    def wait_for_rdp(ip, port, timeout=30):
        deadline = time.time() + timeout
        while time.time() < deadline:
            try:
                with socket.create_connection((ip, port), timeout=2):
                    return True
            except (socket.timeout, ConnectionRefusedError, OSError):
                time.sleep(1)
        return False

    def cleanup():
        logging.info(vpn_process)
        if vpn_process and vpn_process.poll() is None:
            os.kill(vpn_process.pid, signal.SIGTERM)
        if os.path.exists(rdp_file):
            os.remove(rdp_file)

    try:
        # status = StatusWindow()
        # status.log("Starting VPN tunnel...")

        wg_config_json = json.dumps(wg_config)
        logging.info("Starting VPN tunnel -- %s", wg_config_json)
        vpn_process = subprocess.Popen(
            [
                "sudo",
                python_exec,
                script_path,
                "--config",
                wg_config_json,
                "--session-id",
                session_id,
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        # status.log(f"Waiting for RDP server at {rdp_target_ip}:{rdp_target_port}...")
        logging.info(f"Waiting for RDP server at {rdp_target_ip}:{rdp_target_port}...")
        if not wait_for_rdp(rdp_target_ip, rdp_target_port):
            # status.log("RDP server unreachable. Cleaning up.")
            cleanup()
            return (
                jsonify({"error": "Could not connect to the remote desktop. No route to host."}),
                400,
            )

        # status.log("Launching Microsoft Remote Desktop...")
        with open(rdp_file, "w") as fh:
            fh.write(
                f"""full address:s:{rdp_target_ip}:{rdp_target_port}
username:s:
prompt for credentials:i:1
screen mode id:i:2
use multimon:i:0
desktopwidth:i:1920
desktopheight:i:1080
session bpp:i:32
redirectclipboard:i:1
redirectprinters:i:1
redirectdrives:i:0
audiomode:i:0
authentication level:i:2
"""
            )

        # Paths to possible RDP app binaries
        apps = [
            "/Applications/Microsoft Remote Desktop.app/Contents/MacOS/Microsoft Remote Desktop",
            "/Applications/Windows App.app/Contents/MacOS/Windows App",
        ]

        # Find the installed RDP app
        for app in apps:
            if os.path.exists(app):
                rdp_executable = app
                break
        else:
            return jsonify({"error": "No supported RDP client app found."}), 400

        app_name = rdp_executable.split("/")[-1]
        start_rdp_session(app_name, rdp_file, cleanup)

        # # Try launching directly with the .rdp file (may not work depending on app version)
        # rdp_process = subprocess.Popen([rdp_executable, rdp_file])
        # logging.info(f"Started RDP client '{os.path.basename(rdp_executable)}' with PID {rdp_process.pid}")

        # def monitor_rdp():
        #     rdp_process.wait()
        #     logging.info("RDP client for session %s ended", session_id)
        #     cleanup()
        # threading.Thread(
        #     target=monitor_rdp, daemon=True
        # ).start()
        return jsonify({"status": "started"})
    except Exception as e:
        logging.exception("Failed to start session")
        cleanup()
        return jsonify({"error": str(e)}), 500


def start_linux(wg_config, session_id, rdp_target_ip, rdp_target_port):
    config_path = None
    remmina_session_file = None

    def cleanup():
        logging.info(f"Cleaning up VPN for interface {interface_name}")
        if config_path and os.path.exists(config_path):
            # Use wg-quick down for consistent cleanup
            subprocess.run(["sudo", "wg-quick", "down", config_path], 
                         capture_output=True, text=True)
            os.remove(config_path)
        if remmina_session_file and os.path.exists(remmina_session_file):
            os.remove(remmina_session_file)

    logging.info("Configuring VPN tunnel...")

    interface_address = wg_config.get("interface", {}).get("address")
    peer_publickey = wg_config.get("peer", {}).get("publickey")
    peer_endpoint = wg_config.get("peer", {}).get("endpoint")
    peer_allowedips = wg_config.get("peer", {}).get("allowedips")

    if not all(
        [interface_address, peer_publickey, peer_endpoint, peer_allowedips]
    ):
        missing = []
        if not interface_address: missing.append("interface address")
        if not peer_publickey: missing.append("peer public key")
        if not peer_endpoint: missing.append("peer endpoint")
        if not peer_allowedips: missing.append("allowed IPs")

        error_msg = f"VPN configuration is incomplete. Missing: {', '.join(missing)}. Please check your VDI portal settings."
        logging.error(error_msg)
        return jsonify({"error": error_msg}), 400

    config_path = os.path.join(SESSION_DIR, f"{session_id}.conf")
    with open(config_path, "w") as f:
        f.write("[Interface]\n")
        f.write(f"Address = {interface_address}\n")
        f.write(f"PrivateKey = {base64.b64encode(PRIVATE_KEY).decode()}\n")
        f.write("\n[Peer]\n")
        f.write(f"PublicKey = {peer_publickey}\n")
        f.write(f"Endpoint = {peer_endpoint}\n")
        f.write(f"AllowedIPs = {peer_allowedips}\n")

    logging.info("Starting VPN tunnel...")
    interface_name = f"cda-{session_id[:8]}"
    
    # Use wg-quick for consistent VPN management
    logging.info(f"Starting VPN with wg-quick up {config_path}")
    result = subprocess.run(
        ["sudo", "wg-quick", "up", config_path],
        capture_output=True, text=True
    )
    
    if result.returncode != 0:
        error_msg = f"Failed to start VPN: {result.stderr.strip()}"
        logging.error(error_msg)
        logging.error(f"wg-quick stdout: {result.stdout}")
        cleanup()
        return jsonify({"error": error_msg}), 500

    # Since we are not using a long-running process, we can't use Popen.
    # We will manage the cleanup process differently.
    vpn_process = None # No process to monitor directly

    logging.info(f"VPN started for session {session_id} using interface {interface_name}")

    logging.info("✅ VPN tunnel established! Waiting for remote desktop...")
    if not wait_for_rdp(rdp_target_ip, rdp_target_port):
        logging.info("❌ Remote desktop is not reachable. Cleaning up VPN...")
        cleanup()
        return jsonify({"error": f"VPN tunnel is working, but remote desktop at {rdp_target_ip}:{rdp_target_port} is not responding. The target machine may be offline or RDP service may not be running."}), 400
    logging.info("✅ Remote desktop is reachable! Starting RDP client...")
    remmina_session_file = generate_remmina_profile(
        SESSION_DIR, session_id, rdp_target_ip, rdp_target_port
    )
    remmina_env = dict(os.environ)
    remmina_env["G_MESSAGES_DEBUG"] = "remmina"
    # Launch Remmina - it will communicate with existing instance
    rdp_process = subprocess.Popen(
        ["remmina", remmina_session_file],
        env=remmina_env,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
    )
    
    # Don't monitor the launcher process - it will exit immediately
    # We'll monitor the VPN connection differently
    logging.info(f"RDP client started for session {session_id} using {remmina_session_file}")
    
    # Wait a moment for Remmina to process the connection
    time.sleep(2)

    with lock:
        sessions[session_id] = {
            "vpn_interface": interface_name,
            "rdp": rdp_process,
            "config": config_path,
            "remmina": remmina_session_file,
            "rdp_ip": rdp_target_ip,
            "rdp_port": rdp_target_port,
        }

    def monitor_rdp(sid):
        # Monitor VPN connection by checking if the RDP target is still reachable
        # and if there are active RDP connections
        logging.info(f"Starting VPN monitoring for session {sid}")
        
        while True:
            try:
                # Check if session still exists (might be cleaned up elsewhere)
                with lock:
                    if sid not in sessions:
                        logging.info(f"Session {sid} no longer exists, stopping monitor")
                        return
                
                # Check if RDP target is still reachable through VPN
                rdp_ip = sessions[sid].get("rdp_ip")
                rdp_port = sessions[sid].get("rdp_port")
                
                if rdp_ip and rdp_port:
                    # Try to connect to RDP port
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((rdp_ip, int(rdp_port)))
                    sock.close()
                    
                    if result != 0:
                        logging.info(f"RDP target {rdp_ip}:{rdp_port} no longer reachable, assuming session closed")
                        break
                
                # Check if there are any active RDP connections to our target
                try:
                    netstat_result = subprocess.run(
                        ["netstat", "-tn"], 
                        capture_output=True, text=True, timeout=5
                    )
                    if rdp_ip and f"{rdp_ip}:{rdp_port}" not in netstat_result.stdout:
                        logging.info(f"No active connection to {rdp_ip}:{rdp_port} found")
                        # Wait a bit more to be sure
                        time.sleep(10)
                        # Check again
                        netstat_result2 = subprocess.run(
                            ["netstat", "-tn"], 
                            capture_output=True, text=True, timeout=5
                        )
                        if f"{rdp_ip}:{rdp_port}" not in netstat_result2.stdout:
                            logging.info(f"Confirmed: RDP connection to {rdp_ip}:{rdp_port} is closed")
                            break
                except Exception as e:
                    logging.warning(f"Could not check network connections: {e}")
                
                # Wait before next check
                time.sleep(30)
                
            except Exception as e:
                logging.error(f"Error in RDP monitoring: {e}")
                time.sleep(30)
        
        # Clean up VPN when RDP session ends
        logging.info(f"RDP session ended for {sid}, shutting down VPN")
        result = subprocess.run(["sudo", "wg-quick", "down", sessions[sid]["config"]], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            logging.error(f"Failed to stop VPN: {result.stderr}")
        
        if os.path.exists(sessions[sid]["config"]):
            os.remove(sessions[sid]["config"])
        if os.path.exists(sessions[sid]["remmina"]):
            os.remove(sessions[sid]["remmina"])
        with lock:
            del sessions[sid]

    threading.Thread(target=monitor_rdp, args=(session_id,), daemon=True).start()

    return jsonify({"status": "started"})


def quit_app(icon, item):
    icon.stop()
    with lock:
        system = platform.system()
        for sid, processes in sessions.items():
            config_file = processes["config"]
            interface = processes["interface"]
            logging.info(f"Terminating VPN for session {sid}")

            if system == "Linux" or system == "Darwin":
                subprocess.call(["wg-quick", "down", str(config_file)])
            elif system == "Windows":
                service_name = f"WireGuardTunnel${interface}"
                # Stop the tunnel service
                subprocess.call(["sc", "stop", service_name])
                # Uninstall the tunnel service
                subprocess.call(
                    [
                        str(config_file.parent / "wireguard.exe"),
                        "/uninstalltunnelservice",
                        interface,
                    ]
                )
            else:
                logging.error(f"Unsupported OS: {system}")

            # Remove session config file
            if os.path.exists(config_file):
                os.remove(config_file)

        sessions.clear()
    os._exit(0)


def send_command_to_service(cmd_dict):
    pipe_name = r"\\.\pipe\Global\VPNAdminPipe"
    deadline = time.time() + 10
    while time.time() < deadline:
        try:
            handle = win32file.CreateFile(
                pipe_name,
                win32file.GENERIC_WRITE,
                0,
                None,
                win32file.OPEN_EXISTING,
                0,
                None,
            )
            win32file.WriteFile(handle, json.dumps(cmd_dict).encode("utf-8"))
            win32file.CloseHandle(handle)
            return True
        except pywintypes.error as e:
            if e.winerror in (2, 231):
                time.sleep(0.2)
                continue
            logging.error(f"Failed to communicate with VPNAdminService: {e}")
            return False
    logging.error("Failed to communicate with VPNAdminService: pipe not available after retries.")
    return False


def start_windows(wg_config, session_id, rdp_target_ip, rdp_target_port):

    vpn_process = None
    rdp_file = os.path.join(SESSION_DIR, f"{session_id}.rdp")

    def wait_for_rdp(ip, port, timeout=60):
        deadline = time.time() + timeout
        while time.time() < deadline:
            try:
                with socket.create_connection((ip, port), timeout=2):
                    return True
            except (socket.timeout, ConnectionRefusedError, OSError):
                time.sleep(1)
        return False

    def cleanup():
        logging.info(vpn_process)
        send_command_to_service({"cmd": "uninstall", "config": wg_config, "session_id": session_id})

        # Remove RDP file
        if os.path.exists(rdp_file):
            logging.info(f"Removing RDP file: {rdp_file}")
            os.remove(rdp_file)

    try:
        wg_config_json = json.dumps(wg_config)
        logging.info("Starting VPN tunnel -- %s", wg_config_json)

        if not send_command_to_service({"cmd": "install", "config": wg_config, "session_id": session_id}):
            return (
                jsonify({"error": "Failed to communicate with VPN Admin Service"}),
                500,
            )

        logging.info(f"Waiting for RDP server at {rdp_target_ip}:{rdp_target_port}...")
        if not wait_for_rdp(rdp_target_ip, rdp_target_port):
            cleanup()
            return (
                jsonify({"error": "Could not connect to the remote desktop. No route to host."}),
                400,
            )

        # Create RDP file
        with open(rdp_file, "w") as fh:
            fh.write(
                f"""full address:s:{rdp_target_ip}:{rdp_target_port}
username:s:
prompt for credentials:i:1
screen mode id:i:2
use multimon:i:0
desktopwidth:i:1920
desktopheight:i:1080
session bpp:i:32
redirectclipboard:i:1
redirectprinters:i:1
redirectdrives:i:0
audiomode:i:0
authentication level:i:2
"""
            )

        # Launch Microsoft Remote Desktop (mstsc.exe)
        mstsc_path = os.path.join(os.environ["SystemRoot"], "System32", "mstsc.exe")
        if not os.path.exists(mstsc_path):
            return (
                jsonify({"error": "mstsc.exe (Remote Desktop Client) not found."}),
                400,
            )

        logging.info(f"Launching mstsc.exe with RDP file {rdp_file}")
        rdp_process = subprocess.Popen([mstsc_path, rdp_file])

        def monitor_rdp():
            rdp_process.wait()
            logging.info("RDP session ended, cleaning up VPN tunnel...")
            cleanup()

        threading.Thread(target=monitor_rdp, daemon=True).start()

        return jsonify({"status": "started"})
    except Exception as e:
        logging.exception("Failed to start session")
        cleanup()
        return jsonify({"error": str(e)}), 500


def run_server():
    server = make_server("127.0.0.1", 8765, app)
    server.serve_forever()


def is_service_running(service_name):
    try:
        status = win32serviceutil.QueryServiceStatus(service_name)[1]
        return status == 4  # 4 = SERVICE_RUNNING
    except pywintypes.error as e:
        if e.winerror == 1060:
            return False
        raise


def enforce_single_instance(mutex_name="Global\\CloudDeskAgentMutex"):
    handle = win32event.CreateMutex(None, False, mutex_name)
    last_error = win32api.GetLastError()
    if last_error == winerror.ERROR_ALREADY_EXISTS:
        logging.info("Another instance is already running")
        sys.exit(0)


# Create/update the Windows service to point to THIS EXE with --service, and start it.
def install_self_as_service() -> bool:
    name = "VPNAdminService"
    display = "VPN Admin Tunnel Service"
    desc = "Manages WireGuard tunnels and named pipe commands for CloudDeskAgent."
    exe = sys.executable
    bin_path = f'"{exe}" --service'

    # Try create
    r = subprocess.run(
        [
            "sc.exe",
            "create",
            name,
            "binPath=",
            bin_path,
            "start=",
            "auto",
            "DisplayName=",
            f'"{display}"',
        ],
        capture_output=True,
        text=True,
        creationflags=CREATE_NO_WINDOW,
    )
    if r.returncode != 0 and "already exists" not in (r.stdout + r.stderr).lower():
        logging.error(f"sc create failed: {r.stderr or r.stdout}")
        return False
    if r.returncode != 0:  # already exists -> reconfig
        r2 = subprocess.run(
            ["sc.exe", "config", name, "binPath=", bin_path, "start=", "auto"],
            capture_output=True,
            text=True,
            creationflags=CREATE_NO_WINDOW,
        )
        if r2.returncode != 0:
            logging.error(f"sc config failed: {r2.stderr or r2.stdout}")
            return False

    # Set description (best effort)
    subprocess.run(
        ["sc.exe", "description", name, desc],
        capture_output=True,
        text=True,
        creationflags=CREATE_NO_WINDOW,
    )

    # Delayed Auto Start
    try:
        with winreg.OpenKey(
            winreg.HKEY_LOCAL_MACHINE,
            rf"SYSTEM\CurrentControlSet\Services\{name}",
            0,
            winreg.KEY_SET_VALUE,
        ) as k:
            winreg.SetValueEx(k, "DelayedAutoStart", 0, winreg.REG_DWORD, 1)
    except Exception as e:
        logging.warning(f"Could not set DelayedAutoStart: {e}")

    # Start (idempotent)
    r = subprocess.run(
        ["sc.exe", "start", name],
        capture_output=True,
        text=True,
        creationflags=CREATE_NO_WINDOW,
    )
    if r.returncode != 0 and "already" not in (r.stdout + r.stderr).lower():
        logging.warning(f"sc start returned {r.returncode}: {r.stderr or r.stdout}")
    set_service_autostart(name, delayed=True)
    logging.info("Service installed/updated and start requested.")
    return True


# Service runtime entrypoint (same EXE). SCM launches us with --service.
def run_service_host():
    # Host the ServiceFramework defined in clouddeskagent.wg_quick_win
    servicemanager.Initialize()
    servicemanager.PrepareToHostSingle(VPNAdminService)  # imported via 'from clouddeskagent.wg_quick_win import *'
    servicemanager.StartServiceCtrlDispatcher()


# Kick service install/run in the background; parent (tray+HTTP) keeps running.
def ensure_service_async():
    def _worker():
        try:
            ok = ensure_vpn_service_running()  # this should elevate with ShellExecuteW(..., "--install-service", ...)
            if ok:
                logging.info("VPNAdminService is running.")
            else:
                logging.error("Failed to ensure VPNAdminService is running. Check logs.")
        except Exception:
            logging.exception("ensure_vpn_service_running crashed")

    threading.Thread(target=_worker, daemon=True).start()


import os, sys, ctypes, subprocess, tempfile, shutil, time

CREATE_NO_WINDOW = getattr(subprocess, "CREATE_NO_WINDOW", 0)


def _resource_path(rel):
    base = getattr(sys, "_MEIPASS", os.path.abspath("."))
    return os.path.join(base, rel)


def is_win_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except Exception:
        return False


def _find_wireguard_exe():
    # Check all common targets (system-wide and per-user)
    candidates = [
        r"C:\Program Files\WireGuard\wireguard.exe",
        r"C:\Program Files (x86)\WireGuard\wireguard.exe",
        os.path.join(os.environ.get("LOCALAPPDATA", ""), "WireGuard", "wireguard.exe"),
    ]
    for p in candidates:
        if p and os.path.exists(p):
            return p
    return None


def is_wireguard_installed():
    return _find_wireguard_exe() is not None


def _wait_wg_installation(timeout=30):
    wg_exe = r"C:\Program Files\WireGuard\wireguard.exe"
    deadline = time.time() + timeout
    while time.time() < deadline:
        if os.path.exists(wg_exe):
            return wg_exe
        time.sleep(0.5)
    raise RuntimeError("WireGuard installed but executable not found after waiting.")


def install_wireguard_silent():
    # 1) Find bundled MSI
    src_msi = _resource_path(r"bin/windows/wireguard-amd64-0.5.3.msi")
    if not os.path.isfile(src_msi):
        raise FileNotFoundError(f"Bundled WireGuard MSI not found at: {src_msi}")

    # 2) Copy to a clean temp dir (avoids funky paths/permissions)
    tmpdir = tempfile.mkdtemp(prefix="wgmsi_")
    msi = os.path.join(tmpdir, "wireguard.msi")
    shutil.copy2(src_msi, msi)

    # 3) Prepare a valid log path (and pre-create the file)
    log_path = os.path.join(tmpdir, "WireGuardInstall.log")
    open(log_path, "w").close()  # ensure it exists and is writable

    # 4) Build msiexec args
    args = [
        "msiexec.exe",
        "/i",
        msi,
        "/qn",
        "/norestart",
        "DO_NOT_LAUNCH=1",
        "/L*V",
        log_path,
    ]

    # 5) Run (we expect to already be elevated inside --install-service)
    if not is_win_admin():
        # Last-resort elevation (shouldn't happen if you call from --install-service)
        rc = ctypes.windll.shell32.ShellExecuteW(
            None,
            "runas",
            "msiexec.exe",
            f'/i "{msi}" /qn /norestart DO_NOT_LAUNCH=1 /L*V "{log_path}"',
            None,
            1,
        )
        if rc <= 32:
            raise RuntimeError(f"UAC elevation for WireGuard install failed (rc={rc}).")
        # We can’t wait on ShellExecute; poll for the file instead
        _wait_wg_installation()
        return True

    # Direct, blocking call (preferred)
    r = subprocess.run(args, capture_output=True, text=True, creationflags=CREATE_NO_WINDOW)
    # Accept 0 (success) and 3010 (success, reboot required)
    if r.returncode not in (0, 3010):
        # Surface the log location to diagnose
        raise RuntimeError(f"WireGuard MSI install failed: code {r.returncode}. See {log_path}")

    _wait_wg_installation()

    # Optional: install the background manager service quietly
    try:
        subprocess.run(
            [r"C:\Program Files\WireGuard\wireguard.exe", "/installmanagerservice"],
            capture_output=True,
            text=True,
            creationflags=CREATE_NO_WINDOW,
        )
    except Exception:
        pass
    return True


def main():
    logging.info("Booting CloudDeskAgent...")
    service_name = "VPNAdminService"

    if platform.system() == "Windows":
        enforce_single_instance()

        # Register autostart once
        marker = os.path.expanduser("~/.clouddeskagent/.autostart_set")
        if not os.path.exists(marker):
            try:
                add_shortcut_to_startup("CloudDeskAgent")
                os.makedirs(os.path.dirname(marker), exist_ok=True)
                with open(marker, "w") as f:
                    f.write("ok\n")
                logging.info("Auto-start registered (Windows).")
            except Exception:
                logging.exception("Failed to set auto-start")

        # Start HTTP + Tray immediately so the agent is responsive
        if not is_service_running(service_name):
            logging.info("VPNAdminService is not running; ensuring it in background...")
            ensure_service_async()
        else:
            logging.info("VPNAdminService already running.")
        start_tray_server()

    else:
        # Non-Windows flows (Linux/macOS)
        start_tray_server()


def start_tray_server():
    threading.Thread(target=run_server, daemon=True).start()
    run_tray(quit_app)


# =========================
# __main__ dispatch
# =========================
if __name__ == "__main__":
    # 1) Service runtime: SCM will launch THIS EXE with --service. No tray/UI here.
    if "--service" in sys.argv:
        run_service_host()
        sys.exit(0)

    # 2) Elevated installer helper: we run THIS EXE with --install-service to create/start the service, then EXIT.
    if "--install-service" in sys.argv:
        if not is_wireguard_installed():
            logging.info("WireGuard not found. Installing silently...")
            install_wireguard_silent()  # <— runs msiexec directly (no elevation), waits
            _wait_wg_installation()  # <— wait for wireguard.exe to appear

        ok = install_self_as_service()
        sys.exit(0 if ok else 1)

    main()
