workflow:
  rules:
    - if: '$CI_COMMIT_TAG'                 # create pipelines for tags
    - if: '$CI_PIPELINE_SOURCE == "push"'  # create pipelines for branch pushes
    - when: never                          # avoid MR/event duplicates

stages:
  - code_checks
  - build
  - upload_to_qa_minio
  - upload_to_prd_minio

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  WINEDEBUG: "-all"
  APP_NAME: "vdi-agent"


license_check:
  stage: code_checks
  except:
    refs:
      - master
  image: openvcloud/pythoncheck:latest
  before_script: []
  script:
    - /opt/license/checker.sh

black:
  stage: code_checks
  image: ghub.gig.tech/ovcimages/openvcloud/pythoncheck
  script:
    - blackcheck -l 120
  except:
    refs:
      - master
  
# Build on every push (and tags). No MR-specific pipeline anymore.
build_windows_exe:
  stage: build
  image: tobix/pywine:3.12
  before_script:
    - apt-get update
    - apt-get install -y --no-install-recommends git ca-certificates
    - chmod +x ./build-win.sh
  cache:
    key: pip-cache
    paths:
      - .cache/pip
  script:
    - wine python -m pip install --upgrade pip
    - ./build-win.sh
  artifacts:
    when: always
    expire_in: 1 week
    paths:
      - dist/*.exe

# Shared deploy template (manual, shows only on TAG pipelines)
.deploy_template: &deploy
  image:
    name: minio/mc:latest                 # <- entrypoint override goes INSIDE image
    entrypoint: [""]                      # run /bin/sh, not `mc` as entrypoint
  when: manual
  needs:
    - job: build_windows_exe
      artifacts: true
  rules:
    - if: '$CI_COMMIT_TAG'                # deploy jobs appear only on tag pipelines
  variables:
    MINIO_BUCKET: "static"                # your bucket
  before_script:
    - mc --version
    - mc alias set minio "$MINIO_URL" "$MINIO_ACCESS_KEY" "$MINIO_ACCESS_SECRET"
  script:
    - VERSION="$CI_COMMIT_TAG"
    - FILE="dist/${APP_NAME}_${VERSION}_windows.exe"
    - 'test -f "$FILE" || { echo "Missing $FILE"; ls -la dist; exit 2; }'
    - sha256sum "$FILE" | tee "${FILE}.sha256"
    - TARGET="minio/${MINIO_BUCKET}/clouddeskagent/windows/"
    - echo "Uploading to ${TARGET}"
    - mc cp "$FILE" "${TARGET}"
    - mc cp "${FILE}.sha256" "${TARGET}${APP_NAME}_${VERSION}_windows.exe.sha256"
    - mc cp "$FILE" "${TARGET}${APP_NAME}_latest_windows.exe"

upload_to_qa_minio:
  <<: *deploy
  stage: upload_to_qa_minio
  environment:
    name: qas
    url: https://meneja-qa.gig.tech
  variables:
    MINIO_URL: $qas_K8S_SECRET_MINIO_URL
    MINIO_ACCESS_KEY: $qas_K8S_SECRET_MINIO_ACCESS_KEY
    MINIO_ACCESS_SECRET: $qas_K8S_SECRET_MINIO_ACCESS_SECRET

upload_to_prd_minio:
  <<: *deploy
  stage: upload_to_prd_minio
  environment:
    name: prd
    url: https://meneja.gig.tech
  variables:
    MINIO_URL: $prd_K8S_SECRET_MINIO_URL
    MINIO_ACCESS_KEY: $prd_K8S_SECRET_MINIO_ACCESS_KEY
    MINIO_ACCESS_SECRET: $prd_K8S_SECRET_MINIO_ACCESS_SECRET
