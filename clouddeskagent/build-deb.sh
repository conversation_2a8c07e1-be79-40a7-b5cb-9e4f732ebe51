#!/bin/bash
# CloudDeskAgent Ubuntu DEB Package Builder
# Version: 1.1.0
# Description: Builds a production-ready DEB package for Ubuntu/Debian systems.
# Supports building for different architectures and cleaning build artifacts.

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# --- Configuration ---
readonly APP_NAME="clouddeskagent"
readonly APP_VERSION="1.0.0"
readonly APP_DESCRIPTION="CloudDeskAgent - Virtual Desktop Interface Agent"
readonly MAINTAINER="GIG.TECH NV <<EMAIL>>"
readonly HOMEPAGE="https://github.com/HaSanAlkholy/agent"

# --- Build Directories ---
readonly BUILD_DIR="build-deb"
readonly DEB_DIR="$BUILD_DIR/DEBIAN"
readonly APP_DIR="$BUILD_DIR/opt/clouddeskagent"
readonly BIN_DIR="$BUILD_DIR/usr/local/bin"
readonly DESKTOP_DIR="$BUILD_DIR/usr/share/applications"
readonly ICON_DIR="$BUILD_DIR/usr/share/icons/hicolor/48x48/apps"

# --- Colors for Output ---
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# --- Logging Functions ---
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }

# --- Error Handling ---
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Build script failed with exit code $exit_code"
    fi
}
trap cleanup EXIT

# --- Core Functions ---

usage() {
    echo "CloudDeskAgent DEB Builder v1.1.0"
    echo
    echo "Usage: $0 [ARCHITECTURE|COMMAND]"
    echo
    echo "Architectures:"
    echo "  amd64     Build for x86_64 (Intel/AMD 64-bit)"
    echo "  arm64     Build for ARM 64-bit (Apple Silicon, etc.)"
    echo "  armhf     Build for ARM 32-bit (Raspberry Pi, etc.)"
    echo "  --all     Build for all supported architectures"
    echo
    echo "Commands:"
    echo "  clean     Remove all build artifacts"
    echo "  help      Show this help message"
    echo
    echo "Examples:"
    echo "  $0                # Build for current host architecture"
    echo "  $0 amd64          # Build only for amd64"
    echo "  $0 --all          # Build for all architectures"
}

clean() {
    log_info "Cleaning up build artifacts..."
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        log_success "Removed build directory: $BUILD_DIR"
    fi
    local packages_found=$(ls ${APP_NAME}_*.deb 2>/dev/null | wc -l)
    if [ "$packages_found" -ne 0 ]; then
        rm -f ${APP_NAME}_*.deb
        log_success "Removed generated DEB packages."
    fi
    log_info "Cleanup complete."
}

# --- Build Step Functions ---

check_dependencies() {
    log_info "Checking build dependencies..."
    local missing_deps=()
    if ! command -v python3 >/dev/null 2>&1; then missing_deps+=("python3"); fi
    if ! command -v pip3 >/dev/null 2>&1; then missing_deps+=("python3-pip"); fi
    if ! command -v dpkg-deb >/dev/null 2>&1; then missing_deps+=("dpkg-dev"); fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Please install them with: sudo apt update && sudo apt install -y ${missing_deps[*]}"
        exit 1
    fi
    log_success "All build dependencies are present."
}

create_directory_structure() {
    log_info "Creating DEB package directory structure..."
    log_info "Cleaning and creating build directory: $BUILD_DIR"
    rm -rf "$BUILD_DIR"
    mkdir -p "$BUILD_DIR"

    log_info "Creating package subdirectories..."
    mkdir -p \
        "$DEB_DIR" \
        "$APP_DIR" \
        "$BIN_DIR" \
        "$DESKTOP_DIR" \
        "$ICON_DIR"
    log_success "Directory structure created."
}

copy_application_files() {
    log_info "Copying application files..."
    cp -r clouddeskagent "$APP_DIR/"
    cp requirements-linux.txt "$APP_DIR/"
    
    cat > "$BIN_DIR/clouddeskagent" << 'EOF'
#!/bin/bash
cd /opt/clouddeskagent
mkdir -p ~/.clouddeskagent/sessions
exec /opt/clouddeskagent/venv/bin/python -m clouddeskagent "$@"
EOF
    chmod +x "$BIN_DIR/clouddeskagent"
    log_success "Application files copied."
}

create_control_file() {
    local arch=$1
    log_info "Creating DEB control file for architecture: $arch"
    cat > "$DEB_DIR/control" << EOF
Package: $APP_NAME
Version: $APP_VERSION
Section: utils
Priority: optional
Architecture: $arch
Maintainer: $MAINTAINER
Homepage: $HOMEPAGE
Depends: python3 (>= 3.8), python3-venv, python3-pip, python3-gi, gir1.2-appindicator3-0.1, wireguard-tools, remmina, python3-tk
Description: $APP_DESCRIPTION
 CloudDeskAgent enables secure VPN connections and RDP sessions
 across Linux systems. It provides a system tray interface for
 managing virtual desktop connections.
EOF
    log_success "Control file created."
}

create_postinst_script() {
    log_info "Creating post-installation script..."
    cat > "$DEB_DIR/postinst" << 'EOF'
#!/bin/bash
set -e
echo "Setting up CloudDeskAgent virtual environment..."
python3 -m venv --system-site-packages /opt/clouddeskagent/venv
echo "Installing Python dependencies..."
/opt/clouddeskagent/venv/bin/pip install -r /opt/clouddeskagent/requirements-linux.txt
echo "Setting up WireGuard permissions..."
echo "%sudo ALL=(ALL) NOPASSWD: /usr/bin/wg-quick" > /etc/sudoers.d/clouddeskagent-wg
chmod 440 /etc/sudoers.d/clouddeskagent-wg
gtk-update-icon-cache -q -t -f /usr/share/icons/hicolor || echo "Could not update icon cache, but installation will continue."

echo "CloudDeskAgent installation completed successfully!"
exit 0
EOF
    chmod +x "$DEB_DIR/postinst"
    log_success "Post-installation script created."
}

create_prerm_script() {
    log_info "Creating pre-removal script..."
    cat > "$DEB_DIR/prerm" << 'EOF'
#!/bin/bash
set -e
if [ -f /etc/sudoers.d/clouddeskagent-wg ]; then
    rm -f /etc/sudoers.d/clouddeskagent-wg
fi
exit 0
EOF
    chmod +x "$DEB_DIR/prerm"
    log_success "Pre-removal script created."
}

create_desktop_entry() {
    log_info "Creating desktop entry..."
    cat > "$DESKTOP_DIR/clouddeskagent.desktop" << EOF
[Desktop Entry]
Name=CloudDesk Agent
Comment=Virtual Desktop Interface Agent - Secure VPN and RDP connections
Exec=/usr/local/bin/clouddeskagent
Icon=/usr/share/icons/hicolor/48x48/apps/clouddeskagent.png
Terminal=false
Type=Application
Categories=Network;RemoteAccess;
StartupNotify=true
EOF
    # Copy the logo.png from clouddeskagent directory as the desktop icon
    cp -f clouddeskagent/appMenu.png "$ICON_DIR/clouddeskagent.png"
    log_success "Desktop entry and icon created."
}

build_deb_package() {
    local arch=$1
    local package_name="${APP_NAME}_${APP_VERSION}_${arch}.deb"
    log_info "Building DEB package: $package_name"
    dpkg-deb --build "$BUILD_DIR" "$package_name"
    
    if [ -f "$package_name" ]; then
        log_success "DEB package built successfully: $package_name"
        log_info "Package size: $(du -h $package_name | cut -f1)"
    else
        log_error "Failed to build DEB package."
        exit 1
    fi
}

# --- Main Build Process ---

build() {
    local arch=$1
    echo
    log_info "Starting CloudDeskAgent DEB Package Build for $arch"
    echo "=================================================="
    check_dependencies
    create_directory_structure
    copy_application_files
    create_control_file "$arch"
    create_postinst_script
    create_prerm_script
    create_desktop_entry
    build_deb_package "$arch"
    echo
    log_success "Build completed successfully for $arch!"
    echo "=================================================="
}

build_all_architectures() {
    local architectures=("amd64" "arm64" "armhf")
    local built_packages=()
    
    log_info "Building DEB packages for all supported architectures..."
    echo "=========================================================="
    
    for arch in "${architectures[@]}"; do
        log_info "Building for architecture: $arch"
        build "$arch"
        
        # Track built packages
        local package_name="${APP_NAME}_${APP_VERSION}_${arch}.deb"
        if [ -f "$package_name" ]; then
            built_packages+=("$package_name")
        fi
        
        echo
    done
    
    # Summary of all built packages
    echo
    log_success "All architecture builds completed!"
    echo "=========================================="
    log_info "Built packages:"
    for package in "${built_packages[@]}"; do
        echo "  - $package ($(du -h $package | cut -f1))"
    done
    echo
    log_info "To install a package, run: sudo apt install ./<package_name>"
}

# --- Script Entry Point ---

main() {
    CMD=${1:-"$(uname -m)"} # Default to host architecture

    case "$CMD" in
        amd64|x86_64)
            build "amd64"
            ;;
        arm64|aarch64)
            build "arm64"
            ;;
        armhf|armv7l)
            build "armhf"
            ;;
        --all|all)
            build_all_architectures
            ;;
        clean)
            clean
            ;;
        help|-h|--help)
            usage
            ;;
        *)
            log_warning "Defaulting to host architecture..."
            build "$( [[ $(uname -m) == "x86_64" ]] && echo "amd64" || echo "$(uname -m)" )"
            ;;
    esac
}

main "$@"
