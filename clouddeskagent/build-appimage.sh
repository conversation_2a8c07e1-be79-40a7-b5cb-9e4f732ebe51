#!/bin/bash
# CloudDeskAgent Universal AppImage Builder
#
# This script creates a production-ready AppImage that is built for maximum
# compatibility. It uses a portable, glibc-based Python build to ensure
# that C-extensions can be compiled and that the final app runs on most systems.

set -euo pipefail # Exit on error, on undefined variable, or pipe failure.

# --- Configuration ---
readonly APP_NAME="CloudDeskAgent"
readonly APP_VERSION="1.0.0"
readonly PYTHON_VERSION="3.9.18"
readonly PYTHON_RELEASE_TAG="20230826"
readonly REQUIREMENTS_FILE="requirements-linux.txt"

# --- Build Directories ---
readonly BUILD_DIR="build"
# The ARCH variable is detected and set automatically below.

# --- Colors for Logging ---
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly BLUE='\033[0;34m'
readonly YELLOW='\033[1;33m'
readonly NC='\033[0m' # No Color

# --- Logging and Error Handling ---
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; } >&2

cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Build script failed with exit code $exit_code."
    fi
    rm -f appimagetool-x86_64.AppImage
}
trap cleanup EXIT

# --- Main Build Process ---
main() {
    echo
    log_info "Starting CloudDeskAgent AppImage Build Process"
    echo "================================================="

    detect_architecture
    readonly APPDIR="$BUILD_DIR/$ARCH/AppDir"
    readonly PYTHON_URL=$(get_python_download_url)

    check_build_dependencies
    clean_previous_build
    create_appdir_structure
    download_and_install_portable_python
    install_agent_dependencies
    copy_agent_files
    create_apprun_launcher
    create_desktop_file_and_icon
    download_appimagetool
    build_the_appimage

    echo
    log_success "Build for $ARCH architecture completed successfully!"
    echo "================================================="
}

# --- Build Functions ---

check_build_dependencies() {
    log_info "Checking for build-time dependencies..."
    if ! command -v curl >/dev/null && ! command -v wget >/dev/null; then
        log_error "Neither 'curl' nor 'wget' were found. Please install one and try again."
        exit 1
    fi
    if ! command -v gcc >/dev/null; then
        log_error "'gcc' command not found. This is required to compile Python packages."
        log_warning "Please install build tools (e.g., 'sudo apt install build-essential') and try again."
        exit 1
    fi
    if [ ! -f "$REQUIREMENTS_FILE" ]; then
        log_error "Requirements file '$REQUIREMENTS_FILE' not found. Please ensure it exists."
        exit 1
    fi
    log_success "All build dependencies found."
}

detect_architecture() {
    log_info "Detecting machine architecture..."
    ARCH=$(uname -m)
    if [[ "$ARCH" == "arm64" ]]; then
        ARCH="aarch64"
    fi
    if [[ "$ARCH" != "x86_64" && "$ARCH" != "aarch64" ]]; then
        log_error "Unsupported architecture: '$ARCH'. This script supports 'x86_64' and 'aarch64'."
        exit 1
    fi
    log_success "Detected architecture: $ARCH"
}

get_python_download_url() {
    case "$ARCH" in
        x86_64)
            echo "https://github.com/indygreg/python-build-standalone/releases/download/${PYTHON_RELEASE_TAG}/cpython-${PYTHON_VERSION}+${PYTHON_RELEASE_TAG}-x86_64-unknown-linux-gnu-install_only.tar.gz"
            ;;
        aarch64)
            echo "https://github.com/indygreg/python-build-standalone/releases/download/${PYTHON_RELEASE_TAG}/cpython-${PYTHON_VERSION}+${PYTHON_RELEASE_TAG}-aarch64-unknown-linux-gnu-install_only.tar.gz"
            ;;
    esac
}

clean_previous_build() {
    log_info "Cleaning previous build directory for $ARCH..."
    rm -rf "$BUILD_DIR/$ARCH"
    rm -f "$APP_NAME-$ARCH.AppImage"
    log_success "Cleaned build directory."
}

create_appdir_structure() {
    log_info "Creating AppDir directory structure..."
    mkdir -p "$APPDIR/opt/agent"
    mkdir -p "$APPDIR/opt/python"
    mkdir -p "$APPDIR/usr/bin"
    mkdir -p "$APPDIR/usr/share/applications"
    mkdir -p "$APPDIR/usr/share/icons/hicolor/256x256/apps"
    log_success "AppDir structure created."
}

download_and_install_portable_python() {
    log_info "Downloading compatible glibc-based Python $PYTHON_VERSION for $ARCH..."
    local python_archive="cpython-${PYTHON_VERSION}-${ARCH}.tar.gz"
    
    if command -v curl >/dev/null; then
        if ! curl --fail --silent --show-error --location "$PYTHON_URL" --output "$python_archive"; then
            log_error "Failed to download Python archive with curl."
            exit 1
        fi
    else
        if ! wget --quiet "$PYTHON_URL" -O "$python_archive"; then
            log_error "Failed to download Python archive with wget."
            exit 1
        fi
    fi

    log_info "Extracting Python..."
    if ! tar -xzf "$python_archive" -C "$APPDIR/opt/python" --strip-components=1; then
        log_error "Failed to extract Python archive."
        exit 1
    fi
    
    rm "$python_archive"
    log_success "Portable Python installed."
}


install_agent_dependencies() {
    log_info "Installing dependencies from '$REQUIREMENTS_FILE' into the AppDir..."
    local python_executable="$APPDIR/opt/python/bin/python3"
    
    # Define the exact site-packages directory where packages must be installed.
    local site_packages_dir="$APPDIR/opt/python/lib/python${PYTHON_VERSION%.*}/site-packages"

    if ! "$python_executable" -m pip install --no-cache-dir --upgrade pip; then
        log_error "Failed to upgrade pip in the AppDir."
        exit 1
    fi

    #
    # === FINAL FIX FOR PIP INSTALL ===
    # The pip error was explicit: we must use the '--target' flag when specifying
    # a platform. This command now tells pip to install the 'manylinux' wheels
    # directly into the bundled Python's site-packages directory.
    #
    if ! "$python_executable" -m pip install \
        --target "$site_packages_dir" \
        --no-cache-dir \
        --platform "manylinux2014_${ARCH}" \
        --python-version "${PYTHON_VERSION%.*}" \
        --only-binary=:all: \
        -r "$REQUIREMENTS_FILE"; then
        log_error "Failed to install dependencies from '$REQUIREMENTS_FILE'."
        exit 1
    fi

    log_success "All Python dependencies are bundled."
}

copy_agent_files() {
    log_info "Copying agent source code..."
    if [ ! -d "clouddeskagent" ]; then
        log_error "Source code directory 'clouddeskagent' not found."
        exit 1
    fi
    cp -r clouddeskagent "$APPDIR/opt/agent/"
    log_success "Agent source code copied."
}

create_apprun_launcher() {
    log_info "Creating AppRun launcher script..."
    #
    # === FINAL FIX ===
    # This version uses an absolute path to the bundled python3 executable.
    # This is the most robust method and directly fixes the "python: not found" error
    # by removing any dependency on the PATH variable for the main execution.
    #
    cat > "$APPDIR/AppRun" << 'EOF'
#!/bin/sh
APPDIR=$(dirname "$(readlink -f "$0")")

# Define the absolute path to the bundled python executable
PYTHON_EXEC="$APPDIR/opt/python/bin/python3"

# Set the Python Path so it can find the 'clouddeskagent' module
export PYTHONPATH="$APPDIR/opt/agent"

# Change to the agent's directory to ensure it can find any local files
cd "$APPDIR/opt/agent/clouddeskagent"

# Execute the application using the explicit, absolute path
exec "$PYTHON_EXEC" -m clouddeskagent "$@"
EOF

    chmod +x "$APPDIR/AppRun"
    log_success "AppRun launcher created."
}

create_desktop_file_and_icon() {
    log_info "Creating .desktop file and installing icon..."
    cat > "$APPDIR/$APP_NAME.desktop" <<EOF
[Desktop Entry]
Name=$APP_NAME
Comment=Virtual Desktop Interface Agent
Exec=AppRun
Icon=$APP_NAME
Terminal=false
Type=Application
Categories=Network;RemoteAccess;
EOF
    if [ -f "CloudDeskAgent.png" ]; then
        cp CloudDeskAgent.png "$APPDIR/"
    else
        log_warning "'CloudDeskAgent.png' not found. The AppImage will have a generic icon."
    fi
    log_success "Desktop entry and icon are set up."
}

download_appimagetool() {
    log_info "Downloading appimagetool..."
    # === CORRECTED URL AS PER YOUR FEEDBACK ===
    local tool_url="https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    
    if [ -f "appimagetool-x86_64.AppImage" ]; then
        log_success "appimagetool already downloaded."
        return
    fi

    if command -v curl >/dev/null; then
         if ! wget -O appimagetool-x86_64.AppImage "$tool_url"; then
            log_error "Failed to download appimagetool with wget."
            exit 1
        fi
    else
        if ! curl --fail --silent --show-error --location "$tool_url" --output appimagetool-x86_64.AppImage; then
            log_error "Failed to download appimagetool with curl."
            exit 1
        fi
    fi

    chmod +x appimagetool-x86_64.AppImage
    log_success "appimagetool is ready."
}

build_the_appimage() {
    log_info "Building the final AppImage for $ARCH..."
    #
    # === CORRECTED COMMAND AS PER YOUR FEEDBACK ===
    # We now explicitly pass the ARCH environment variable to the tool,
    # which resolves the "More than one architectures were found" error.
    #
    if ! ARCH="$ARCH" ./appimagetool-x86_64.AppImage --no-appstream "$APPDIR" "$APP_NAME-$ARCH.AppImage"; then
        log_error "AppImage build failed. Check the output from appimagetool."
        exit 1
    fi
    chmod +x "$APP_NAME-$ARCH.AppImage"
    log_success "AppImage created successfully."
}

# --- Run the main function ---
main