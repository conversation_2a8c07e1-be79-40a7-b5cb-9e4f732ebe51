#!/usr/bin/env bash
set -euo pipefail

# -------- Config --------
APP_NAME="${APP_NAME:-vdi-agent}"

# Windows-style add-data paths (we're producing a Windows exe)
ADD_DATA_ARGS=(
  --add-data "config.json;."
  --add-data "logo.png;."
  --add-data "bin\\windows;bin\\windows"
)

HIDDEN_IMPORTS=(
  --hidden-import pkg_resources.py2_warn
  --hidden-import six
  --hidden-import encodings
)

ENTRYPOINT="clouddeskagent\\__main__.py"
STAMP_FILE="clouddeskagent/__main__.py"

# -------- Version: tag when present, else branch-shortsha --------
if [[ -n "${CI_COMMIT_TAG:-}" ]]; then
  VERSION="${CI_COMMIT_TAG}"
else
  BRANCH="${CI_COMMIT_REF_NAME:-branch}"
  SHORT="${CI_COMMIT_SHORT_SHA:-unknown}"
  # sanitize branch for filenames
  SAFE_BRANCH="${BRANCH//\//-}"
  VERSION="${SAFE_BRANCH}-${SHORT}"
fi
echo "Building ${APP_NAME} version: ${VERSION}"

# -------- Wine prefix --------
export WINEDEBUG="${WINEDEBUG:--all}"
export WINEDLLOVERRIDES="mscoree,mshtml="

if [ -f "/opt/mkuserwineprefix" ]; then
  # pywine helper: creates a user-writable prefix for CI containers
  # shellcheck disable=SC1091
  . /opt/mkuserwineprefix
else
  export WINEARCH=win64
  export WINEPREFIX="${WINEPREFIX:-$PWD/.wine-win64}"
  mkdir -p "$WINEPREFIX"
  wineboot -u
fi

# -------- Ensure Windows Python & PyInstaller --------
if ! wine python -V >/dev/null 2>&1; then
  echo "ERROR: Windows Python not found in Wine environment." >&2
  exit 2
fi

wine python -m pip install --upgrade pip
[ -f requirements.txt ] && wine python -m pip install -r requirements.txt
wine python -m pip install --upgrade pyinstaller

# -------- Version-stamp source (sed _VERSION_) --------
if [ ! -f "$STAMP_FILE" ]; then
  echo "ERROR: $STAMP_FILE not found." >&2
  exit 3
fi
cp "$STAMP_FILE" "${STAMP_FILE}.bak"
# escape sed-sensitive chars in VERSION
ESCAPED_VERSION=$(printf '%s' "$VERSION" | sed -e 's/[\/&]/\\&/g')
sed -i "s/_VERSION_/${ESCAPED_VERSION}/g" "$STAMP_FILE"

# -------- Build --------
rm -rf build dist
echo "Running PyInstaller..."
wine python -m PyInstaller --noconfirm --onefile --name "${APP_NAME}" \
  "${ADD_DATA_ARGS[@]}" \
  "${HIDDEN_IMPORTS[@]}" \
  "${ENTRYPOINT}"

# -------- Restore stamped file --------
if command -v git >/dev/null 2>&1; then
  git checkout -- "$STAMP_FILE" || mv -f "${STAMP_FILE}.bak" "$STAMP_FILE"
else
  mv -f "${STAMP_FILE}.bak" "$STAMP_FILE"
fi

# -------- Artifacts --------
if [ ! -f "dist/${APP_NAME}.exe" ]; then
  echo "ERROR: dist/${APP_NAME}.exe not found after build." >&2
  exit 4
fi

cp "dist/${APP_NAME}.exe" "dist/${APP_NAME}_${VERSION}_windows.exe"
echo "Built:"
echo "  dist/${APP_NAME}.exe"
echo "  dist/${APP_NAME}_${VERSION}_windows.exe"
ls -la dist
