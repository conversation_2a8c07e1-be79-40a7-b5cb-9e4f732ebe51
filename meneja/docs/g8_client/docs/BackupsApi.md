# meneja.lib.clients.g8.lib.BackupsApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**create_backup**](BackupsApi.md#create_backup) | **POST** /backups | Create new backup
[**create_backup_policy**](BackupsApi.md#create_backup_policy) | **POST** /backups/policies | Create new backup policy
[**create_backup_target**](BackupsApi.md#create_backup_target) | **POST** /backups/targets | Create new backup target
[**create_backup_worker**](BackupsApi.md#create_backup_worker) | **POST** /backups/workers | Create backup worker
[**delete_backup**](BackupsApi.md#delete_backup) | **DELETE** /backups/{backup_id} | Delete backup
[**delete_backup_policy**](BackupsApi.md#delete_backup_policy) | **DELETE** /backups/policies/{policy_id} | Delete backup policy
[**delete_backup_target**](BackupsApi.md#delete_backup_target) | **DELETE** /backups/targets/{target_id} | Delete backup target
[**delete_backup_worker**](BackupsApi.md#delete_backup_worker) | **DELETE** /backups/workers/{worker_id} | Delete backup worker
[**delete_target_v_mbackups**](BackupsApi.md#delete_target_v_mbackups) | **DELETE** /backups/targets/{target_id}/vms/{vm_id} | delete vm backups
[**get_backup**](BackupsApi.md#get_backup) | **GET** /backups/{backup_id} | Get backup info
[**get_backup_policy**](BackupsApi.md#get_backup_policy) | **GET** /backups/policies/{policy_id} | Get backup policy info
[**get_backup_target**](BackupsApi.md#get_backup_target) | **GET** /backups/targets/{target_id} | Get backup target info
[**list_backup_policy**](BackupsApi.md#list_backup_policy) | **GET** /backups/policies | List all backup policies
[**list_backup_target**](BackupsApi.md#list_backup_target) | **GET** /backups/targets | List all BackupTargets
[**list_backup_workers**](BackupsApi.md#list_backup_workers) | **GET** /backups/workers | List backup workers
[**list_backups**](BackupsApi.md#list_backups) | **GET** /backups | List all backups
[**list_target_vm_backups**](BackupsApi.md#list_target_vm_backups) | **GET** /backups/targets/{target_id}/vms/{vm_id}/backups | list vm backups
[**list_target_vms**](BackupsApi.md#list_target_vms) | **GET** /backups/targets/{target_id}/vms | list backup target vms
[**sync_target_backups**](BackupsApi.md#sync_target_backups) | **POST** /backups/targets/{target_id}/sync | Sync target backups
[**update_backup_policy**](BackupsApi.md#update_backup_policy) | **PUT** /backups/policies/{policy_id} | Update backup policy info
[**update_backup_target**](BackupsApi.md#update_backup_target) | **PUT** /backups/targets/{target_id} | Update backup target info
[**update_backup_worker**](BackupsApi.md#update_backup_worker) | **PUT** /backups/workers/{worker_id} | Update backup worker


# **create_backup**
> BackupBackupStructGET create_backup(payload, x_fields=x_fields)

Create new backup

Create new backup

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.BackupBackupStructCREATE() # BackupBackupStructCREATE | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Create new backup
    api_response = api_instance.create_backup(payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->create_backup: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**BackupBackupStructCREATE**](BackupBackupStructCREATE.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupBackupStructGET**](BackupBackupStructGET.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_backup_policy**
> BackupBackupPolicyStructGET create_backup_policy(payload, x_fields=x_fields)

Create new backup policy

Create new backup policy

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.BackupBackupPolicyStructCREATE() # BackupBackupPolicyStructCREATE | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Create new backup policy
    api_response = api_instance.create_backup_policy(payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->create_backup_policy: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**BackupBackupPolicyStructCREATE**](BackupBackupPolicyStructCREATE.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupBackupPolicyStructGET**](BackupBackupPolicyStructGET.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_backup_target**
> BackupBackupTargetStructGET create_backup_target(payload, x_fields=x_fields)

Create new backup target

Create new backup target

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.BackupBackupTargetStructCREATE() # BackupBackupTargetStructCREATE | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Create new backup target
    api_response = api_instance.create_backup_target(payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->create_backup_target: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**BackupBackupTargetStructCREATE**](BackupBackupTargetStructCREATE.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupBackupTargetStructGET**](BackupBackupTargetStructGET.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_backup_worker**
> create_backup_worker(payload)

Create backup worker

Create backup worker

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.BackupBackupWorkerStructCREATE() # BackupBackupWorkerStructCREATE | 

try:
    # Create backup worker
    api_instance.create_backup_worker(payload)
except ApiException as e:
    print("Exception when calling BackupsApi->create_backup_worker: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**BackupBackupWorkerStructCREATE**](BackupBackupWorkerStructCREATE.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_backup**
> delete_backup(backup_id)

Delete backup

Delete backup

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
backup_id = 'backup_id_example' # str | 

try:
    # Delete backup
    api_instance.delete_backup(backup_id)
except ApiException as e:
    print("Exception when calling BackupsApi->delete_backup: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **backup_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_backup_policy**
> delete_backup_policy(policy_id)

Delete backup policy

Delete backup policy

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
policy_id = 56 # int | 

try:
    # Delete backup policy
    api_instance.delete_backup_policy(policy_id)
except ApiException as e:
    print("Exception when calling BackupsApi->delete_backup_policy: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **policy_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_backup_target**
> delete_backup_target(target_id)

Delete backup target

Delete backup target

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
target_id = 56 # int | 

try:
    # Delete backup target
    api_instance.delete_backup_target(target_id)
except ApiException as e:
    print("Exception when calling BackupsApi->delete_backup_target: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **target_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_backup_worker**
> delete_backup_worker(worker_id)

Delete backup worker

Delete backup worker

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
worker_id = 56 # int | 

try:
    # Delete backup worker
    api_instance.delete_backup_worker(worker_id)
except ApiException as e:
    print("Exception when calling BackupsApi->delete_backup_worker: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **worker_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_target_v_mbackups**
> delete_target_v_mbackups(target_id, vm_id)

delete vm backups

Delete vm backups

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
target_id = 56 # int | 
vm_id = 56 # int | 

try:
    # delete vm backups
    api_instance.delete_target_v_mbackups(target_id, vm_id)
except ApiException as e:
    print("Exception when calling BackupsApi->delete_target_v_mbackups: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **target_id** | **int**|  | 
 **vm_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_backup**
> BackupBackupStructGET get_backup(backup_id, x_fields=x_fields)

Get backup info

Get backup info

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
backup_id = 'backup_id_example' # str | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get backup info
    api_response = api_instance.get_backup(backup_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->get_backup: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **backup_id** | **str**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupBackupStructGET**](BackupBackupStructGET.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_backup_policy**
> BackupBackupPolicyStructGET get_backup_policy(policy_id, x_fields=x_fields)

Get backup policy info

Get backup policy info

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
policy_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get backup policy info
    api_response = api_instance.get_backup_policy(policy_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->get_backup_policy: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **policy_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupBackupPolicyStructGET**](BackupBackupPolicyStructGET.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_backup_target**
> BackupBackupTargetStructGET get_backup_target(target_id, x_fields=x_fields)

Get backup target info

Get backup target info

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
target_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get backup target info
    api_response = api_instance.get_backup_target(target_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->get_backup_target: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **target_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupBackupTargetStructGET**](BackupBackupTargetStructGET.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_backup_policy**
> BackupPolicyStructs list_backup_policy(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, account_id=account_id, metadata=metadata, x_fields=x_fields)

List all backup policies

List backup policy

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
account_id = 56 # int | account id (optional)
metadata = 'metadata_example' # str | json string of metadata filters (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List all backup policies
    api_response = api_instance.list_backup_policy(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, account_id=account_id, metadata=metadata, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->list_backup_policy: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **account_id** | **int**| account id | [optional] 
 **metadata** | **str**| json string of metadata filters | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupPolicyStructs**](BackupPolicyStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_backup_target**
> BackupTargetStructs list_backup_target(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, account_id=account_id, metadata=metadata, x_fields=x_fields)

List all BackupTargets

List backup target

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
account_id = 56 # int | account id (optional)
metadata = 'metadata_example' # str | json string of metadata filters (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List all BackupTargets
    api_response = api_instance.list_backup_target(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, account_id=account_id, metadata=metadata, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->list_backup_target: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **account_id** | **int**| account id | [optional] 
 **metadata** | **str**| json string of metadata filters | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupTargetStructs**](BackupTargetStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_backup_workers**
> BackupWorkerStructs list_backup_workers(account_id, limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, x_fields=x_fields)

List backup workers

List backup workers

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
account_id = 56 # int | account id
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List backup workers
    api_response = api_instance.list_backup_workers(account_id, limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->list_backup_workers: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **account_id** | **int**| account id | 
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupWorkerStructs**](BackupWorkerStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_backups**
> BackupStructs list_backups(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, location=location, vm_id=vm_id, account_id=account_id, cloudspace_id=cloudspace_id, policy=policy, target_id=target_id, status=status, exclude_expired=exclude_expired, x_fields=x_fields)

List all backups

List backups

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
location = 'location_example' # str | Location name (optional)
vm_id = 56 # int | Only retrun snapshots created for specific virtual machine (optional)
account_id = 56 # int | Account id (optional)
cloudspace_id = 56 # int | Cloudspace id (optional)
policy = 56 # int | Policy id (optional)
target_id = 56 # int | Backup target id (optional)
status = ['status_example'] # list[str] | Backup status (optional)
exclude_expired = false # bool | Exclude expired backups (optional) (default to false)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List all backups
    api_response = api_instance.list_backups(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, location=location, vm_id=vm_id, account_id=account_id, cloudspace_id=cloudspace_id, policy=policy, target_id=target_id, status=status, exclude_expired=exclude_expired, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->list_backups: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **location** | **str**| Location name | [optional] 
 **vm_id** | **int**| Only retrun snapshots created for specific virtual machine | [optional] 
 **account_id** | **int**| Account id | [optional] 
 **cloudspace_id** | **int**| Cloudspace id | [optional] 
 **policy** | **int**| Policy id | [optional] 
 **target_id** | **int**| Backup target id | [optional] 
 **status** | [**list[str]**](str.md)| Backup status | [optional] 
 **exclude_expired** | **bool**| Exclude expired backups | [optional] [default to false]
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupStructs**](BackupStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_target_vm_backups**
> BackupStructs list_target_vm_backups(target_id, vm_id, x_fields=x_fields)

list vm backups

list vm backups

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
target_id = 56 # int | 
vm_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # list vm backups
    api_response = api_instance.list_target_vm_backups(target_id, vm_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->list_target_vm_backups: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **target_id** | **int**|  | 
 **vm_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupStructs**](BackupStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_target_vms**
> BackupVMStructs list_target_vms(target_id, x_fields=x_fields)

list backup target vms

List target vms

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
target_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # list backup target vms
    api_response = api_instance.list_target_vms(target_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling BackupsApi->list_target_vms: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **target_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**BackupVMStructs**](BackupVMStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **sync_target_backups**
> sync_target_backups(target_id)

Sync target backups

Sync target backups

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
target_id = 56 # int | 

try:
    # Sync target backups
    api_instance.sync_target_backups(target_id)
except ApiException as e:
    print("Exception when calling BackupsApi->sync_target_backups: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **target_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_backup_policy**
> update_backup_policy(policy_id, payload)

Update backup policy info

Update backup policy info

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
policy_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.BackupBackupPolicyStructUPDATE() # BackupBackupPolicyStructUPDATE | 

try:
    # Update backup policy info
    api_instance.update_backup_policy(policy_id, payload)
except ApiException as e:
    print("Exception when calling BackupsApi->update_backup_policy: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **policy_id** | **int**|  | 
 **payload** | [**BackupBackupPolicyStructUPDATE**](BackupBackupPolicyStructUPDATE.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_backup_target**
> update_backup_target(target_id, payload)

Update backup target info

Update backup target info

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
target_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.BackupBackupTargetStructUPDATE() # BackupBackupTargetStructUPDATE | 

try:
    # Update backup target info
    api_instance.update_backup_target(target_id, payload)
except ApiException as e:
    print("Exception when calling BackupsApi->update_backup_target: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **target_id** | **int**|  | 
 **payload** | [**BackupBackupTargetStructUPDATE**](BackupBackupTargetStructUPDATE.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_backup_worker**
> update_backup_worker(worker_id, payload)

Update backup worker

Update backup worker

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.BackupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
worker_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.BackupBackupWorkerStructUPDATE() # BackupBackupWorkerStructUPDATE | 

try:
    # Update backup worker
    api_instance.update_backup_worker(worker_id, payload)
except ApiException as e:
    print("Exception when calling BackupsApi->update_backup_worker: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **worker_id** | **int**|  | 
 **payload** | [**BackupBackupWorkerStructUPDATE**](BackupBackupWorkerStructUPDATE.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

