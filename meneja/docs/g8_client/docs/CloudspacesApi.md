# meneja.lib.clients.g8.lib.CloudspacesApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**add_g8_cloudspace_dns_record**](CloudspacesApi.md#add_g8_cloudspace_dns_record) | **POST** /cloudspaces/{cloudspace_id}/dns_records | add cloudspace dns record
[**add_g8_cloudspace_wireguard_interface_config**](CloudspacesApi.md#add_g8_cloudspace_wireguard_interface_config) | **POST** /cloudspaces/{cloudspace_id}/wireguard | Add cloudspace wireguard interface configuration
[**add_g8_cloudspace_wireguard_peer**](CloudspacesApi.md#add_g8_cloudspace_wireguard_peer) | **POST** /cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers | Add cloudspace wireguard peer
[**delete_g8_cloudspace_dns_records**](CloudspacesApi.md#delete_g8_cloudspace_dns_records) | **DELETE** /cloudspaces/{cloudspace_id}/dns_records/{guid} | Delete dns records for cloudspace
[**delete_g8_cloudspace_wireguard_interface**](CloudspacesApi.md#delete_g8_cloudspace_wireguard_interface) | **DELETE** /cloudspaces/{cloudspace_id}/wireguard/{interface_name} | Delete cloudspace wireguard interface
[**delete_g8_cloudspace_wireguard_peer**](CloudspacesApi.md#delete_g8_cloudspace_wireguard_peer) | **DELETE** /cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers/{peer_name} | Delete cloudspace wireguard peer
[**get_g8_cloudspace_by_id**](CloudspacesApi.md#get_g8_cloudspace_by_id) | **GET** /cloudspaces/{cloudspace_id} | Get cloudspace by id
[**get_g8_cloudspace_dns_records**](CloudspacesApi.md#get_g8_cloudspace_dns_records) | **GET** /cloudspaces/{cloudspace_id}/dns_records | Get dns records for cloudspace
[**get_g8_cloudspace_wireguard_config**](CloudspacesApi.md#get_g8_cloudspace_wireguard_config) | **GET** /cloudspaces/{cloudspace_id}/wireguard | Get cloudspace wireguard configuration
[**list_g8_cloudspaces**](CloudspacesApi.md#list_g8_cloudspaces) | **GET** /cloudspaces | List cloudspaces
[**update_g8_cloudspace_dns_record**](CloudspacesApi.md#update_g8_cloudspace_dns_record) | **PUT** /cloudspaces/{cloudspace_id}/dns_records/{guid} | Update cloudspace dns record
[**update_g8_cloudspace_wireguard_interface**](CloudspacesApi.md#update_g8_cloudspace_wireguard_interface) | **PUT** /cloudspaces/{cloudspace_id}/wireguard/{interface_name} | Update cloudspace wireguard interface configuration
[**update_g8_cloudspace_wireguard_peer**](CloudspacesApi.md#update_g8_cloudspace_wireguard_peer) | **PUT** /cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers/{peer_name} | Update cloudspace wireguard peer configuration


# **add_g8_cloudspace_dns_record**
> CloudspacesDNSRecordStruct add_g8_cloudspace_dns_record(cloudspace_id, payload, x_fields=x_fields)

add cloudspace dns record

add cloudspace dns record

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.CloudspacesWireGuardPeerStruct() # CloudspacesWireGuardPeerStruct | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # add cloudspace dns record
    api_response = api_instance.add_g8_cloudspace_dns_record(cloudspace_id, payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CloudspacesApi->add_g8_cloudspace_dns_record: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **payload** | [**CloudspacesWireGuardPeerStruct**](CloudspacesWireGuardPeerStruct.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**CloudspacesDNSRecordStruct**](CloudspacesDNSRecordStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_g8_cloudspace_wireguard_interface_config**
> add_g8_cloudspace_wireguard_interface_config(cloudspace_id, payload)

Add cloudspace wireguard interface configuration

Add cloudspace wireguard interface config

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.CloudspacesWireGuardConfigWriteStruct() # CloudspacesWireGuardConfigWriteStruct | 

try:
    # Add cloudspace wireguard interface configuration
    api_instance.add_g8_cloudspace_wireguard_interface_config(cloudspace_id, payload)
except ApiException as e:
    print("Exception when calling CloudspacesApi->add_g8_cloudspace_wireguard_interface_config: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **payload** | [**CloudspacesWireGuardConfigWriteStruct**](CloudspacesWireGuardConfigWriteStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_g8_cloudspace_wireguard_peer**
> add_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, payload)

Add cloudspace wireguard peer

Add cloudspace wireguard peer

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
interface_name = 'interface_name_example' # str | 
payload = meneja.lib.clients.g8.lib.CloudspacesWireGuardPeerStruct() # CloudspacesWireGuardPeerStruct | 

try:
    # Add cloudspace wireguard peer
    api_instance.add_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, payload)
except ApiException as e:
    print("Exception when calling CloudspacesApi->add_g8_cloudspace_wireguard_peer: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **interface_name** | **str**|  | 
 **payload** | [**CloudspacesWireGuardPeerStruct**](CloudspacesWireGuardPeerStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_g8_cloudspace_dns_records**
> delete_g8_cloudspace_dns_records(cloudspace_id, guid)

Delete dns records for cloudspace

Delete DNS record for cloudsspace

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
guid = 'guid_example' # str | 

try:
    # Delete dns records for cloudspace
    api_instance.delete_g8_cloudspace_dns_records(cloudspace_id, guid)
except ApiException as e:
    print("Exception when calling CloudspacesApi->delete_g8_cloudspace_dns_records: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **guid** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_g8_cloudspace_wireguard_interface**
> delete_g8_cloudspace_wireguard_interface(cloudspace_id, interface_name)

Delete cloudspace wireguard interface

Delete cloudspace wireguard interface config

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
interface_name = 'interface_name_example' # str | 

try:
    # Delete cloudspace wireguard interface
    api_instance.delete_g8_cloudspace_wireguard_interface(cloudspace_id, interface_name)
except ApiException as e:
    print("Exception when calling CloudspacesApi->delete_g8_cloudspace_wireguard_interface: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **interface_name** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_g8_cloudspace_wireguard_peer**
> delete_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, peer_name)

Delete cloudspace wireguard peer

Delete cloudspace wireguard peer

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
interface_name = 'interface_name_example' # str | 
peer_name = 'peer_name_example' # str | 

try:
    # Delete cloudspace wireguard peer
    api_instance.delete_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, peer_name)
except ApiException as e:
    print("Exception when calling CloudspacesApi->delete_g8_cloudspace_wireguard_peer: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **interface_name** | **str**|  | 
 **peer_name** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_cloudspace_by_id**
> CloudspacesCloudspaceDetailsStruct get_g8_cloudspace_by_id(cloudspace_id, x_fields=x_fields)

Get cloudspace by id

Get cloudspace details by id

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get cloudspace by id
    api_response = api_instance.get_g8_cloudspace_by_id(cloudspace_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CloudspacesApi->get_g8_cloudspace_by_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**CloudspacesCloudspaceDetailsStruct**](CloudspacesCloudspaceDetailsStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_cloudspace_dns_records**
> CloudspacesDNSRecordsStruct get_g8_cloudspace_dns_records(cloudspace_id, x_fields=x_fields)

Get dns records for cloudspace

Get DNS records for cloudsspace

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get dns records for cloudspace
    api_response = api_instance.get_g8_cloudspace_dns_records(cloudspace_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CloudspacesApi->get_g8_cloudspace_dns_records: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**CloudspacesDNSRecordsStruct**](CloudspacesDNSRecordsStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_cloudspace_wireguard_config**
> list[CloudspacesWireGuardConfigReadStruct] get_g8_cloudspace_wireguard_config(cloudspace_id, include_status=include_status, x_fields=x_fields)

Get cloudspace wireguard configuration

Get cloudspace wireguard config

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
include_status = true # bool | Include peer status of wireguard (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get cloudspace wireguard configuration
    api_response = api_instance.get_g8_cloudspace_wireguard_config(cloudspace_id, include_status=include_status, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CloudspacesApi->get_g8_cloudspace_wireguard_config: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **include_status** | **bool**| Include peer status of wireguard | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**list[CloudspacesWireGuardConfigReadStruct]**](CloudspacesWireGuardConfigReadStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_cloudspaces**
> CloudspacePaginationModel list_g8_cloudspaces(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, account=account, networks=networks, external_ip=external_ip, status=status, x_fields=x_fields)

List cloudspaces

Lists cloudspaces in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
id = 56 # int | Cloudspaces id filter (optional)
name = 'name_example' # str | Cloudspaces  name filter (optional)
account = 'account_example' # str | Cloudspaces account(id,name) filter (optional)
networks = 'networks_example' # str | Cloudspaces networks(id,subnet) filter (optional)
external_ip = 'external_ip_example' # str | Cloudspaces external_ip(ip,subnet,external_network id,external_network name) filter (optional)
status = 'status_example' # str | Cloudspaces status filter (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List cloudspaces
    api_response = api_instance.list_g8_cloudspaces(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, account=account, networks=networks, external_ip=external_ip, status=status, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CloudspacesApi->list_g8_cloudspaces: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **id** | **int**| Cloudspaces id filter | [optional] 
 **name** | **str**| Cloudspaces  name filter | [optional] 
 **account** | **str**| Cloudspaces account(id,name) filter | [optional] 
 **networks** | **str**| Cloudspaces networks(id,subnet) filter | [optional] 
 **external_ip** | **str**| Cloudspaces external_ip(ip,subnet,external_network id,external_network name) filter | [optional] 
 **status** | **str**| Cloudspaces status filter | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**CloudspacePaginationModel**](CloudspacePaginationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_g8_cloudspace_dns_record**
> update_g8_cloudspace_dns_record(cloudspace_id, guid, payload)

Update cloudspace dns record

Update cloudspace wireguard peer

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
guid = 'guid_example' # str | 
payload = meneja.lib.clients.g8.lib.CloudspacesWireGuardPeerStruct() # CloudspacesWireGuardPeerStruct | 

try:
    # Update cloudspace dns record
    api_instance.update_g8_cloudspace_dns_record(cloudspace_id, guid, payload)
except ApiException as e:
    print("Exception when calling CloudspacesApi->update_g8_cloudspace_dns_record: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **guid** | **str**|  | 
 **payload** | [**CloudspacesWireGuardPeerStruct**](CloudspacesWireGuardPeerStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_g8_cloudspace_wireguard_interface**
> update_g8_cloudspace_wireguard_interface(cloudspace_id, interface_name, payload)

Update cloudspace wireguard interface configuration

Update cloudspace wireguard interface config

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
interface_name = 'interface_name_example' # str | 
payload = meneja.lib.clients.g8.lib.CloudspacesWireGuardConfigWriteStruct() # CloudspacesWireGuardConfigWriteStruct | 

try:
    # Update cloudspace wireguard interface configuration
    api_instance.update_g8_cloudspace_wireguard_interface(cloudspace_id, interface_name, payload)
except ApiException as e:
    print("Exception when calling CloudspacesApi->update_g8_cloudspace_wireguard_interface: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **interface_name** | **str**|  | 
 **payload** | [**CloudspacesWireGuardConfigWriteStruct**](CloudspacesWireGuardConfigWriteStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_g8_cloudspace_wireguard_peer**
> update_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, peer_name, payload)

Update cloudspace wireguard peer configuration

Update cloudspace wireguard peer

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CloudspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cloudspace_id = 56 # int | 
interface_name = 'interface_name_example' # str | 
peer_name = 'peer_name_example' # str | 
payload = meneja.lib.clients.g8.lib.CloudspacesWireGuardPeerStruct() # CloudspacesWireGuardPeerStruct | 

try:
    # Update cloudspace wireguard peer configuration
    api_instance.update_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, peer_name, payload)
except ApiException as e:
    print("Exception when calling CloudspacesApi->update_g8_cloudspace_wireguard_peer: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cloudspace_id** | **int**|  | 
 **interface_name** | **str**|  | 
 **peer_name** | **str**|  | 
 **payload** | [**CloudspacesWireGuardPeerStruct**](CloudspacesWireGuardPeerStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

