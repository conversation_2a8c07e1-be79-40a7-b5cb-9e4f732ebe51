# meneja.lib.clients.g8.lib
RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software

This Python package is automatically generated by the [Swagger Codegen](https://github.com/swagger-api/swagger-codegen) project:

- API version: v4.0
- Package version: 1.0.0
- Build package: io.swagger.codegen.languages.PythonClientCodegen

## Requirements.

Python 2.7 and 3.4+

## Installation & Usage
### pip install

If the python package is hosted on Github, you can install directly from Github

```sh
pip install git+https://github.com//.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com//.git`)

Then import the package:
```python
import meneja.lib.clients.g8.lib 
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import meneja.lib.clients.g8.lib
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.AccountsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
account_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get account consumption
    api_response = api_instance.get_g8_account_consumption(account_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling AccountsApi->get_g8_account_consumption: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *https://localhost/api/1*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*AccountsApi* | [**get_g8_account_consumption**](docs/AccountsApi.md#get_g8_account_consumption) | **GET** /accounts/{account_id}/consumption | Get account consumption
*AccountsApi* | [**get_g8_admin_account_by_id**](docs/AccountsApi.md#get_g8_admin_account_by_id) | **GET** /accounts/{account_id} | Get account by id
*AccountsApi* | [**list_g8_admin_accounts**](docs/AccountsApi.md#list_g8_admin_accounts) | **GET** /accounts | List Accounts
*BackupsApi* | [**create_backup**](docs/BackupsApi.md#create_backup) | **POST** /backups | Create new backup
*BackupsApi* | [**create_backup_policy**](docs/BackupsApi.md#create_backup_policy) | **POST** /backups/policies | Create new backup policy
*BackupsApi* | [**create_backup_target**](docs/BackupsApi.md#create_backup_target) | **POST** /backups/targets | Create new backup target
*BackupsApi* | [**create_backup_worker**](docs/BackupsApi.md#create_backup_worker) | **POST** /backups/workers | Create backup worker
*BackupsApi* | [**delete_backup**](docs/BackupsApi.md#delete_backup) | **DELETE** /backups/{backup_id} | Delete backup
*BackupsApi* | [**delete_backup_policy**](docs/BackupsApi.md#delete_backup_policy) | **DELETE** /backups/policies/{policy_id} | Delete backup policy
*BackupsApi* | [**delete_backup_target**](docs/BackupsApi.md#delete_backup_target) | **DELETE** /backups/targets/{target_id} | Delete backup target
*BackupsApi* | [**delete_backup_worker**](docs/BackupsApi.md#delete_backup_worker) | **DELETE** /backups/workers/{worker_id} | Delete backup worker
*BackupsApi* | [**delete_target_v_mbackups**](docs/BackupsApi.md#delete_target_v_mbackups) | **DELETE** /backups/targets/{target_id}/vms/{vm_id} | delete vm backups
*BackupsApi* | [**get_backup**](docs/BackupsApi.md#get_backup) | **GET** /backups/{backup_id} | Get backup info
*BackupsApi* | [**get_backup_policy**](docs/BackupsApi.md#get_backup_policy) | **GET** /backups/policies/{policy_id} | Get backup policy info
*BackupsApi* | [**get_backup_target**](docs/BackupsApi.md#get_backup_target) | **GET** /backups/targets/{target_id} | Get backup target info
*BackupsApi* | [**list_backup_policy**](docs/BackupsApi.md#list_backup_policy) | **GET** /backups/policies | List all backup policies
*BackupsApi* | [**list_backup_target**](docs/BackupsApi.md#list_backup_target) | **GET** /backups/targets | List all BackupTargets
*BackupsApi* | [**list_backup_workers**](docs/BackupsApi.md#list_backup_workers) | **GET** /backups/workers | List backup workers
*BackupsApi* | [**list_backups**](docs/BackupsApi.md#list_backups) | **GET** /backups | List all backups
*BackupsApi* | [**list_target_vm_backups**](docs/BackupsApi.md#list_target_vm_backups) | **GET** /backups/targets/{target_id}/vms/{vm_id}/backups | list vm backups
*BackupsApi* | [**list_target_vms**](docs/BackupsApi.md#list_target_vms) | **GET** /backups/targets/{target_id}/vms | list backup target vms
*BackupsApi* | [**sync_target_backups**](docs/BackupsApi.md#sync_target_backups) | **POST** /backups/targets/{target_id}/sync | Sync target backups
*BackupsApi* | [**update_backup_policy**](docs/BackupsApi.md#update_backup_policy) | **PUT** /backups/policies/{policy_id} | Update backup policy info
*BackupsApi* | [**update_backup_target**](docs/BackupsApi.md#update_backup_target) | **PUT** /backups/targets/{target_id} | Update backup target info
*BackupsApi* | [**update_backup_worker**](docs/BackupsApi.md#update_backup_worker) | **PUT** /backups/workers/{worker_id} | Update backup worker
*CdromImagesApi* | [**get_g8_cdrom_images**](docs/CdromImagesApi.md#get_g8_cdrom_images) | **GET** /cdrom-images/{cdrom_image_id} | Get CDROM image by id
*CdromImagesApi* | [**list_g8_cdrom_images**](docs/CdromImagesApi.md#list_g8_cdrom_images) | **GET** /cdrom-images | List CDROM images
*CloudspacesApi* | [**add_g8_cloudspace_dns_record**](docs/CloudspacesApi.md#add_g8_cloudspace_dns_record) | **POST** /cloudspaces/{cloudspace_id}/dns_records | add cloudspace dns record
*CloudspacesApi* | [**add_g8_cloudspace_wireguard_interface_config**](docs/CloudspacesApi.md#add_g8_cloudspace_wireguard_interface_config) | **POST** /cloudspaces/{cloudspace_id}/wireguard | Add cloudspace wireguard interface configuration
*CloudspacesApi* | [**add_g8_cloudspace_wireguard_peer**](docs/CloudspacesApi.md#add_g8_cloudspace_wireguard_peer) | **POST** /cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers | Add cloudspace wireguard peer
*CloudspacesApi* | [**delete_g8_cloudspace_dns_records**](docs/CloudspacesApi.md#delete_g8_cloudspace_dns_records) | **DELETE** /cloudspaces/{cloudspace_id}/dns_records/{guid} | Delete dns records for cloudspace
*CloudspacesApi* | [**delete_g8_cloudspace_wireguard_interface**](docs/CloudspacesApi.md#delete_g8_cloudspace_wireguard_interface) | **DELETE** /cloudspaces/{cloudspace_id}/wireguard/{interface_name} | Delete cloudspace wireguard interface
*CloudspacesApi* | [**delete_g8_cloudspace_wireguard_peer**](docs/CloudspacesApi.md#delete_g8_cloudspace_wireguard_peer) | **DELETE** /cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers/{peer_name} | Delete cloudspace wireguard peer
*CloudspacesApi* | [**get_g8_cloudspace_by_id**](docs/CloudspacesApi.md#get_g8_cloudspace_by_id) | **GET** /cloudspaces/{cloudspace_id} | Get cloudspace by id
*CloudspacesApi* | [**get_g8_cloudspace_dns_records**](docs/CloudspacesApi.md#get_g8_cloudspace_dns_records) | **GET** /cloudspaces/{cloudspace_id}/dns_records | Get dns records for cloudspace
*CloudspacesApi* | [**get_g8_cloudspace_wireguard_config**](docs/CloudspacesApi.md#get_g8_cloudspace_wireguard_config) | **GET** /cloudspaces/{cloudspace_id}/wireguard | Get cloudspace wireguard configuration
*CloudspacesApi* | [**list_g8_cloudspaces**](docs/CloudspacesApi.md#list_g8_cloudspaces) | **GET** /cloudspaces | List cloudspaces
*CloudspacesApi* | [**update_g8_cloudspace_dns_record**](docs/CloudspacesApi.md#update_g8_cloudspace_dns_record) | **PUT** /cloudspaces/{cloudspace_id}/dns_records/{guid} | Update cloudspace dns record
*CloudspacesApi* | [**update_g8_cloudspace_wireguard_interface**](docs/CloudspacesApi.md#update_g8_cloudspace_wireguard_interface) | **PUT** /cloudspaces/{cloudspace_id}/wireguard/{interface_name} | Update cloudspace wireguard interface configuration
*CloudspacesApi* | [**update_g8_cloudspace_wireguard_peer**](docs/CloudspacesApi.md#update_g8_cloudspace_wireguard_peer) | **PUT** /cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers/{peer_name} | Update cloudspace wireguard peer configuration
*DefaultApi* | [**get_liveness_check**](docs/DefaultApi.md#get_liveness_check) | **GET** /liveness | API liveness endpoint
*DisksApi* | [**g8_admin_add_disk**](docs/DisksApi.md#g8_admin_add_disk) | **POST** /disks | Create Disk
*DisksApi* | [**g8_admin_delete_disk**](docs/DisksApi.md#g8_admin_delete_disk) | **DELETE** /disks/{disk_id} | Delete disk
*DisksApi* | [**g8_admin_disk_add_cache**](docs/DisksApi.md#g8_admin_disk_add_cache) | **POST** /disks/{disk_id}/cache | Configure cache on disk
*DisksApi* | [**g8_admin_disk_disk_backup_blocksize**](docs/DisksApi.md#g8_admin_disk_disk_backup_blocksize) | **POST** /disks/{disk_id}/backup_block_size | Set disk backup block size
*DisksApi* | [**g8_admin_disk_disk_backup_ratio**](docs/DisksApi.md#g8_admin_disk_disk_backup_ratio) | **POST** /disks/{disk_id}/backup_snapshot_ratio | Set disk backup snapshot ratio
*DisksApi* | [**g8_admin_disk_set_srub**](docs/DisksApi.md#g8_admin_disk_set_srub) | **POST** /disks/{disk_id}/scrub | Configure cache on disk
*DisksApi* | [**g8_admin_get_disk**](docs/DisksApi.md#g8_admin_get_disk) | **GET** /disks/{disk_id} | Get disks
*DisksApi* | [**g8_admin_list_disk**](docs/DisksApi.md#g8_admin_list_disk) | **GET** /disks | List disks
*ErrorsApi* | [**get_g8_error_condition**](docs/ErrorsApi.md#get_g8_error_condition) | **GET** /errors/{traceback_hash} | Get error condition for the traceback
*ErrorsApi* | [**list_g8_error_conditions**](docs/ErrorsApi.md#list_g8_error_conditions) | **GET** /errors/ | List error conditions
*ExternalnetworksApi* | [**add_g8_admin_external_network**](docs/ExternalnetworksApi.md#add_g8_admin_external_network) | **POST** /externalnetworks | Create ExternalNetwork
*ExternalnetworksApi* | [**add_g8_admin_external_network_accounts**](docs/ExternalnetworksApi.md#add_g8_admin_external_network_accounts) | **POST** /externalnetworks/{externalnetwork_id}/accounts | Add accounts to externalnetwork
*ExternalnetworksApi* | [**add_g8_admin_external_network_ips**](docs/ExternalnetworksApi.md#add_g8_admin_external_network_ips) | **POST** /externalnetworks/{externalnetwork_id}/ips | Add external network ips
*ExternalnetworksApi* | [**delete_g8_admin_external_network_by_id**](docs/ExternalnetworksApi.md#delete_g8_admin_external_network_by_id) | **DELETE** /externalnetworks/{externalnetwork_id} | Delete external network by id
*ExternalnetworksApi* | [**get_g8_admin_external_network_by_id**](docs/ExternalnetworksApi.md#get_g8_admin_external_network_by_id) | **GET** /externalnetworks/{externalnetwork_id} | Get external network by id
*ExternalnetworksApi* | [**get_g8_admin_external_network_ips**](docs/ExternalnetworksApi.md#get_g8_admin_external_network_ips) | **GET** /externalnetworks/{externalnetwork_id}/ips | Get external network ips
*ExternalnetworksApi* | [**list_g8_admin_external_networks**](docs/ExternalnetworksApi.md#list_g8_admin_external_networks) | **GET** /externalnetworks | List ExternalNetworks
*ExternalnetworksApi* | [**remove_g8_admin_external_network_accounts**](docs/ExternalnetworksApi.md#remove_g8_admin_external_network_accounts) | **DELETE** /externalnetworks/{externalnetwork_id}/accounts/{account_id} | Add accounts to externalnetwork
*ExternalnetworksApi* | [**remove_g8_admin_external_network_ip**](docs/ExternalnetworksApi.md#remove_g8_admin_external_network_ip) | **DELETE** /externalnetworks/{externalnetwork_id}/ips/{ipaddress} | Remove specific IP from ExternalNetwork
*ExternalnetworksApi* | [**update_g8_admin_external_network**](docs/ExternalnetworksApi.md#update_g8_admin_external_network) | **PATCH** /externalnetworks/{externalnetwork_id} | Update ExternalNetwork
*GpusApi* | [**g8_disable_gpu**](docs/GpusApi.md#g8_disable_gpu) | **DELETE** /gpus/{gpu_id} | Disable GPU
*GpusApi* | [**g8_enable_gpu**](docs/GpusApi.md#g8_enable_gpu) | **POST** /gpus | Enable GPU
*GpusApi* | [**g8_list_gpus**](docs/GpusApi.md#g8_list_gpus) | **GET** /gpus | List all GPUs
*GroupsApi* | [**delete_g8_group_metadata_key_value**](docs/GroupsApi.md#delete_g8_group_metadata_key_value) | **DELETE** /groups/{group_name}/metadata/{key} | Delete metadata key
*GroupsApi* | [**get_g8_group_metadata_key_value**](docs/GroupsApi.md#get_g8_group_metadata_key_value) | **GET** /groups/{group_name}/metadata/{key} | Get metadata value
*GroupsApi* | [**get_group_metadata_settings**](docs/GroupsApi.md#get_group_metadata_settings) | **GET** /groups/{group_name}/settings | Get Settings
*GroupsApi* | [**get_list_group_metadata**](docs/GroupsApi.md#get_list_group_metadata) | **GET** /groups/{group_name}/metadata | List Groups Metadata
*GroupsApi* | [**set_g8_group_metadata_key_value**](docs/GroupsApi.md#set_g8_group_metadata_key_value) | **POST** /groups/{group_name}/metadata/{key} | Set metadata value
*GroupsApi* | [**set_group_metadata_settings**](docs/GroupsApi.md#set_group_metadata_settings) | **PUT** /groups/{group_name}/settings | Set Settings
*LocationsApi* | [**get_g8_location_settings**](docs/LocationsApi.md#get_g8_location_settings) | **GET** /locations/settings | Get Runtime Settings
*LocationsApi* | [**get_get_system_account**](docs/LocationsApi.md#get_get_system_account) | **GET** /locations/system_account | Get System Account
*LocationsApi* | [**set_g8_location_settings**](docs/LocationsApi.md#set_g8_location_settings) | **PUT** /locations/settings | Set Runtime Settings
*NodesApi* | [**add_g8_admin_allowed_os_categories_to_node**](docs/NodesApi.md#add_g8_admin_allowed_os_categories_to_node) | **POST** /nodes/{node_id}/allowed-os-categories | Add allowed os categories to node
*NodesApi* | [**add_g8_admin_disallowed_os_categories_to_node**](docs/NodesApi.md#add_g8_admin_disallowed_os_categories_to_node) | **POST** /nodes/{node_id}/disallowed-os-categories | Add accounts to externalnetwork
*NodesApi* | [**g8_nodes_localstorage_add_disk**](docs/NodesApi.md#g8_nodes_localstorage_add_disk) | **POST** /nodes/localstorage/disks/add | Add disk to localstorage (lvm)
*NodesApi* | [**get_g8_node_by_id**](docs/NodesApi.md#get_g8_node_by_id) | **GET** /nodes/{node_id} | Get node by id
*NodesApi* | [**get_g8_node_power_status**](docs/NodesApi.md#get_g8_node_power_status) | **GET** /nodes/status | Return on/of status for nodes and switches
*NodesApi* | [**list_g8_node_health_checks**](docs/NodesApi.md#list_g8_node_health_checks) | **GET** /nodes/{node_id}/health-checks | List node health checks
*NodesApi* | [**list_g8_nodes**](docs/NodesApi.md#list_g8_nodes) | **GET** /nodes | List nodes
*NodesApi* | [**list_g8_nodes_health_summary**](docs/NodesApi.md#list_g8_nodes_health_summary) | **GET** /nodes/health-checks/summary | List health check summary for all nodes
*NodesApi* | [**mute_g8_node_health_check**](docs/NodesApi.md#mute_g8_node_health_check) | **PATCH** /nodes/{node_id}/health-checks/{health_check_id}/messages/{message_uid}/mute | Mute/un-mute a specific health check
*NodesApi* | [**remove_g8_admin_allowed_os_categories_to_node**](docs/NodesApi.md#remove_g8_admin_allowed_os_categories_to_node) | **DELETE** /nodes/{node_id}/allowed-os-categories/{category} | Remove allowed os categories from node
*NodesApi* | [**remove_g8_admin_disallowed_os_categories_to_node**](docs/NodesApi.md#remove_g8_admin_disallowed_os_categories_to_node) | **DELETE** /nodes/{node_id}/disallowed-os-categories/{category} | Remove disallowed os categories from node
*NodesApi* | [**reset_health_check_failure**](docs/NodesApi.md#reset_health_check_failure) | **POST** /nodes/health-checks/reset-backup-failures | Reset health check failure
*NodesApi* | [**run_all_g8_node_health_checks**](docs/NodesApi.md#run_all_g8_node_health_checks) | **PUT** /nodes/{node_id}/health-checks | Re-run all health checks for a specific node
*NodesApi* | [**run_g8_node_health_check**](docs/NodesApi.md#run_g8_node_health_check) | **PUT** /nodes/{node_id}/health-checks/{health_check_id} | Re-run a health checks for a specific node
*NodesApi* | [**unmute_g8_node_health_check**](docs/NodesApi.md#unmute_g8_node_health_check) | **PATCH** /nodes/{node_id}/health-checks/{health_check_id}/messages/{message_uid}/unmute | Un-mute a specific health check
*ObjectspacesApi* | [**add_bucket_life_cycle_rule**](docs/ObjectspacesApi.md#add_bucket_life_cycle_rule) | **POST** /objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules | Add bucket lifecycle rule
*ObjectspacesApi* | [**cancel_bucket_multipart_uploads**](docs/ObjectspacesApi.md#cancel_bucket_multipart_uploads) | **DELETE** /objectspaces/{objectspace_id}/buckets/{bucket_name}/multipart-uploads/{key}/{upload_id} | Cancel bucket incompelete multipart uploads
*ObjectspacesApi* | [**create_bucket_access**](docs/ObjectspacesApi.md#create_bucket_access) | **POST** /objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management | Create objectspace bucket access
*ObjectspacesApi* | [**delete_bucket_access**](docs/ObjectspacesApi.md#delete_bucket_access) | **DELETE** /objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management/{name} | Delete bucket access
*ObjectspacesApi* | [**delete_bucket_life_cycle_rule**](docs/ObjectspacesApi.md#delete_bucket_life_cycle_rule) | **DELETE** /objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules/{rule_id} | Delete bucket lifecycle rule
*ObjectspacesApi* | [**get_g8_objectspace_by_id**](docs/ObjectspacesApi.md#get_g8_objectspace_by_id) | **GET** /objectspaces/{objectspace_id} | Get objectspace by id
*ObjectspacesApi* | [**list_bucket_access**](docs/ObjectspacesApi.md#list_bucket_access) | **GET** /objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management | List objectspace bucket access
*ObjectspacesApi* | [**list_bucket_life_cycle_rule**](docs/ObjectspacesApi.md#list_bucket_life_cycle_rule) | **GET** /objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules | List bucket lifecycle rules
*ObjectspacesApi* | [**list_bucket_multipart_uploads**](docs/ObjectspacesApi.md#list_bucket_multipart_uploads) | **GET** /objectspaces/{objectspace_id}/buckets/{bucket_name}/multipart-uploads | List bucket incomplete multipart uploads
*ObjectspacesApi* | [**list_g8_objectspaces**](docs/ObjectspacesApi.md#list_g8_objectspaces) | **GET** /objectspaces | List Objectspaces
*ObjectspacesApi* | [**update_bucket_access**](docs/ObjectspacesApi.md#update_bucket_access) | **PUT** /objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management/{name} | Update bucket access type
*SwitchesApi* | [**get_switches_config**](docs/SwitchesApi.md#get_switches_config) | **GET** /switches/configuration | Get switches configuration
*TasksApi* | [**get_task**](docs/TasksApi.md#get_task) | **GET** /tasks/{task_guid} | Get task by guid
*VgpusApi* | [**g8_create_vgpu**](docs/VgpusApi.md#g8_create_vgpu) | **POST** /vgpus | Create VGPU
*VgpusApi* | [**g8_list_gpu_non_admin**](docs/VgpusApi.md#g8_list_gpu_non_admin) | **GET** /vgpus/available | List enabled GPUs for non-admin users
*VgpusApi* | [**g8_list_vgpu**](docs/VgpusApi.md#g8_list_vgpu) | **GET** /vgpus | List VGPU
*VgpusApi* | [**g8_remove_vgpu**](docs/VgpusApi.md#g8_remove_vgpu) | **DELETE** /vgpus/{vgpu_guid} | Remove GPU
*VgpusApi* | [**g8_restore_vgpu**](docs/VgpusApi.md#g8_restore_vgpu) | **POST** /vgpus/{vgpu_guid} | Restore VGPU
*VirtualMachineImagesApi* | [**get_g8_virtual_machine_image_by_id**](docs/VirtualMachineImagesApi.md#get_g8_virtual_machine_image_by_id) | **GET** /virtual-machine-images/{image_id} | Get image by id
*VirtualMachineImagesApi* | [**list_g8_virtual_machine_images**](docs/VirtualMachineImagesApi.md#list_g8_virtual_machine_images) | **GET** /virtual-machine-images | List images
*VirtualMachineImagesApi* | [**list_g8_virtual_machines_by_image_id**](docs/VirtualMachineImagesApi.md#list_g8_virtual_machines_by_image_id) | **GET** /virtual-machine-images/{image_id}/virtual-machines | Get vm list by image id
*VirtualMachinesApi* | [**assign_backup_policy**](docs/VirtualMachinesApi.md#assign_backup_policy) | **POST** /virtual-machines/{vm_id}/backup_policies | Assign backup policy
*VirtualMachinesApi* | [**create_vm_from_backup**](docs/VirtualMachinesApi.md#create_vm_from_backup) | **POST** /virtual-machines/create_from_backup | Create vm from backup
*VirtualMachinesApi* | [**g8_vm_attach_vgpu**](docs/VirtualMachinesApi.md#g8_vm_attach_vgpu) | **POST** /virtual-machines/{vm_id}/vgpus | Attach vgpu
*VirtualMachinesApi* | [**g8_vm_detach_vgpu**](docs/VirtualMachinesApi.md#g8_vm_detach_vgpu) | **DELETE** /virtual-machines/{vm_id}/vgpus/{vgpu_guid} | Detach vgpu
*VirtualMachinesApi* | [**get_g8_admin_vm_by_id**](docs/VirtualMachinesApi.md#get_g8_admin_vm_by_id) | **GET** /virtual-machines/{vm_id} | Get vm by id
*VirtualMachinesApi* | [**get_virtual_machine_placement**](docs/VirtualMachinesApi.md#get_virtual_machine_placement) | **GET** /virtual-machines/placement | Get virtual machine placement
*VirtualMachinesApi* | [**get_vm_restore_progress**](docs/VirtualMachinesApi.md#get_vm_restore_progress) | **GET** /virtual-machines/{vm_id}/restore_progress | Get vm restore progress
*VirtualMachinesApi* | [**list_g8_admin_vmachines**](docs/VirtualMachinesApi.md#list_g8_admin_vmachines) | **GET** /virtual-machines | List Vmachines
*VirtualMachinesApi* | [**unassign_backup_policy**](docs/VirtualMachinesApi.md#unassign_backup_policy) | **DELETE** /virtual-machines/{vm_id}/backup_policies/{policy_id} | Unassign backup policy
*WorkflowsApi* | [**get_g8_task_logs**](docs/WorkflowsApi.md#get_g8_task_logs) | **GET** /workflows/{workflow_id}/task/{task_id}/logs | Lists the logs of a task
*WorkflowsApi* | [**get_g8_workflow**](docs/WorkflowsApi.md#get_g8_workflow) | **GET** /workflows/{workflow_id} | Get workflow by id
*WorkflowsApi* | [**get_g8_workflow_logs**](docs/WorkflowsApi.md#get_g8_workflow_logs) | **GET** /workflows/{workflow_id}/logs | List the logs of a workflow
*WorkflowsApi* | [**list_g8_workflows**](docs/WorkflowsApi.md#list_g8_workflows) | **GET** /workflows | List workflows


## Documentation For Models

 - [AccessStruct](docs/AccessStruct.md)
 - [AccountIds](docs/AccountIds.md)
 - [Accounts](docs/Accounts.md)
 - [AccountsACE](docs/AccountsACE.md)
 - [AccountsACEGroupStruct](docs/AccountsACEGroupStruct.md)
 - [AccountsACEGroupStructAccess](docs/AccountsACEGroupStructAccess.md)
 - [AccountsACEUserStruct](docs/AccountsACEUserStruct.md)
 - [AccountsACEUserStructAccess](docs/AccountsACEUserStructAccess.md)
 - [AccountsAccountConsumption](docs/AccountsAccountConsumption.md)
 - [AccountsAccountDetailsStruct](docs/AccountsAccountDetailsStruct.md)
 - [AccountsAccountDetailsStructLimits](docs/AccountsAccountDetailsStructLimits.md)
 - [AccountsCloudSpace](docs/AccountsCloudSpace.md)
 - [AccountsImage](docs/AccountsImage.md)
 - [AccountsLimits](docs/AccountsLimits.md)
 - [AccountsObjectSpace](docs/AccountsObjectSpace.md)
 - [AccountsPagination](docs/AccountsPagination.md)
 - [AccountsPaginationPagination](docs/AccountsPaginationPagination.md)
 - [AccountsResourceLimits](docs/AccountsResourceLimits.md)
 - [AccountsUnattachedDisk](docs/AccountsUnattachedDisk.md)
 - [Audit](docs/Audit.md)
 - [BackupBackupDiskStructGET](docs/BackupBackupDiskStructGET.md)
 - [BackupBackupDiskStructLIST](docs/BackupBackupDiskStructLIST.md)
 - [BackupBackupImageStructGET](docs/BackupBackupImageStructGET.md)
 - [BackupBackupImageStructLIST](docs/BackupBackupImageStructLIST.md)
 - [BackupBackupNetworkInterfaceStructGET](docs/BackupBackupNetworkInterfaceStructGET.md)
 - [BackupBackupNetworkInterfaceStructGETPciAddress](docs/BackupBackupNetworkInterfaceStructGETPciAddress.md)
 - [BackupBackupNetworkInterfaceStructLIST](docs/BackupBackupNetworkInterfaceStructLIST.md)
 - [BackupBackupPolicyStructCREATE](docs/BackupBackupPolicyStructCREATE.md)
 - [BackupBackupPolicyStructCREATESnapshotPolicy](docs/BackupBackupPolicyStructCREATESnapshotPolicy.md)
 - [BackupBackupPolicyStructGET](docs/BackupBackupPolicyStructGET.md)
 - [BackupBackupPolicyStructLIST](docs/BackupBackupPolicyStructLIST.md)
 - [BackupBackupPolicyStructUPDATE](docs/BackupBackupPolicyStructUPDATE.md)
 - [BackupBackupStructCREATE](docs/BackupBackupStructCREATE.md)
 - [BackupBackupStructGET](docs/BackupBackupStructGET.md)
 - [BackupBackupStructGETVm](docs/BackupBackupStructGETVm.md)
 - [BackupBackupStructLIST](docs/BackupBackupStructLIST.md)
 - [BackupBackupTargetStructCREATE](docs/BackupBackupTargetStructCREATE.md)
 - [BackupBackupTargetStructCREATES3](docs/BackupBackupTargetStructCREATES3.md)
 - [BackupBackupTargetStructGET](docs/BackupBackupTargetStructGET.md)
 - [BackupBackupTargetStructLIST](docs/BackupBackupTargetStructLIST.md)
 - [BackupBackupTargetStructUPDATE](docs/BackupBackupTargetStructUPDATE.md)
 - [BackupBackupVMStructGET](docs/BackupBackupVMStructGET.md)
 - [BackupBackupVMStructLIST](docs/BackupBackupVMStructLIST.md)
 - [BackupBackupWorkerStructCREATE](docs/BackupBackupWorkerStructCREATE.md)
 - [BackupBackupWorkerStructLIST](docs/BackupBackupWorkerStructLIST.md)
 - [BackupBackupWorkerStructUPDATE](docs/BackupBackupWorkerStructUPDATE.md)
 - [BackupPolicyStructs](docs/BackupPolicyStructs.md)
 - [BackupS3StructCREATE](docs/BackupS3StructCREATE.md)
 - [BackupS3StructGET](docs/BackupS3StructGET.md)
 - [BackupS3StructUPDATE](docs/BackupS3StructUPDATE.md)
 - [BackupSnapshotPolicyCREATE](docs/BackupSnapshotPolicyCREATE.md)
 - [BackupSnapshotPolicyGET](docs/BackupSnapshotPolicyGET.md)
 - [BackupSnapshotPolicyUPDATE](docs/BackupSnapshotPolicyUPDATE.md)
 - [BackupStructs](docs/BackupStructs.md)
 - [BackupTargetStructs](docs/BackupTargetStructs.md)
 - [BackupVMPCIAddressGET](docs/BackupVMPCIAddressGET.md)
 - [BackupVMPCIAddressLIST](docs/BackupVMPCIAddressLIST.md)
 - [BackupVMStructs](docs/BackupVMStructs.md)
 - [BackupWorkerStructs](docs/BackupWorkerStructs.md)
 - [CDROMPaginationModel](docs/CDROMPaginationModel.md)
 - [CdromCDROMDetailsStruct](docs/CdromCDROMDetailsStruct.md)
 - [CdromCDROMDetailsStructAccount](docs/CdromCDROMDetailsStructAccount.md)
 - [CdromCDROMDetailsStructStorage](docs/CdromCDROMDetailsStructStorage.md)
 - [CdromCDROMListStruct](docs/CdromCDROMListStruct.md)
 - [CdromStorageStruct](docs/CdromStorageStruct.md)
 - [CloudspacePaginationModel](docs/CloudspacePaginationModel.md)
 - [CloudspacesCloudspaceDetailsStruct](docs/CloudspacesCloudspaceDetailsStruct.md)
 - [CloudspacesCloudspaceDetailsStructAccount](docs/CloudspacesCloudspaceDetailsStructAccount.md)
 - [CloudspacesCloudspaceDetailsStructLimits](docs/CloudspacesCloudspaceDetailsStructLimits.md)
 - [CloudspacesCloudspaceListStruct](docs/CloudspacesCloudspaceListStruct.md)
 - [CloudspacesDNSRecordStruct](docs/CloudspacesDNSRecordStruct.md)
 - [CloudspacesDNSRecordsStruct](docs/CloudspacesDNSRecordsStruct.md)
 - [CloudspacesIpsecRemoteStruct](docs/CloudspacesIpsecRemoteStruct.md)
 - [CloudspacesIpsecTunnelStruct](docs/CloudspacesIpsecTunnelStruct.md)
 - [CloudspacesIpsecTunnelStructRemote](docs/CloudspacesIpsecTunnelStructRemote.md)
 - [CloudspacesNetworkStruct](docs/CloudspacesNetworkStruct.md)
 - [CloudspacesResourceLimitsStruct](docs/CloudspacesResourceLimitsStruct.md)
 - [CloudspacesVMIpStruct](docs/CloudspacesVMIpStruct.md)
 - [CloudspacesVirtualMachineStruct](docs/CloudspacesVirtualMachineStruct.md)
 - [CloudspacesVirtualMachineStructNode](docs/CloudspacesVirtualMachineStructNode.md)
 - [CloudspacesWireGuardAllowedIPStruct](docs/CloudspacesWireGuardAllowedIPStruct.md)
 - [CloudspacesWireGuardConfigReadStruct](docs/CloudspacesWireGuardConfigReadStruct.md)
 - [CloudspacesWireGuardConfigWriteStruct](docs/CloudspacesWireGuardConfigWriteStruct.md)
 - [CloudspacesWireGuardPeerStruct](docs/CloudspacesWireGuardPeerStruct.md)
 - [DeleteInputStruct](docs/DeleteInputStruct.md)
 - [Disk](docs/Disk.md)
 - [DiskACE](docs/DiskACE.md)
 - [DiskCache](docs/DiskCache.md)
 - [DiskDiskCache](docs/DiskDiskCache.md)
 - [DiskDiskVersion](docs/DiskDiskVersion.md)
 - [DiskIoTune](docs/DiskIoTune.md)
 - [DiskSnapshotPolicy](docs/DiskSnapshotPolicy.md)
 - [DisksDiskBackupBlocksizeStruct](docs/DisksDiskBackupBlocksizeStruct.md)
 - [DisksDiskBackupRatioStruct](docs/DisksDiskBackupRatioStruct.md)
 - [DisksDiskCacheStruct](docs/DisksDiskCacheStruct.md)
 - [DisksDiskCreateStruct](docs/DisksDiskCreateStruct.md)
 - [DisksDiskScrubStruct](docs/DisksDiskScrubStruct.md)
 - [DisksPagination](docs/DisksPagination.md)
 - [ErrorLog](docs/ErrorLog.md)
 - [ErrorLogErrorInstance](docs/ErrorLogErrorInstance.md)
 - [ErrorLogFrame](docs/ErrorLogFrame.md)
 - [ErrorLogs](docs/ErrorLogs.md)
 - [ErrorLogsPagination](docs/ErrorLogsPagination.md)
 - [ExternalNetworkDetailsStruct](docs/ExternalNetworkDetailsStruct.md)
 - [ExternalNetworkIpStructs](docs/ExternalNetworkIpStructs.md)
 - [ExternalNetworkListStruct](docs/ExternalNetworkListStruct.md)
 - [ExternalNetworksExternalNetworkCreateStruct](docs/ExternalNetworksExternalNetworkCreateStruct.md)
 - [ExternalNetworksExternalNetworkIpStruct](docs/ExternalNetworksExternalNetworkIpStruct.md)
 - [ExternalNetworksExternalNetworkIpStructLIST](docs/ExternalNetworksExternalNetworkIpStructLIST.md)
 - [ExternalNetworksExternalNetworkIpStructUsage](docs/ExternalNetworksExternalNetworkIpStructUsage.md)
 - [ExternalNetworksExternalNetworkStruct](docs/ExternalNetworksExternalNetworkStruct.md)
 - [ExternalNetworksExternalNetworkUpdateStruct](docs/ExternalNetworksExternalNetworkUpdateStruct.md)
 - [ExternalNetworksPagination](docs/ExternalNetworksPagination.md)
 - [ExternalNetworksUsageResourceStruct](docs/ExternalNetworksUsageResourceStruct.md)
 - [ExternalNetworksUsageResourceStructLIST](docs/ExternalNetworksUsageResourceStructLIST.md)
 - [GPUShortStructs](docs/GPUShortStructs.md)
 - [GPUStructs](docs/GPUStructs.md)
 - [Gpu](docs/Gpu.md)
 - [GpuCreate](docs/GpuCreate.md)
 - [GroupAccessStruct](docs/GroupAccessStruct.md)
 - [GroupMetadata](docs/GroupMetadata.md)
 - [HealthCheck](docs/HealthCheck.md)
 - [HealthCheckMessage](docs/HealthCheckMessage.md)
 - [HealthCheckMessageMuteDetails](docs/HealthCheckMessageMuteDetails.md)
 - [HealthCheckPagination](docs/HealthCheckPagination.md)
 - [ImagesImageDetailsStruct](docs/ImagesImageDetailsStruct.md)
 - [ImagesImageDetailsStructAccountAccess](docs/ImagesImageDetailsStructAccountAccess.md)
 - [ImagesImageDetailsStructOs](docs/ImagesImageDetailsStructOs.md)
 - [ImagesImageDetailsStructVmSpecifications](docs/ImagesImageDetailsStructVmSpecifications.md)
 - [ImagesImagesListStruct](docs/ImagesImagesListStruct.md)
 - [ImagesOsStruct](docs/ImagesOsStruct.md)
 - [ImagesPaginationModel](docs/ImagesPaginationModel.md)
 - [ImagesShortNodeStruct](docs/ImagesShortNodeStruct.md)
 - [ImagesVirtualMachinesListStruct](docs/ImagesVirtualMachinesListStruct.md)
 - [ImagesVirtualMachinesListStructNode](docs/ImagesVirtualMachinesListStructNode.md)
 - [ImagesVmSpecStruct](docs/ImagesVmSpecStruct.md)
 - [InternalPortStruct](docs/InternalPortStruct.md)
 - [InternalPortStructPort](docs/InternalPortStructPort.md)
 - [IpDetails](docs/IpDetails.md)
 - [IpShort](docs/IpShort.md)
 - [IpShortLIST](docs/IpShortLIST.md)
 - [IpShorts](docs/IpShorts.md)
 - [LogLine](docs/LogLine.md)
 - [MetadataKeyValue](docs/MetadataKeyValue.md)
 - [NodeDetails](docs/NodeDetails.md)
 - [NodeDetailsNetAddress](docs/NodeDetailsNetAddress.md)
 - [NodeHealthCheckStatuss](docs/NodeHealthCheckStatuss.md)
 - [Nodes](docs/Nodes.md)
 - [NodesCPUInfo](docs/NodesCPUInfo.md)
 - [NodesCpuinfo](docs/NodesCpuinfo.md)
 - [NodesLocalstorageDiskCreate](docs/NodesLocalstorageDiskCreate.md)
 - [NodesMuteHealthCheckStruct](docs/NodesMuteHealthCheckStruct.md)
 - [NodesNodeHealthCheckStatusLIST](docs/NodesNodeHealthCheckStatusLIST.md)
 - [NodesNodePowerStatusStruct](docs/NodesNodePowerStatusStruct.md)
 - [NodesPagination](docs/NodesPagination.md)
 - [NodesPowerStatusStruct](docs/NodesPowerStatusStruct.md)
 - [NodesSwitchStatusStruct](docs/NodesSwitchStatusStruct.md)
 - [OSCategories](docs/OSCategories.md)
 - [ObjectspacePagination](docs/ObjectspacePagination.md)
 - [ObjectspacesACEGroupStruct](docs/ObjectspacesACEGroupStruct.md)
 - [ObjectspacesACEUserStruct](docs/ObjectspacesACEUserStruct.md)
 - [ObjectspacesAuditInfo](docs/ObjectspacesAuditInfo.md)
 - [ObjectspacesBucketInfo](docs/ObjectspacesBucketInfo.md)
 - [ObjectspacesBucketLifeCycleRule](docs/ObjectspacesBucketLifeCycleRule.md)
 - [ObjectspacesBucketLifeCycleRules](docs/ObjectspacesBucketLifeCycleRules.md)
 - [ObjectspacesBucketMultipartUpload](docs/ObjectspacesBucketMultipartUpload.md)
 - [ObjectspacesBucketMultipartUploads](docs/ObjectspacesBucketMultipartUploads.md)
 - [ObjectspacesCloudspaceInfo](docs/ObjectspacesCloudspaceInfo.md)
 - [ObjectspacesCloudspaceIps](docs/ObjectspacesCloudspaceIps.md)
 - [ObjectspacesObjectSpaceAccessListStruct](docs/ObjectspacesObjectSpaceAccessListStruct.md)
 - [ObjectspacesObjectSpaceAccessRequestStruct](docs/ObjectspacesObjectSpaceAccessRequestStruct.md)
 - [ObjectspacesObjectSpaceAccessStruct](docs/ObjectspacesObjectSpaceAccessStruct.md)
 - [ObjectspacesObjectspaceDetailsStruct](docs/ObjectspacesObjectspaceDetailsStruct.md)
 - [ObjectspacesObjectspaceDetailsStructAccount](docs/ObjectspacesObjectspaceDetailsStructAccount.md)
 - [ObjectspacesObjectspaceStruct](docs/ObjectspacesObjectspaceStruct.md)
 - [ObjectspacesUpdateBucketAccessTypeStruct](docs/ObjectspacesUpdateBucketAccessTypeStruct.md)
 - [Pagination](docs/Pagination.md)
 - [PortForwardStruct](docs/PortForwardStruct.md)
 - [PortForwardStructExternal](docs/PortForwardStructExternal.md)
 - [PortForwardStructInternal](docs/PortForwardStructInternal.md)
 - [PortStruct](docs/PortStruct.md)
 - [RuntimeSettings](docs/RuntimeSettings.md)
 - [ShortInfoStruct](docs/ShortInfoStruct.md)
 - [SwitchesSwitchConfig](docs/SwitchesSwitchConfig.md)
 - [SwitchesSwitchesConfigList](docs/SwitchesSwitchesConfigList.md)
 - [UserAccessStruct](docs/UserAccessStruct.md)
 - [VgpusGPUShortStructLIST](docs/VgpusGPUShortStructLIST.md)
 - [VgpusGPUStructLIST](docs/VgpusGPUStructLIST.md)
 - [VgpusVGPUMachineInfoStruct](docs/VgpusVGPUMachineInfoStruct.md)
 - [VgpusVGPUPropertyStructLIST](docs/VgpusVGPUPropertyStructLIST.md)
 - [VgpusVGPUTypeStructLIST](docs/VgpusVGPUTypeStructLIST.md)
 - [VgpusVgpuCreateStruct](docs/VgpusVgpuCreateStruct.md)
 - [VirtualGPUPagination](docs/VirtualGPUPagination.md)
 - [VirtualGpu](docs/VirtualGpu.md)
 - [VirtualGpuACE](docs/VirtualGpuACE.md)
 - [VirtualGpuVirtualMachine](docs/VirtualGpuVirtualMachine.md)
 - [VirtualMachinePaginationModel](docs/VirtualMachinePaginationModel.md)
 - [VirtualMachinePlacementInfoStruct](docs/VirtualMachinePlacementInfoStruct.md)
 - [VirtualMachinePlacementStruct](docs/VirtualMachinePlacementStruct.md)
 - [VirtualMachinesByNodePlacementStruct](docs/VirtualMachinesByNodePlacementStruct.md)
 - [VirtualMachinesByOsCategoryStruct](docs/VirtualMachinesByOsCategoryStruct.md)
 - [VirtualMachinesByOsStruct](docs/VirtualMachinesByOsStruct.md)
 - [VmachinesCreateVMFromBackupResponseStruct](docs/VmachinesCreateVMFromBackupResponseStruct.md)
 - [VmachinesCreateVMFromBackupStructCREATE](docs/VmachinesCreateVMFromBackupStructCREATE.md)
 - [VmachinesDiskProgress](docs/VmachinesDiskProgress.md)
 - [VmachinesDiskStruct](docs/VmachinesDiskStruct.md)
 - [VmachinesGetVMRestoreProgressStruct](docs/VmachinesGetVMRestoreProgressStruct.md)
 - [VmachinesNicInfo](docs/VmachinesNicInfo.md)
 - [VmachinesPagination](docs/VmachinesPagination.md)
 - [VmachinesTagStruct](docs/VmachinesTagStruct.md)
 - [VmachinesVMBackupPolicy](docs/VmachinesVMBackupPolicy.md)
 - [VmachinesVMBackupPolicyStruct](docs/VmachinesVMBackupPolicyStruct.md)
 - [VmachinesVmDetailsStruct](docs/VmachinesVmDetailsStruct.md)
 - [VmachinesVmDetailsStructAccount](docs/VmachinesVmDetailsStructAccount.md)
 - [VmachinesVmDetailsStructCloudspace](docs/VmachinesVmDetailsStructCloudspace.md)
 - [VmachinesVmDetailsStructImage](docs/VmachinesVmDetailsStructImage.md)
 - [VmachinesVmDetailsStructNode](docs/VmachinesVmDetailsStructNode.md)
 - [VmachinesVmDeviceAttachStruct](docs/VmachinesVmDeviceAttachStruct.md)
 - [VmachinesVmachineDeviceStruct](docs/VmachinesVmachineDeviceStruct.md)
 - [VmachinesVmachineListStruct](docs/VmachinesVmachineListStruct.md)
 - [VmachinesVmachineLoacaionHistoryStruct](docs/VmachinesVmachineLoacaionHistoryStruct.md)
 - [Workflow](docs/Workflow.md)
 - [WorkflowsPagination](docs/WorkflowsPagination.md)
 - [WorkflowsWorkflowStep](docs/WorkflowsWorkflowStep.md)


## Documentation For Authorization


## Bearer

- **Type**: API key
- **API key parameter name**: Authorization
- **Location**: HTTP header


## Author



