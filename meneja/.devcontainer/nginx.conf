events { }

http {

    access_log  /var/log/nginx/access.log;
    log_format main '$http_x_forwarded_for - $remote_user [$time_local] '
'"$request_method $scheme://$host$request_uri $server_protocol" '
'$status $body_bytes_sent "$http_referer" '
'"$http_user_agent" $request_time';

    server {
        listen 80;
        server_name storage.meneja-dev.gig.tech.local;
        ignore_invalid_headers off;
        client_max_body_size 0;
        proxy_buffering off;

        location / {
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $http_host;
            proxy_connect_timeout 300;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            proxy_pass  http://mnj-minio:9000;
        }
    }

    server {
        listen 80;
        server_name iam.cairo-cloud.eg.local;
        root /var/certs;

        location / {

        }
    }

    server {
        listen 443 ssl;
        ssl_certificate iam.cairo-cloud.eg.local.cert;
        ssl_certificate_key iam.cairo-cloud.eg.local.key;
        server_name  iam.cairo-cloud.eg.local;
        proxy_set_header   Host $host;
        ignore_invalid_headers off;
        client_max_body_size 0;
        proxy_buffering off;

        location / {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_connect_timeout 300;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            proxy_pass http://iam-backend:8080;
        }

        location /api/1/admin/branding/color-scheme {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host "cairo-cloud.eg.local";
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host "cairo-cloud.eg.local";
            proxy_pass http://mnj-app:8081/api/1/admin/branding/color-scheme;
        }

        location /api/1/admin/branding/logo {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host "cairo-cloud.eg.local";
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host "cairo-cloud.eg.local";
            proxy_pass http://mnj-app:8081/api/1/admin/branding/logo;
        }
        location ~ ^/api/1/admin(.*)$ {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host "cairo-cloud.eg.local";
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host "cairo-cloud.eg.local";
            proxy_pass http://mnj-app:8081/api/1/admin$1;
        }
    }

    server {
        listen  80;
        server_name  meneja-dev.gig.tech.local;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-Host $host;
        client_max_body_size 0;
        location /api {
            proxy_read_timeout 1200;
            proxy_pass  http://mnj-app:8080;
        }
        location /bi {
            proxy_read_timeout 1200;
            proxy_pass http://mnj-app:8080;
        }
        location /socket.io {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_pass http://mnj-app:8080/socket.io;
        }

        location /sockjs-node/ {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_pass http://mnj-node:8080;
        }

        location /swaggerui {
            proxy_pass  http://mnj-app:8080;
        }
        location /authorization {
            proxy_pass  http://mnj-app:8080/authorization;
        }
        location /logout {
            proxy_pass  http://mnj-app:8080/logout;
        }
        location /liveness_probe {
            proxy_pass  http://mnj-app:8080/liveness_probe;
        }
        location /callback {
            proxy_pass  http://mnj-app:8080/callback;
        }
        location / {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_read_timeout 1200;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_pass http://mnj-node:8080/;
        }
    }

    server {
        listen 443;
        server_name  cairo-cloud.eg.local;
        ssl_certificate     cairo-cloud.eg.local.cert;
        ssl_certificate_key cairo-cloud.eg.local.key;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-Host $host;

        location /v2 {
            proxy_read_timeout 1200;
            proxy_pass http://mnj-app:8081;
        }
    }

    server {
        listen 80;
        server_name  cairo-cloud.eg.local;
        proxy_set_header   Host $host;
        client_max_body_size 0;

        location = /vdi/agent {
            return 301 /vdi/agent/;
        }
        location ^~ /vdi/agent/ {
            proxy_http_version 1.1;
            proxy_read_timeout 1200;
            proxy_buffering off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header X-Base-Path "/vdi/agent/";
            # IMPORTANT: no trailing slash → upstream sees /vdi/agent/...
            proxy_pass http://vdi-node:8080;
        }

        # Add specific handling for vdi-node static assets that might not have the prefix
        location ^~ /static/ {
            # Check if this request is coming from a VDI page
            if ($http_referer ~ "^https?://[^/]+/vdi/agent") {
                proxy_pass http://vdi-node:8080;
                break;
            }
            # Otherwise, fall through to normal handling
        }

        # ===== OTHER ROUTES (unchanged) =====

        location /api { proxy_read_timeout 1200; proxy_pass http://mnj-app:8081; }
        location /bi  { proxy_read_timeout 1200; proxy_pass http://mnj-app:8081; }
        location /docs{ proxy_read_timeout 1200; proxy_pass http://mnj-app:8082; }

        location /socket.io {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_pass http://mnj-app:8081/socket.io;
        }

        location /sockjs-node/ {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_pass http://vco-node:8080;
        }

        location /swaggerui     { proxy_pass http://mnj-app:8081; }
        location /authorization { proxy_pass http://mnj-app:8081/authorization; }
        location /logout        { proxy_pass http://mnj-app:8081/logout; }
        location /demo          { proxy_pass http://mnj-app:8081/demo; }
        location /liveness_probe{ proxy_pass http://mnj-app:8081/liveness_probe; }
        location /callback      { proxy_pass http://mnj-app:8081/callback; }

        # catch-all to vco-node (kept last so it never shadows /vdi/agent/)
        location / {
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_read_timeout 1200;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_pass http://vco-node:8080/;
        }
    }
}
