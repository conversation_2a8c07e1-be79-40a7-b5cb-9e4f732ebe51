# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REP<PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import logging
import os
from time import time
from typing import Iterable, List

import jose.jwt
import requests
from requests.exceptions import HTTPError

from meneja.lib.enumeration import EnvironmentName

logger = logging.getLogger()


class OrganizationNotFound(Exception):
    """Organization not found error"""

    pass  # pylint: disable=unnecessary-pass


class OrganizationAlreadyMember(Exception):
    """Organization already member error"""

    pass  # pylint: disable=unnecessary-pass


class OrganizationAlreadyExists(Exception):
    """Organization already exists error"""

    pass  # pylint: disable=unnecessary-pass


class ParentOrganizationNotFound(Exception):
    """Parent organization not found error"""

    pass  # pylint: disable=unnecessary-pass


class MaximumAmountReached(Exception):
    """MAximum amount reached error"""

    pass  # pylint: disable=unnecessary-pass


class AccessDeniedWithoutScope(Exception):
    """Access denied without scope error"""

    def __init__(self, message, *scopes):
        super().__init__(message)
        self.scopes = scopes


class RefreshToken:
    """Refreshes JWT if expired"""

    def __call__(self, func):
        def wrapper(super_self, *args, **kwargs):
            if "skip_jwt_refresh" in kwargs and kwargs["skip_jwt_refresh"]:
                logger.debug("Skipping JWT refresh")
            else:
                super_self.refresh_jwt()
            return func(super_self, *args, **kwargs)

        return wrapper


class ItsyouOnlineClient:
    """Itsyou.online client"""

    org_seperator = "."
    refresh_buffer = 5 * 60  # Duration in second to refresh the JWT in advance of expiration.

    def __init__(self, jwt: str = "", url: str = None):
        self.session = requests.Session()
        if not jwt:
            jwt = os.environ.get("MNJ_TOKEN")
        if not jwt:
            raise RuntimeError("Itsyou.online client could not find a JWT")
        self.set_jwt(jwt)
        if not url:
            claims = jose.jwt.get_unverified_claims(jwt)
            iss = claims["iss"]
            if iss == "itsyouonline":
                if EnvironmentName.current() == EnvironmentName.PRD:
                    url = "https://itsyou.online"
                else:
                    url = "https://staging.itsyou.online"
            else:
                if EnvironmentName.current() == EnvironmentName.TEST:
                    url = f"http://{os.environ['IAM_HOST']}"
                else:
                    url = f"https://{iss}"
        self.base_url = url
        if not self.base_url:
            raise RuntimeError("Itsyou.online client could not find a itsyou.online URL")

    def set_custom_jwt_from_api_key(self, client_id: str, client_secret: str, scopes: str = None):
        """Generate for specific API keys. Used to generate personal jwt tokens

        Args:
            app_id (str): App ID
            app_secret (str): App secret
            scopes (str): Comma-separated list of scopes
        """
        import logging
        logger = logging.getLogger(__name__)

        data = {
            "grant_type": "client_credentials",
            "client_id": client_id,
            "client_secret": client_secret,
            "response_type": "id_token",
            "scope": "offline_access",
        }
        if scopes:
            data["scope"] = f"{data['scope']},{scopes}"

        logger.info("=== SERVICE ACCOUNT DEBUG: ItsyouOnline set_custom_jwt_from_api_key ===")
        logger.info("SERVICE ACCOUNT DEBUG: client_id: %s", client_id)
        logger.info("SERVICE ACCOUNT DEBUG: client_secret length: %d", len(client_secret))
        logger.info("SERVICE ACCOUNT DEBUG: requested scopes: %s", scopes)
        logger.info("SERVICE ACCOUNT DEBUG: final scope parameter: %s", data["scope"])
        logger.info("SERVICE ACCOUNT DEBUG: full request data: %s", {k: v if k != "client_secret" else "***" for k, v in data.items()})

        try:
            response = self.call("v1/oauth/access_token", method="post", data=data)
            logger.info("SERVICE ACCOUNT DEBUG: OAuth response status: %d", response.status_code)
            logger.info("SERVICE ACCOUNT DEBUG: OAuth response headers: %s", dict(response.headers))
            jwt = response.text
            logger.info("SERVICE ACCOUNT DEBUG: JWT response length: %d", len(jwt))
            self.set_jwt(jwt)
            logger.info("SERVICE ACCOUNT DEBUG: Successfully set JWT")
        except Exception as e:
            logger.error("SERVICE ACCOUNT DEBUG: Error in OAuth request: %s", e)
            if hasattr(e, 'response') and e.response is not None:
                logger.error("SERVICE ACCOUNT DEBUG: Error response status: %d", e.response.status_code)
                logger.error("SERVICE ACCOUNT DEBUG: Error response text: %s", e.response.text)
            raise

    @classmethod
    def new(
        cls, domain: str, client_id: str, client_secret: str, is_https: bool = True, scope: str = "organization:owner"
    ):
        """Creates a client using client_id and secret.

        Args:
            domain (str): Domain of the iam server
            client_id (str): Client id
            client_secret (str): Client secret
            scope (str) : Client scope default is organization owner
        """
        prefix = "https" if is_https else "http"
        params = dict(
            grant_type="client_credentials",
            client_id=client_id,
            client_secret=client_secret,
            response_type="id_token",
            scope=scope,
        )
        if EnvironmentName.current() == EnvironmentName.DEV:
            response = requests.post(
                f"{prefix}://{domain}/v1/oauth/access_token", params=params, verify=False, timeout=60
            )
        else:
            response = requests.post(f"{prefix}://{domain}/v1/oauth/access_token", params=params, timeout=60)
        response.raise_for_status()
        return cls(jwt=response.text, url=f"{prefix}://{domain}/")

    def _get_jwt_username(self):
        claims = jose.jwt.get_unverified_claims(self.jwt)
        return claims["username"]

    def set_jwt(self, jwt: str):
        """Sets the authorization header

        Arguments:
            jwt {str} -- JWT
        """
        self.jwt = jwt
        self.session.headers["Authorization"] = f"bearer {jwt}"

    def refresh_jwt(self, force_refresh: bool = False):
        """Refresh the current JWT if needed.

        Arguments:
            force_refresh {bool} -- Refresh Token even if not expired
        """
        claims = jose.jwt.get_unverified_claims(self.jwt)
        if "refresh_token" in claims and (force_refresh or (int(time()) + self.refresh_buffer > claims["exp"])):
            logger.debug("Refreshing JWT")
            jwt = self.call("v1/oauth/jwt/refresh", method="post").text
            self.set_jwt(jwt)

    def get_jwt(self) -> str:
        """Return the current JWT
        If expired and refreshable, it will be the refreshed token

        Returns:
            str -- Currently used JWT
        """
        self.refresh_jwt()
        return self.jwt

    def call(
        self, url: str, method: str = "get", params: List = None, json=None, data=None
    ) -> requests.models.Response:
        """Sends an API request to the server

        Arguments:
            url {str} -- API path on the IYO server

        Keyword Arguments:
            method {str} -- HTTP request method (default: {"get"})
            params {[type]} -- (optional) Dictionary or bytes to be sent in the query (default: {None})
            json {[type]} -- (optional) json to send in the body of the request (default: {None})
            data {[type]} -- (optional) data to send in the body of the request (default: {None})

        Returns:
            requests.Response -- Response of the API call
        """
        url = os.path.join(self.base_url, url)
        if EnvironmentName.current() == EnvironmentName.DEV:
            response = self.session.request(url=url, method=method, params=params, json=json, data=data, verify=False)
        else:
            response = self.session.request(url=url, method=method, params=params, json=json, data=data)

        response.raise_for_status()
        return response

    @RefreshToken()
    def get_organization(self, name: str, skip_jwt_refresh: bool = False) -> dict:  # pylint: disable=unused-argument
        """Get the information of a specific organization

        Arguments:
            name {str} -- Name of the organization

        Returns:
            dict -- Dict with data of the organization
        """
        url = f"api/organizations/{name}"
        r = self.call(url=url, method="get")
        return r.json()

    @RefreshToken()
    def delete_organization(self, name: str, skip_jwt_refresh: bool = False) -> None:  # pylint: disable=unused-argument
        """Delete an organization

        Arguments:
            name {str} -- Name of the organization to be deleted

        Returns:
            None
        """
        url = f"api/organizations/{name}"
        self.call(url=url, method="delete")

    @RefreshToken()
    def create_organization(
        self,
        name: str,
        members: Iterable = None,
        dns: Iterable = None,
        owners: Iterable = None,
        public_keys: Iterable = None,
        orgowners: Iterable = None,
        orgmembers: Iterable = None,
        requiredscopes: Iterable = None,
        includesuborgsof: Iterable = None,
        includes: Iterable = None,
        skip_jwt_refresh: bool = False,  # pylint: disable=unused-argument
    ) -> dict:
        """Create an organization

        Arguments:
            name {str} -- Name of the organization

        Keyword Arguments:
            members {Iterable} -- (default: {None})
            dns {Iterable} -- (default: {None})
            owners {Iterable} -- (default: {None})
            publicKeys {Iterable} -- Public Keys (default: {None})
            orgowners {Iterable} --  (default: {None})
            orgmembers {list} -- (default: {None})
            requiredscopes {Iterable} -- (default: {None})
            includesuborgsof {Iterable} -- (default: {None})
            includes {Iterable} -- (default: {None})

        Returns:
            dict -- Current data of the created organization
        """
        iterables = {
            "members": members,
            "dns": dns,
            "owners": owners,
            "public_keys": public_keys,
            "orgowners": orgowners,
            "orgmembers": orgmembers,
            "requiredscopes": requiredscopes,
            "includesuborgsof": includesuborgsof,
            "includes": includes,
        }

        for key, value in iterables.items():
            if value is None:
                iterables[key] = []

        url = "api/organizations"
        data = {
            "dns": dns,
            "globalid": name,
            "members": members,
            "owners": owners,
            "publicKeys": public_keys,
            "orgowners": orgowners,
            "orgmembers": orgmembers,
            "requiredscopes": requiredscopes,
            "includesuborgsof": includesuborgsof,
            "includes": includes,
        }
        try:
            r = self.call(url=url, method="post", json=data)
        except HTTPError as err:
            if err.response.status_code == 409:
                raise OrganizationAlreadyExists(f"Conflict: Organization {name} already exists") from err
            else:
                raise
        return r.json()

    @RefreshToken()
    def create_suborganization(
        self,
        name: str,
        members: Iterable = None,
        dns: Iterable = None,
        owners: Iterable = None,
        public_keys: Iterable = None,
        orgowners: Iterable = None,
        orgmembers: Iterable = None,
        requiredscopes: Iterable = None,
        includesuborgsof: Iterable = None,
        includes: Iterable = None,
        skip_jwt_refresh: bool = False,  # pylint: disable=unused-argument
    ) -> dict:
        """Create a sub organization

        Arguments:
            name {str} -- Name of the sub organization

        Keyword Arguments:
            members {Iterable} -- (default: {None})
            dns {Iterable} -- (default: {None})
            owners {Iterable} -- (default: {None})
            publicKeys {Iterable} -- (default: {None})
            orgowners {Iterable} -- (default: {None})
            orgmembers {list} -- (default: {None})
            requiredscopes {Iterable} -- (default: {None})
            includesuborgsof {Iterable} -- (default: {None})
            includes {Iterable} -- (default: {None})

        Returns:
            dict -- Current data of the created sub organization
        """
        iterables = {
            "members": members,
            "dns": dns,
            "owners": owners,
            "public_keys": public_keys,
            "orgowners": orgowners,
            "orgmembers": orgmembers,
            "requiredscopes": requiredscopes,
            "includesuborgsof": includesuborgsof,
            "includes": includes,
        }
        for key, value in iterables.items():
            if value is None:
                iterables[key] = []

        parent_org = self.org_seperator.join(name.split(self.org_seperator)[:-1])
        url = f"api/organizations/{parent_org}"
        data = {
            "globalid": name,
        }
        try:
            r = self.call(url=url, method="post", json=data)
        except HTTPError as err:
            if err.response.status_code == 403:
                raise AccessDeniedWithoutScope(
                    f"The user:ownerof:organization scope for organization {parent_org} is needed "
                    "to continue the operation!",
                    f"user:ownerof:organization:{parent_org}",
                ) from err
            elif err.response.status_code == 404:
                raise ParentOrganizationNotFound(f"Parent org {parent_org} not found") from err
            elif err.response.status_code == 422:
                raise MaximumAmountReached("Maximum amount of organizations reached") from err
            elif err.response.status_code == 409:
                raise OrganizationAlreadyExists(f"Conflict: Organization {name} already exists") from err
            else:
                raise
        return r.json()

    @RefreshToken()
    def add_orgmember(
        self,
        parent: str,
        org: str,
        include_suborgs=False,
        skip_jwt_refresh: bool = False,  # pylint: disable=unused-argument
        accept_invite: bool = False,
    ) -> None:
        """Add another organization as a member of this one

        Arguments:
            parent_name {str} -- Name of organization to include the suborg into
            org {str} -- Suborg to include
            include_suborgs -- Include sub organizations of the provided organizations as member or the parent org
            accept_invite {bool} -- add organization without sending invite

        Returns:
            None
        """
        url = f"api/organizations/{parent}/orgmembers"
        data = {"orgmember": org, "acceptinvite": accept_invite}
        try:
            self.call(url=url, method="post", json=data)
        except HTTPError as err:
            if err.response.status_code == 404:
                raise OrganizationNotFound from err
            elif err.response.status_code == 409:
                raise OrganizationAlreadyMember("Organization is already an owner or a member") from err
            elif err.response.status_code == 422:
                raise MaximumAmountReached("Maximum amount of invitations reached") from err
            raise
        if include_suborgs:
            self.include_suborganization(parent=parent, org=org)

    @RefreshToken()
    def delete_orgmember(
        self, parent: str, org: str, skip_jwt_refresh: bool = False  # pylint: disable=unused-argument
    ) -> None:
        """delete organization from this one

        Arguments:
            parent_name {str} -- Name of organization to delete the suborg from
            org {str} -- Suborg to delete

        Returns:
            None
        """
        url = f"api/organizations/{parent}/orgmembers/{org}"

        try:
            self.call(url=url, method="delete")
        except HTTPError as err:
            if err.response.status_code == 404:
                raise OrganizationNotFound from err
            if err.response.status_code == 403:
                raise AccessDeniedWithoutScope(
                    f"The user:ownerof:organization scope for organization {parent} is needed "
                    "to continue the operation!",
                    f"user:ownerof:organization:{parent}",
                ) from err
            raise

    @RefreshToken()
    def invite_user_to_organization(
        self,
        organization: str,
        user: str,
        is_owner: bool = False,
        skip_jwt_refresh: bool = False,  # pylint: disable=unused-argument
    ) -> None:
        """Add a user as to organization

        Arguments:
            organization {str} -- Name of organization to to add the user to
            user {str} -- user to add

        Returns:
            None
        """
        users_type = "owners" if is_owner else "members"
        url = f"api/organizations/{organization}/{users_type}"
        data = {"searchString": user}
        self.call(url=url, method="post", json=data)

    @RefreshToken()
    def remove_user_from_organization(
        self,
        organization: str,
        user: str,
        is_owner: bool = False,
        skip_jwt_refresh: bool = False,  # pylint: disable=unused-argument
    ) -> None:
        """Remove a user as to organization

        Arguments:
            organization {str} -- Name of organization to to add the user to
            user {str} -- user to add

        Returns:
            None
        """
        users_type = "owners" if is_owner else "members"
        url = f"api/organizations/{organization}/{users_type}/{user}"
        self.call(url=url, method="delete")

    @RefreshToken()
    def include_suborganization(
        self, parent: str, org: str, skip_jwt_refresh: bool = False  # pylint: disable=unused-argument
    ) -> None:
        """Add an orgmember or orgowner organization to the includesuborgsof list

        Arguments:
            org {str} -- Suborg to include
            parent {str} -- Name of organization to include the suborg into

        Returns:
            None
        """
        parent_org_info = self.get_organization(parent)
        if org in parent_org_info["includesuborgsof"]:
            return
        url = f"api/organizations/{parent}/orgmembers/includesuborgs"
        data = {"globalid": org}
        self.call(url=url, method="post", json=data)

    @RefreshToken()
    def generate_api_key(
        self,
        organization: str,
        label: str,
        grant_type: bool = False,
        callback_url: str = "",
        skip_jwt_refresh: bool = False,  # pylint: disable=unused-argument
    ) -> str:
        """Generates an API key for an organization.
        The ID of the key is the full (sub-)organization name.
        The secret of the key is returned when the API key is successfully created

        Arguments:
            organization {str} -- Organization that the key represents
            label {str} -- Label for the key

        Keyword Arguments:
            grant_type {bool} -- (default: {False})
            callback_url {str} -- callback URL for calls using this API key (default: {""})

        Returns:
            str -- Secret of the API key
        """
        url = f"api/organizations/{organization}/apikeys"
        data = {
            "label": label,
            "clientCredentialsGrantType": grant_type,
            "callbackURL": callback_url,
        }
        r = self.call(url=url, method="post", json=data)
        return r.json()["secret"]

    @RefreshToken()
    def get_api_secret(
        self, organization: str, label: str, skip_jwt_refresh: bool = False  # pylint: disable=unused-argument
    ) -> str:
        """Fetches the API secret of an organization with specified label

        Arguments:
            organization {str} -- Organization of the API key
            label {str} -- LAbel of the API key

        Returns:
            str -- API key secret
        """
        url = f"api/organizations/{organization}/apikeys/{label}"
        r = self.call(url=url, method="get")
        return r.json()["secret"]

    @RefreshToken()
    def get_organizations(
        self, username: str = "", skip_jwt_refresh: bool = False  # pylint: disable=unused-argument
    ) -> dict:
        """Returns the organizations the provided user is member of or owns.
        In case no user was provided the JWT owner will be user as user.
        Returned as a dict with fields "member" and "owner" which are lists of organizations.

        Arguments:
            username {str} -- Name of the user to return the organizations for

        Returns:
            dict -- Organizations the JWT owner owns or is member of
        """
        if not username:
            username = self._get_jwt_username()
        url = f"api/users/{username}/organizations"
        r = self.call(url=url, method="get")
        return r.json()

    @RefreshToken()
    def get_tree(self, org: str = "", skip_jwt_refresh: bool = False) -> dict:  # pylint: disable=unused-argument
        """Returns the tree structure of the organization

        Keyword Arguments:
            org {str} -- Organization to get the tree structure from (default: {""})

        Returns:
            dict -- Tree stucture of the organization
        """
        url = f"api/organizations/{org}/tree"
        try:
            r = self.call(url=url, method="get")
        except HTTPError as err:
            if err.response.status_code == 403:
                raise AccessDeniedWithoutScope(
                    f"The user:ownerof:organization scope for organization {org} is needed "
                    "to continue the operation!",
                    f"user:ownerof:organization:{org}",
                ) from err
            raise
        return r.json()

    @RefreshToken()
    def list_organization_users(
        self, organization: str, skip_jwt_refresh: bool = False  # pylint: disable=unused-argument
    ) -> dict:
        """List users (members/owners) of organization

        Args:
            organization (str): IAM/IYO Organization

        Returns:
            dict: haseditpermissions: bool
                  users: list
                    username: str
                    role: str
                    missingscopes: List[str]
        """
        url = f"api/organizations/{organization}/users"
        r = self.call(url=url, method="get")
        return r.json()

    @RefreshToken()
    def accept_invitation(self, username: str, organization: str, role: str, body: dict = None) -> dict:
        """Accept user invitation to join an organization

        Args:
            username (str): Username
            organization (str): IAM/IYO Organization
            role (str): Role either owner or member (admin will be added in the future)
            body (dict): Invitation body can be acquired from get_user_notifications

        Returns:
            dict: User notification
        """
        url = f"api/users/{username}/organizations/{organization}/roles/{role}"
        r = self.call(url=url, method="post", json=body or {})
        return r.json()

    @RefreshToken()
    def accept_org_invitation(self, organization: str, inviting_organization: str, role: str) -> dict:
        """Accept organization invitation to join an organization

        Args:
            organization (str): Organization name
            inviting_organization (str): IAM/IYO inviting Organization
            role (str): Role either orgowner or orgmember
            body (dict): Invitation body can be acquired from get_user_notifications

        Returns:
            dict: User notification
        """
        url = f"api/organizations/{organization}/organizations/{inviting_organization}/roles/{role}"
        json_data = {
            "organization": inviting_organization,
            "role": role,
            "user": organization,
            "isorganization": True,
            "status": "pending",
            "method": "website",
        }
        r = self.call(url=url, method="post", json=json_data)
        return r.json()

    @RefreshToken()
    def get_org_invitations(
        self, organization: str, skip_jwt_refresh: bool = False  # pylint: disable=unused-argument
    ) -> Iterable:
        """Returns invitations of the organization

        Keyword Arguments:
            org {str} -- Organization to get the tree structure from (default: {""})

        Returns:
            Iterable -- List of invitations
        """
        url = f"api/organizations/{organization}/invitations/"
        r = self.call(url=url, method="get")
        return r.json()

    @RefreshToken()
    def get_user_notifications(self, username: str = "") -> dict:
        """Returns the tree structure of the organization

        Keyword Arguments:
            org {str} -- Organization to get the tree structure from (default: {""})

        Returns:
            dict -- user notifications
        """
        if not username:
            username = self._get_jwt_username()
        url = f"api/users/{username}/notifications/"
        r = self.call(url=url, method="get")
        return r.json()

    @RefreshToken()
    def get_user_info(self, skip_jwt_refresh: bool = False) -> dict:  # pylint: disable=unused-argument
        """Returns the user info
        Returns:
            dict -- User information
        """
        url = f"api/users/{self._get_jwt_username()}/info"
        r = self.call(url=url, method="get")
        return r.json()
