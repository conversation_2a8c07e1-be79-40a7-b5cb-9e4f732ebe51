# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ExternalNetworksExternalNetworkUpdateStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "name": "str",
        "gateway": "str",
        "protection": "bool",
        "speed_limit": "int",
        "dhcp": "bool",
        "ping_ips": "list[IpShort]",
    }

    attribute_map = {
        "name": "name",
        "gateway": "gateway",
        "protection": "protection",
        "speed_limit": "speed_limit",
        "dhcp": "dhcp",
        "ping_ips": "ping_ips",
    }

    def __init__(
        self, name=None, gateway=None, protection=None, speed_limit=None, dhcp=None, ping_ips=None, _configuration=None
    ):  # noqa: E501
        """ExternalNetworksExternalNetworkUpdateStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._gateway = None
        self._protection = None
        self._speed_limit = None
        self._dhcp = None
        self._ping_ips = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if gateway is not None:
            self.gateway = gateway
        if protection is not None:
            self.protection = protection
        if speed_limit is not None:
            self.speed_limit = speed_limit
        if dhcp is not None:
            self.dhcp = dhcp
        if ping_ips is not None:
            self.ping_ips = ping_ips

    @property
    def name(self):
        """Gets the name of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501

        External network name  # noqa: E501

        :return: The name of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ExternalNetworksExternalNetworkUpdateStruct.

        External network name  # noqa: E501

        :param name: The name of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def gateway(self):
        """Gets the gateway of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501

        Gateway to the upstream network  # noqa: E501

        :return: The gateway of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :rtype: str
        """
        return self._gateway

    @gateway.setter
    def gateway(self, gateway):
        """Sets the gateway of this ExternalNetworksExternalNetworkUpdateStruct.

        Gateway to the upstream network  # noqa: E501

        :param gateway: The gateway of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :type: str
        """

        self._gateway = gateway

    @property
    def protection(self):
        """Gets the protection of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501

        Indicates if this external network has ip-mac protection or not.  # noqa: E501

        :return: The protection of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :rtype: bool
        """
        return self._protection

    @protection.setter
    def protection(self, protection):
        """Sets the protection of this ExternalNetworksExternalNetworkUpdateStruct.

        Indicates if this external network has ip-mac protection or not.  # noqa: E501

        :param protection: The protection of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :type: bool
        """

        self._protection = protection

    @property
    def speed_limit(self):
        """Gets the speed_limit of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501

        Maximum bandwith speed configured on the network interfaces expressed in Kbit. 0 is the default which gets translated into 1 Gbit  # noqa: E501

        :return: The speed_limit of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :rtype: int
        """
        return self._speed_limit

    @speed_limit.setter
    def speed_limit(self, speed_limit):
        """Sets the speed_limit of this ExternalNetworksExternalNetworkUpdateStruct.

        Maximum bandwith speed configured on the network interfaces expressed in Kbit. 0 is the default which gets translated into 1 Gbit  # noqa: E501

        :param speed_limit: The speed_limit of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :type: int
        """

        self._speed_limit = speed_limit

    @property
    def dhcp(self):
        """Gets the dhcp of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501

        Indicates if a dhcp server automatically hands out ip addresses  # noqa: E501

        :return: The dhcp of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :rtype: bool
        """
        return self._dhcp

    @dhcp.setter
    def dhcp(self, dhcp):
        """Sets the dhcp of this ExternalNetworksExternalNetworkUpdateStruct.

        Indicates if a dhcp server automatically hands out ip addresses  # noqa: E501

        :param dhcp: The dhcp of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :type: bool
        """

        self._dhcp = dhcp

    @property
    def ping_ips(self):
        """Gets the ping_ips of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501

        List of ips to be pinged to check the external network health  # noqa: E501

        :return: The ping_ips of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :rtype: list[IpShort]
        """
        return self._ping_ips

    @ping_ips.setter
    def ping_ips(self, ping_ips):
        """Sets the ping_ips of this ExternalNetworksExternalNetworkUpdateStruct.

        List of ips to be pinged to check the external network health  # noqa: E501

        :param ping_ips: The ping_ips of this ExternalNetworksExternalNetworkUpdateStruct.  # noqa: E501
        :type: list[IpShort]
        """

        self._ping_ips = ping_ips

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ExternalNetworksExternalNetworkUpdateStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExternalNetworksExternalNetworkUpdateStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExternalNetworksExternalNetworkUpdateStruct):
            return True

        return self.to_dict() != other.to_dict()
