# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VmachinesVmachineListStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "name": "str",
        "status": "str",
        "cloudspace": "VmachinesVmDetailsStructCloudspace",
        "node": "VmachinesVmDetailsStructNode",
        "account": "VmachinesVmDetailsStructAccount",
        "backup_status": "str",
    }

    attribute_map = {
        "id": "id",
        "name": "name",
        "status": "status",
        "cloudspace": "cloudspace",
        "node": "node",
        "account": "account",
        "backup_status": "backup_status",
    }

    def __init__(
        self,
        id=None,
        name=None,
        status=None,
        cloudspace=None,
        node=None,
        account=None,
        backup_status=None,
        _configuration=None,
    ):  # noqa: E501
        """VmachinesVmachineListStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._status = None
        self._cloudspace = None
        self._node = None
        self._account = None
        self._backup_status = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.status = status
        self.cloudspace = cloudspace
        self.node = node
        self.account = account
        if backup_status is not None:
            self.backup_status = backup_status

    @property
    def id(self):
        """Gets the id of this VmachinesVmachineListStruct.  # noqa: E501

        vm id  # noqa: E501

        :return: The id of this VmachinesVmachineListStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VmachinesVmachineListStruct.

        vm id  # noqa: E501

        :param id: The id of this VmachinesVmachineListStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this VmachinesVmachineListStruct.  # noqa: E501

        vm name  # noqa: E501

        :return: The name of this VmachinesVmachineListStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VmachinesVmachineListStruct.

        vm name  # noqa: E501

        :param name: The name of this VmachinesVmachineListStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def status(self):
        """Gets the status of this VmachinesVmachineListStruct.  # noqa: E501

        vm status  # noqa: E501

        :return: The status of this VmachinesVmachineListStruct.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VmachinesVmachineListStruct.

        vm status  # noqa: E501

        :param status: The status of this VmachinesVmachineListStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    @property
    def cloudspace(self):
        """Gets the cloudspace of this VmachinesVmachineListStruct.  # noqa: E501


        :return: The cloudspace of this VmachinesVmachineListStruct.  # noqa: E501
        :rtype: VmachinesVmDetailsStructCloudspace
        """
        return self._cloudspace

    @cloudspace.setter
    def cloudspace(self, cloudspace):
        """Sets the cloudspace of this VmachinesVmachineListStruct.


        :param cloudspace: The cloudspace of this VmachinesVmachineListStruct.  # noqa: E501
        :type: VmachinesVmDetailsStructCloudspace
        """
        if self._configuration.client_side_validation and cloudspace is None:
            raise ValueError("Invalid value for `cloudspace`, must not be `None`")  # noqa: E501

        self._cloudspace = cloudspace

    @property
    def node(self):
        """Gets the node of this VmachinesVmachineListStruct.  # noqa: E501


        :return: The node of this VmachinesVmachineListStruct.  # noqa: E501
        :rtype: VmachinesVmDetailsStructNode
        """
        return self._node

    @node.setter
    def node(self, node):
        """Sets the node of this VmachinesVmachineListStruct.


        :param node: The node of this VmachinesVmachineListStruct.  # noqa: E501
        :type: VmachinesVmDetailsStructNode
        """
        if self._configuration.client_side_validation and node is None:
            raise ValueError("Invalid value for `node`, must not be `None`")  # noqa: E501

        self._node = node

    @property
    def account(self):
        """Gets the account of this VmachinesVmachineListStruct.  # noqa: E501


        :return: The account of this VmachinesVmachineListStruct.  # noqa: E501
        :rtype: VmachinesVmDetailsStructAccount
        """
        return self._account

    @account.setter
    def account(self, account):
        """Sets the account of this VmachinesVmachineListStruct.


        :param account: The account of this VmachinesVmachineListStruct.  # noqa: E501
        :type: VmachinesVmDetailsStructAccount
        """
        if self._configuration.client_side_validation and account is None:
            raise ValueError("Invalid value for `account`, must not be `None`")  # noqa: E501

        self._account = account

    @property
    def backup_status(self):
        """Gets the backup_status of this VmachinesVmachineListStruct.  # noqa: E501

        latest backup status  # noqa: E501

        :return: The backup_status of this VmachinesVmachineListStruct.  # noqa: E501
        :rtype: str
        """
        return self._backup_status

    @backup_status.setter
    def backup_status(self, backup_status):
        """Sets the backup_status of this VmachinesVmachineListStruct.

        latest backup status  # noqa: E501

        :param backup_status: The backup_status of this VmachinesVmachineListStruct.  # noqa: E501
        :type: str
        """

        self._backup_status = backup_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VmachinesVmachineListStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VmachinesVmachineListStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VmachinesVmachineListStruct):
            return True

        return self.to_dict() != other.to_dict()
