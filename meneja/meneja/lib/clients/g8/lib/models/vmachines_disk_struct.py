# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VmachinesDiskStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"id": "int", "name": "str", "size": "int", "path": "str", "type_": "str"}

    attribute_map = {"id": "id", "name": "name", "size": "size", "path": "path", "type_": "type_"}

    def __init__(self, id=None, name=None, size=None, path=None, type_=None, _configuration=None):  # noqa: E501
        """VmachinesDiskStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._size = None
        self._path = None
        self._type_ = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.size = size
        self.path = path
        self.type_ = type_

    @property
    def id(self):
        """Gets the id of this VmachinesDiskStruct.  # noqa: E501

        Image id  # noqa: E501

        :return: The id of this VmachinesDiskStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VmachinesDiskStruct.

        Image id  # noqa: E501

        :param id: The id of this VmachinesDiskStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this VmachinesDiskStruct.  # noqa: E501

        Image name  # noqa: E501

        :return: The name of this VmachinesDiskStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VmachinesDiskStruct.

        Image name  # noqa: E501

        :param name: The name of this VmachinesDiskStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def size(self):
        """Gets the size of this VmachinesDiskStruct.  # noqa: E501

        Image size  # noqa: E501

        :return: The size of this VmachinesDiskStruct.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this VmachinesDiskStruct.

        Image size  # noqa: E501

        :param size: The size of this VmachinesDiskStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and size is None:
            raise ValueError("Invalid value for `size`, must not be `None`")  # noqa: E501

        self._size = size

    @property
    def path(self):
        """Gets the path of this VmachinesDiskStruct.  # noqa: E501

        Image path  # noqa: E501

        :return: The path of this VmachinesDiskStruct.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this VmachinesDiskStruct.

        Image path  # noqa: E501

        :param path: The path of this VmachinesDiskStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and path is None:
            raise ValueError("Invalid value for `path`, must not be `None`")  # noqa: E501

        self._path = path

    @property
    def type_(self):
        """Gets the type_ of this VmachinesDiskStruct.  # noqa: E501

        Image type  # noqa: E501

        :return: The type_ of this VmachinesDiskStruct.  # noqa: E501
        :rtype: str
        """
        return self._type_

    @type_.setter
    def type_(self, type_):
        """Sets the type_ of this VmachinesDiskStruct.

        Image type  # noqa: E501

        :param type_: The type_ of this VmachinesDiskStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type_ is None:
            raise ValueError("Invalid value for `type_`, must not be `None`")  # noqa: E501

        self._type_ = type_

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VmachinesDiskStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VmachinesDiskStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VmachinesDiskStruct):
            return True

        return self.to_dict() != other.to_dict()
