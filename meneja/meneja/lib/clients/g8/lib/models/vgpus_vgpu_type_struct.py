# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VgpusVGPUTypeStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"name": "str", "type_id": "str", "properties": "list[VgpusVGPUPropertyStruct]", "enabled": "bool"}

    attribute_map = {"name": "name", "type_id": "type_id", "properties": "properties", "enabled": "enabled"}

    def __init__(self, name=None, type_id=None, properties=None, enabled=False, _configuration=None):  # noqa: E501
        """VgpusVGPUTypeStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._type_id = None
        self._properties = None
        self._enabled = None
        self.discriminator = None

        self.name = name
        self.type_id = type_id
        self.properties = properties
        self.enabled = enabled

    @property
    def name(self):
        """Gets the name of this VgpusVGPUTypeStruct.  # noqa: E501

        VGPU type name  # noqa: E501

        :return: The name of this VgpusVGPUTypeStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VgpusVGPUTypeStruct.

        VGPU type name  # noqa: E501

        :param name: The name of this VgpusVGPUTypeStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def type_id(self):
        """Gets the type_id of this VgpusVGPUTypeStruct.  # noqa: E501

        VGPU type id  # noqa: E501

        :return: The type_id of this VgpusVGPUTypeStruct.  # noqa: E501
        :rtype: str
        """
        return self._type_id

    @type_id.setter
    def type_id(self, type_id):
        """Sets the type_id of this VgpusVGPUTypeStruct.

        VGPU type id  # noqa: E501

        :param type_id: The type_id of this VgpusVGPUTypeStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type_id is None:
            raise ValueError("Invalid value for `type_id`, must not be `None`")  # noqa: E501

        self._type_id = type_id

    @property
    def properties(self):
        """Gets the properties of this VgpusVGPUTypeStruct.  # noqa: E501

        VGPU type properties  # noqa: E501

        :return: The properties of this VgpusVGPUTypeStruct.  # noqa: E501
        :rtype: list[VgpusVGPUPropertyStruct]
        """
        return self._properties

    @properties.setter
    def properties(self, properties):
        """Sets the properties of this VgpusVGPUTypeStruct.

        VGPU type properties  # noqa: E501

        :param properties: The properties of this VgpusVGPUTypeStruct.  # noqa: E501
        :type: list[VgpusVGPUPropertyStruct]
        """
        if self._configuration.client_side_validation and properties is None:
            raise ValueError("Invalid value for `properties`, must not be `None`")  # noqa: E501

        self._properties = properties

    @property
    def enabled(self):
        """Gets the enabled of this VgpusVGPUTypeStruct.  # noqa: E501

        VGPU type enabled  # noqa: E501

        :return: The enabled of this VgpusVGPUTypeStruct.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this VgpusVGPUTypeStruct.

        VGPU type enabled  # noqa: E501

        :param enabled: The enabled of this VgpusVGPUTypeStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and enabled is None:
            raise ValueError("Invalid value for `enabled`, must not be `None`")  # noqa: E501

        self._enabled = enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VgpusVGPUTypeStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VgpusVGPUTypeStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VgpusVGPUTypeStruct):
            return True

        return self.to_dict() != other.to_dict()
