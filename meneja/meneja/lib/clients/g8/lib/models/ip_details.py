# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class IpDetails(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"address": "str", "port_forwards": "list[PortForwardStruct]", "metric": "int", "egress": "bool"}

    attribute_map = {"address": "address", "port_forwards": "port_forwards", "metric": "metric", "egress": "egress"}

    def __init__(self, address=None, port_forwards=None, metric=None, egress=None, _configuration=None):  # noqa: E501
        """IpDetails - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address = None
        self._port_forwards = None
        self._metric = None
        self._egress = None
        self.discriminator = None

        self.address = address
        self.port_forwards = port_forwards
        self.metric = metric
        self.egress = egress

    @property
    def address(self):
        """Gets the address of this IpDetails.  # noqa: E501

        Ip address  # noqa: E501

        :return: The address of this IpDetails.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this IpDetails.

        Ip address  # noqa: E501

        :param address: The address of this IpDetails.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and address is None:
            raise ValueError("Invalid value for `address`, must not be `None`")  # noqa: E501

        self._address = address

    @property
    def port_forwards(self):
        """Gets the port_forwards of this IpDetails.  # noqa: E501

        default  # noqa: E501

        :return: The port_forwards of this IpDetails.  # noqa: E501
        :rtype: list[PortForwardStruct]
        """
        return self._port_forwards

    @port_forwards.setter
    def port_forwards(self, port_forwards):
        """Sets the port_forwards of this IpDetails.

        default  # noqa: E501

        :param port_forwards: The port_forwards of this IpDetails.  # noqa: E501
        :type: list[PortForwardStruct]
        """
        if self._configuration.client_side_validation and port_forwards is None:
            raise ValueError("Invalid value for `port_forwards`, must not be `None`")  # noqa: E501

        self._port_forwards = port_forwards

    @property
    def metric(self):
        """Gets the metric of this IpDetails.  # noqa: E501

        Metric in routing table  # noqa: E501

        :return: The metric of this IpDetails.  # noqa: E501
        :rtype: int
        """
        return self._metric

    @metric.setter
    def metric(self, metric):
        """Sets the metric of this IpDetails.

        Metric in routing table  # noqa: E501

        :param metric: The metric of this IpDetails.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and metric is None:
            raise ValueError("Invalid value for `metric`, must not be `None`")  # noqa: E501

        self._metric = metric

    @property
    def egress(self):
        """Gets the egress of this IpDetails.  # noqa: E501

        Wether to use this network for egress  # noqa: E501

        :return: The egress of this IpDetails.  # noqa: E501
        :rtype: bool
        """
        return self._egress

    @egress.setter
    def egress(self, egress):
        """Sets the egress of this IpDetails.

        Wether to use this network for egress  # noqa: E501

        :param egress: The egress of this IpDetails.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and egress is None:
            raise ValueError("Invalid value for `egress`, must not be `None`")  # noqa: E501

        self._egress = egress

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(IpDetails, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IpDetails):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IpDetails):
            return True

        return self.to_dict() != other.to_dict()
