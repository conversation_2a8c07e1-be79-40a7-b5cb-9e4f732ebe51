# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ExternalNetworkListStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "name": "str",
        "type": "str",
        "subnet": "str",
        "gateway": "str",
        "ips": "list[IpShort]",
    }

    attribute_map = {"id": "id", "name": "name", "type": "type", "subnet": "subnet", "gateway": "gateway", "ips": "ips"}

    def __init__(
        self, id=None, name=None, type=None, subnet=None, gateway=None, ips=None, _configuration=None
    ):  # noqa: E501
        """ExternalNetworkListStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._type = None
        self._subnet = None
        self._gateway = None
        self._ips = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.type = type
        self.subnet = subnet
        self.gateway = gateway
        self.ips = ips

    @property
    def id(self):
        """Gets the id of this ExternalNetworkListStruct.  # noqa: E501

        unique ID for the external network  # noqa: E501

        :return: The id of this ExternalNetworkListStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ExternalNetworkListStruct.

        unique ID for the external network  # noqa: E501

        :param id: The id of this ExternalNetworkListStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this ExternalNetworkListStruct.  # noqa: E501

        Name of the external network  # noqa: E501

        :return: The name of this ExternalNetworkListStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ExternalNetworkListStruct.

        Name of the external network  # noqa: E501

        :param name: The name of this ExternalNetworkListStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def type(self):
        """Gets the type of this ExternalNetworkListStruct.  # noqa: E501

        External network type ('external', 'cloudspace')  # noqa: E501

        :return: The type of this ExternalNetworkListStruct.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ExternalNetworkListStruct.

        External network type ('external', 'cloudspace')  # noqa: E501

        :param type: The type of this ExternalNetworkListStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def subnet(self):
        """Gets the subnet of this ExternalNetworkListStruct.  # noqa: E501

        Subnet mask for the network  # noqa: E501

        :return: The subnet of this ExternalNetworkListStruct.  # noqa: E501
        :rtype: str
        """
        return self._subnet

    @subnet.setter
    def subnet(self, subnet):
        """Sets the subnet of this ExternalNetworkListStruct.

        Subnet mask for the network  # noqa: E501

        :param subnet: The subnet of this ExternalNetworkListStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet is None:
            raise ValueError("Invalid value for `subnet`, must not be `None`")  # noqa: E501

        self._subnet = subnet

    @property
    def gateway(self):
        """Gets the gateway of this ExternalNetworkListStruct.  # noqa: E501

        Gateway address  # noqa: E501

        :return: The gateway of this ExternalNetworkListStruct.  # noqa: E501
        :rtype: str
        """
        return self._gateway

    @gateway.setter
    def gateway(self, gateway):
        """Sets the gateway of this ExternalNetworkListStruct.

        Gateway address  # noqa: E501

        :param gateway: The gateway of this ExternalNetworkListStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and gateway is None:
            raise ValueError("Invalid value for `gateway`, must not be `None`")  # noqa: E501

        self._gateway = gateway

    @property
    def ips(self):
        """Gets the ips of this ExternalNetworkListStruct.  # noqa: E501

        List of ip addresses  # noqa: E501

        :return: The ips of this ExternalNetworkListStruct.  # noqa: E501
        :rtype: list[IpShort]
        """
        return self._ips

    @ips.setter
    def ips(self, ips):
        """Sets the ips of this ExternalNetworkListStruct.

        List of ip addresses  # noqa: E501

        :param ips: The ips of this ExternalNetworkListStruct.  # noqa: E501
        :type: list[IpShort]
        """
        if self._configuration.client_side_validation and ips is None:
            raise ValueError("Invalid value for `ips`, must not be `None`")  # noqa: E501

        self._ips = ips

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ExternalNetworkListStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExternalNetworkListStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExternalNetworkListStruct):
            return True

        return self.to_dict() != other.to_dict()
