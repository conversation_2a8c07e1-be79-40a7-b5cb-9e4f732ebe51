# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VmachinesVmDetailsStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "name": "str",
        "description": "str",
        "status": "str",
        "image": "VmachinesVmDetailsStructImage",
        "memory": "int",
        "cloudspace": "VmachinesVmDetailsStructCloudspace",
        "creation_time": "int",
        "update_time": "int",
        "vcpus": "int",
        "hypervisor_type": "str",
        "tags": "list[VmachinesTagStruct]",
        "disks": "list[VmachinesDiskStruct]",
        "audits": "list[Audit]",
        "user_access": "list[UserAccessStruct]",
        "group_access": "list[GroupAccessStruct]",
        "nics": "list[VmachinesNicInfo]",
        "node": "VmachinesVmDetailsStructNode",
        "account": "VmachinesVmDetailsStructAccount",
        "devices": "list[VmachinesVmachineDeviceStruct]",
        "location_history": "list[VmachinesVmachineLoacaionHistoryStruct]",
        "backup_policies": "list[VmachinesVMBackupPolicy]",
        "backup_status": "str",
    }

    attribute_map = {
        "id": "id",
        "name": "name",
        "description": "description",
        "status": "status",
        "image": "image",
        "memory": "memory",
        "cloudspace": "cloudspace",
        "creation_time": "creation_time",
        "update_time": "update_time",
        "vcpus": "vcpus",
        "hypervisor_type": "hypervisor_type",
        "tags": "tags",
        "disks": "disks",
        "audits": "audits",
        "user_access": "user_access",
        "group_access": "group_access",
        "nics": "nics",
        "node": "node",
        "account": "account",
        "devices": "devices",
        "location_history": "location_history",
        "backup_policies": "backup_policies",
        "backup_status": "backup_status",
    }

    def __init__(
        self,
        id=None,
        name=None,
        description=None,
        status=None,
        image=None,
        memory=None,
        cloudspace=None,
        creation_time=None,
        update_time=None,
        vcpus=None,
        hypervisor_type=None,
        tags=None,
        disks=None,
        audits=None,
        user_access=None,
        group_access=None,
        nics=None,
        node=None,
        account=None,
        devices=None,
        location_history=None,
        backup_policies=None,
        backup_status=None,
        _configuration=None,
    ):  # noqa: E501
        """VmachinesVmDetailsStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._description = None
        self._status = None
        self._image = None
        self._memory = None
        self._cloudspace = None
        self._creation_time = None
        self._update_time = None
        self._vcpus = None
        self._hypervisor_type = None
        self._tags = None
        self._disks = None
        self._audits = None
        self._user_access = None
        self._group_access = None
        self._nics = None
        self._node = None
        self._account = None
        self._devices = None
        self._location_history = None
        self._backup_policies = None
        self._backup_status = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.description = description
        self.status = status
        self.image = image
        self.memory = memory
        self.cloudspace = cloudspace
        self.creation_time = creation_time
        self.update_time = update_time
        self.vcpus = vcpus
        self.hypervisor_type = hypervisor_type
        self.tags = tags
        self.disks = disks
        self.audits = audits
        self.user_access = user_access
        self.group_access = group_access
        self.nics = nics
        self.node = node
        self.account = account
        self.devices = devices
        self.location_history = location_history
        self.backup_policies = backup_policies
        if backup_status is not None:
            self.backup_status = backup_status

    @property
    def id(self):
        """Gets the id of this VmachinesVmDetailsStruct.  # noqa: E501

        vm id  # noqa: E501

        :return: The id of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VmachinesVmDetailsStruct.

        vm id  # noqa: E501

        :param id: The id of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this VmachinesVmDetailsStruct.  # noqa: E501

        vm name  # noqa: E501

        :return: The name of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VmachinesVmDetailsStruct.

        vm name  # noqa: E501

        :param name: The name of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def description(self):
        """Gets the description of this VmachinesVmDetailsStruct.  # noqa: E501

        vm description  # noqa: E501

        :return: The description of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this VmachinesVmDetailsStruct.

        vm description  # noqa: E501

        :param description: The description of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and description is None:
            raise ValueError("Invalid value for `description`, must not be `None`")  # noqa: E501

        self._description = description

    @property
    def status(self):
        """Gets the status of this VmachinesVmDetailsStruct.  # noqa: E501

        vm status  # noqa: E501

        :return: The status of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VmachinesVmDetailsStruct.

        vm status  # noqa: E501

        :param status: The status of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    @property
    def image(self):
        """Gets the image of this VmachinesVmDetailsStruct.  # noqa: E501


        :return: The image of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: VmachinesVmDetailsStructImage
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this VmachinesVmDetailsStruct.


        :param image: The image of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: VmachinesVmDetailsStructImage
        """
        if self._configuration.client_side_validation and image is None:
            raise ValueError("Invalid value for `image`, must not be `None`")  # noqa: E501

        self._image = image

    @property
    def memory(self):
        """Gets the memory of this VmachinesVmDetailsStruct.  # noqa: E501

        vm memory  # noqa: E501

        :return: The memory of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this VmachinesVmDetailsStruct.

        vm memory  # noqa: E501

        :param memory: The memory of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and memory is None:
            raise ValueError("Invalid value for `memory`, must not be `None`")  # noqa: E501

        self._memory = memory

    @property
    def cloudspace(self):
        """Gets the cloudspace of this VmachinesVmDetailsStruct.  # noqa: E501


        :return: The cloudspace of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: VmachinesVmDetailsStructCloudspace
        """
        return self._cloudspace

    @cloudspace.setter
    def cloudspace(self, cloudspace):
        """Sets the cloudspace of this VmachinesVmDetailsStruct.


        :param cloudspace: The cloudspace of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: VmachinesVmDetailsStructCloudspace
        """
        if self._configuration.client_side_validation and cloudspace is None:
            raise ValueError("Invalid value for `cloudspace`, must not be `None`")  # noqa: E501

        self._cloudspace = cloudspace

    @property
    def creation_time(self):
        """Gets the creation_time of this VmachinesVmDetailsStruct.  # noqa: E501

        creation timestamp  # noqa: E501

        :return: The creation_time of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this VmachinesVmDetailsStruct.

        creation timestamp  # noqa: E501

        :param creation_time: The creation_time of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and creation_time is None:
            raise ValueError("Invalid value for `creation_time`, must not be `None`")  # noqa: E501

        self._creation_time = creation_time

    @property
    def update_time(self):
        """Gets the update_time of this VmachinesVmDetailsStruct.  # noqa: E501

        update timestamp  # noqa: E501

        :return: The update_time of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this VmachinesVmDetailsStruct.

        update timestamp  # noqa: E501

        :param update_time: The update_time of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and update_time is None:
            raise ValueError("Invalid value for `update_time`, must not be `None`")  # noqa: E501

        self._update_time = update_time

    @property
    def vcpus(self):
        """Gets the vcpus of this VmachinesVmDetailsStruct.  # noqa: E501

        vcpus  # noqa: E501

        :return: The vcpus of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._vcpus

    @vcpus.setter
    def vcpus(self, vcpus):
        """Sets the vcpus of this VmachinesVmDetailsStruct.

        vcpus  # noqa: E501

        :param vcpus: The vcpus of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and vcpus is None:
            raise ValueError("Invalid value for `vcpus`, must not be `None`")  # noqa: E501

        self._vcpus = vcpus

    @property
    def hypervisor_type(self):
        """Gets the hypervisor_type of this VmachinesVmDetailsStruct.  # noqa: E501

        vcpus  # noqa: E501

        :return: The hypervisor_type of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._hypervisor_type

    @hypervisor_type.setter
    def hypervisor_type(self, hypervisor_type):
        """Sets the hypervisor_type of this VmachinesVmDetailsStruct.

        vcpus  # noqa: E501

        :param hypervisor_type: The hypervisor_type of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and hypervisor_type is None:
            raise ValueError("Invalid value for `hypervisor_type`, must not be `None`")  # noqa: E501
        allowed_values = ["VMWARE"]  # noqa: E501
        if self._configuration.client_side_validation and hypervisor_type not in allowed_values:
            raise ValueError(
                "Invalid value for `hypervisor_type` ({0}), must be one of {1}".format(  # noqa: E501
                    hypervisor_type, allowed_values
                )
            )

        self._hypervisor_type = hypervisor_type

    @property
    def tags(self):
        """Gets the tags of this VmachinesVmDetailsStruct.  # noqa: E501

        tags  # noqa: E501

        :return: The tags of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[VmachinesTagStruct]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this VmachinesVmDetailsStruct.

        tags  # noqa: E501

        :param tags: The tags of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[VmachinesTagStruct]
        """
        if self._configuration.client_side_validation and tags is None:
            raise ValueError("Invalid value for `tags`, must not be `None`")  # noqa: E501

        self._tags = tags

    @property
    def disks(self):
        """Gets the disks of this VmachinesVmDetailsStruct.  # noqa: E501

        tags  # noqa: E501

        :return: The disks of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[VmachinesDiskStruct]
        """
        return self._disks

    @disks.setter
    def disks(self, disks):
        """Sets the disks of this VmachinesVmDetailsStruct.

        tags  # noqa: E501

        :param disks: The disks of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[VmachinesDiskStruct]
        """
        if self._configuration.client_side_validation and disks is None:
            raise ValueError("Invalid value for `disks`, must not be `None`")  # noqa: E501

        self._disks = disks

    @property
    def audits(self):
        """Gets the audits of this VmachinesVmDetailsStruct.  # noqa: E501

        list of audits for this vm  # noqa: E501

        :return: The audits of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[Audit]
        """
        return self._audits

    @audits.setter
    def audits(self, audits):
        """Sets the audits of this VmachinesVmDetailsStruct.

        list of audits for this vm  # noqa: E501

        :param audits: The audits of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[Audit]
        """
        if self._configuration.client_side_validation and audits is None:
            raise ValueError("Invalid value for `audits`, must not be `None`")  # noqa: E501

        self._audits = audits

    @property
    def user_access(self):
        """Gets the user_access of this VmachinesVmDetailsStruct.  # noqa: E501

        User access rights  # noqa: E501

        :return: The user_access of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[UserAccessStruct]
        """
        return self._user_access

    @user_access.setter
    def user_access(self, user_access):
        """Sets the user_access of this VmachinesVmDetailsStruct.

        User access rights  # noqa: E501

        :param user_access: The user_access of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[UserAccessStruct]
        """
        if self._configuration.client_side_validation and user_access is None:
            raise ValueError("Invalid value for `user_access`, must not be `None`")  # noqa: E501

        self._user_access = user_access

    @property
    def group_access(self):
        """Gets the group_access of this VmachinesVmDetailsStruct.  # noqa: E501

        Group access rights  # noqa: E501

        :return: The group_access of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[GroupAccessStruct]
        """
        return self._group_access

    @group_access.setter
    def group_access(self, group_access):
        """Sets the group_access of this VmachinesVmDetailsStruct.

        Group access rights  # noqa: E501

        :param group_access: The group_access of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[GroupAccessStruct]
        """
        if self._configuration.client_side_validation and group_access is None:
            raise ValueError("Invalid value for `group_access`, must not be `None`")  # noqa: E501

        self._group_access = group_access

    @property
    def nics(self):
        """Gets the nics of this VmachinesVmDetailsStruct.  # noqa: E501

        list of nics for this vm  # noqa: E501

        :return: The nics of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[VmachinesNicInfo]
        """
        return self._nics

    @nics.setter
    def nics(self, nics):
        """Sets the nics of this VmachinesVmDetailsStruct.

        list of nics for this vm  # noqa: E501

        :param nics: The nics of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[VmachinesNicInfo]
        """
        if self._configuration.client_side_validation and nics is None:
            raise ValueError("Invalid value for `nics`, must not be `None`")  # noqa: E501

        self._nics = nics

    @property
    def node(self):
        """Gets the node of this VmachinesVmDetailsStruct.  # noqa: E501


        :return: The node of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: VmachinesVmDetailsStructNode
        """
        return self._node

    @node.setter
    def node(self, node):
        """Sets the node of this VmachinesVmDetailsStruct.


        :param node: The node of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: VmachinesVmDetailsStructNode
        """
        if self._configuration.client_side_validation and node is None:
            raise ValueError("Invalid value for `node`, must not be `None`")  # noqa: E501

        self._node = node

    @property
    def account(self):
        """Gets the account of this VmachinesVmDetailsStruct.  # noqa: E501


        :return: The account of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: VmachinesVmDetailsStructAccount
        """
        return self._account

    @account.setter
    def account(self, account):
        """Sets the account of this VmachinesVmDetailsStruct.


        :param account: The account of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: VmachinesVmDetailsStructAccount
        """
        if self._configuration.client_side_validation and account is None:
            raise ValueError("Invalid value for `account`, must not be `None`")  # noqa: E501

        self._account = account

    @property
    def devices(self):
        """Gets the devices of this VmachinesVmDetailsStruct.  # noqa: E501

        Devices list  # noqa: E501

        :return: The devices of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[VmachinesVmachineDeviceStruct]
        """
        return self._devices

    @devices.setter
    def devices(self, devices):
        """Sets the devices of this VmachinesVmDetailsStruct.

        Devices list  # noqa: E501

        :param devices: The devices of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[VmachinesVmachineDeviceStruct]
        """
        if self._configuration.client_side_validation and devices is None:
            raise ValueError("Invalid value for `devices`, must not be `None`")  # noqa: E501

        self._devices = devices

    @property
    def location_history(self):
        """Gets the location_history of this VmachinesVmDetailsStruct.  # noqa: E501

        Location history  # noqa: E501

        :return: The location_history of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[VmachinesVmachineLoacaionHistoryStruct]
        """
        return self._location_history

    @location_history.setter
    def location_history(self, location_history):
        """Sets the location_history of this VmachinesVmDetailsStruct.

        Location history  # noqa: E501

        :param location_history: The location_history of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[VmachinesVmachineLoacaionHistoryStruct]
        """
        if self._configuration.client_side_validation and location_history is None:
            raise ValueError("Invalid value for `location_history`, must not be `None`")  # noqa: E501

        self._location_history = location_history

    @property
    def backup_policies(self):
        """Gets the backup_policies of this VmachinesVmDetailsStruct.  # noqa: E501

        Backup policies  # noqa: E501

        :return: The backup_policies of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: list[VmachinesVMBackupPolicy]
        """
        return self._backup_policies

    @backup_policies.setter
    def backup_policies(self, backup_policies):
        """Sets the backup_policies of this VmachinesVmDetailsStruct.

        Backup policies  # noqa: E501

        :param backup_policies: The backup_policies of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: list[VmachinesVMBackupPolicy]
        """
        if self._configuration.client_side_validation and backup_policies is None:
            raise ValueError("Invalid value for `backup_policies`, must not be `None`")  # noqa: E501

        self._backup_policies = backup_policies

    @property
    def backup_status(self):
        """Gets the backup_status of this VmachinesVmDetailsStruct.  # noqa: E501

        latest backup status  # noqa: E501

        :return: The backup_status of this VmachinesVmDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._backup_status

    @backup_status.setter
    def backup_status(self, backup_status):
        """Sets the backup_status of this VmachinesVmDetailsStruct.

        latest backup status  # noqa: E501

        :param backup_status: The backup_status of this VmachinesVmDetailsStruct.  # noqa: E501
        :type: str
        """

        self._backup_status = backup_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VmachinesVmDetailsStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VmachinesVmDetailsStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VmachinesVmDetailsStruct):
            return True

        return self.to_dict() != other.to_dict()
