# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class NodeDetailsNetAddress(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"cidr": "list[str]", "index": "int", "ip": "list[str]", "mac": "str", "mtu": "int", "name": "str"}

    attribute_map = {"cidr": "cidr", "index": "index", "ip": "ip", "mac": "mac", "mtu": "mtu", "name": "name"}

    def __init__(
        self, cidr=None, index=None, ip=None, mac=None, mtu=None, name=None, _configuration=None
    ):  # noqa: E501
        """NodeDetailsNetAddress - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cidr = None
        self._index = None
        self._ip = None
        self._mac = None
        self._mtu = None
        self._name = None
        self.discriminator = None

        if cidr is not None:
            self.cidr = cidr
        if index is not None:
            self.index = index
        if ip is not None:
            self.ip = ip
        if mac is not None:
            self.mac = mac
        if mtu is not None:
            self.mtu = mtu
        if name is not None:
            self.name = name

    @property
    def cidr(self):
        """Gets the cidr of this NodeDetailsNetAddress.  # noqa: E501

        Classless inter-domain routing for the interface  # noqa: E501

        :return: The cidr of this NodeDetailsNetAddress.  # noqa: E501
        :rtype: list[str]
        """
        return self._cidr

    @cidr.setter
    def cidr(self, cidr):
        """Sets the cidr of this NodeDetailsNetAddress.

        Classless inter-domain routing for the interface  # noqa: E501

        :param cidr: The cidr of this NodeDetailsNetAddress.  # noqa: E501
        :type: list[str]
        """

        self._cidr = cidr

    @property
    def index(self):
        """Gets the index of this NodeDetailsNetAddress.  # noqa: E501

        An integer id for the network address  # noqa: E501

        :return: The index of this NodeDetailsNetAddress.  # noqa: E501
        :rtype: int
        """
        return self._index

    @index.setter
    def index(self, index):
        """Sets the index of this NodeDetailsNetAddress.

        An integer id for the network address  # noqa: E501

        :param index: The index of this NodeDetailsNetAddress.  # noqa: E501
        :type: int
        """

        self._index = index

    @property
    def ip(self):
        """Gets the ip of this NodeDetailsNetAddress.  # noqa: E501

        list of ip addresses for the interface  # noqa: E501

        :return: The ip of this NodeDetailsNetAddress.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this NodeDetailsNetAddress.

        list of ip addresses for the interface  # noqa: E501

        :param ip: The ip of this NodeDetailsNetAddress.  # noqa: E501
        :type: list[str]
        """

        self._ip = ip

    @property
    def mac(self):
        """Gets the mac of this NodeDetailsNetAddress.  # noqa: E501

        Mac address for the interface  # noqa: E501

        :return: The mac of this NodeDetailsNetAddress.  # noqa: E501
        :rtype: str
        """
        return self._mac

    @mac.setter
    def mac(self, mac):
        """Sets the mac of this NodeDetailsNetAddress.

        Mac address for the interface  # noqa: E501

        :param mac: The mac of this NodeDetailsNetAddress.  # noqa: E501
        :type: str
        """

        self._mac = mac

    @property
    def mtu(self):
        """Gets the mtu of this NodeDetailsNetAddress.  # noqa: E501

        The maximum transmission unit (MTU) of the network interface  # noqa: E501

        :return: The mtu of this NodeDetailsNetAddress.  # noqa: E501
        :rtype: int
        """
        return self._mtu

    @mtu.setter
    def mtu(self, mtu):
        """Sets the mtu of this NodeDetailsNetAddress.

        The maximum transmission unit (MTU) of the network interface  # noqa: E501

        :param mtu: The mtu of this NodeDetailsNetAddress.  # noqa: E501
        :type: int
        """

        self._mtu = mtu

    @property
    def name(self):
        """Gets the name of this NodeDetailsNetAddress.  # noqa: E501

        Network interface name  # noqa: E501

        :return: The name of this NodeDetailsNetAddress.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this NodeDetailsNetAddress.

        Network interface name  # noqa: E501

        :param name: The name of this NodeDetailsNetAddress.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(NodeDetailsNetAddress, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeDetailsNetAddress):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeDetailsNetAddress):
            return True

        return self.to_dict() != other.to_dict()
