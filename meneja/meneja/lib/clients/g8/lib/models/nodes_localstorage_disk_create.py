# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class NodesLocalstorageDiskCreate(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"node_id": "int", "disk_id": "str"}

    attribute_map = {"node_id": "node_id", "disk_id": "disk_id"}

    def __init__(self, node_id=None, disk_id=None, _configuration=None):  # noqa: E501
        """NodesLocalstorageDiskCreate - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._node_id = None
        self._disk_id = None
        self.discriminator = None

        self.node_id = node_id
        self.disk_id = disk_id

    @property
    def node_id(self):
        """Gets the node_id of this NodesLocalstorageDiskCreate.  # noqa: E501

        Node id that contains the localstorage  # noqa: E501

        :return: The node_id of this NodesLocalstorageDiskCreate.  # noqa: E501
        :rtype: int
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this NodesLocalstorageDiskCreate.

        Node id that contains the localstorage  # noqa: E501

        :param node_id: The node_id of this NodesLocalstorageDiskCreate.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and node_id is None:
            raise ValueError("Invalid value for `node_id`, must not be `None`")  # noqa: E501

        self._node_id = node_id

    @property
    def disk_id(self):
        """Gets the disk_id of this NodesLocalstorageDiskCreate.  # noqa: E501

        Disk id as in `/dev/disk/bi-id/<disk-id>`  # noqa: E501

        :return: The disk_id of this NodesLocalstorageDiskCreate.  # noqa: E501
        :rtype: str
        """
        return self._disk_id

    @disk_id.setter
    def disk_id(self, disk_id):
        """Sets the disk_id of this NodesLocalstorageDiskCreate.

        Disk id as in `/dev/disk/bi-id/<disk-id>`  # noqa: E501

        :param disk_id: The disk_id of this NodesLocalstorageDiskCreate.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and disk_id is None:
            raise ValueError("Invalid value for `disk_id`, must not be `None`")  # noqa: E501

        self._disk_id = disk_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(NodesLocalstorageDiskCreate, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodesLocalstorageDiskCreate):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodesLocalstorageDiskCreate):
            return True

        return self.to_dict() != other.to_dict()
