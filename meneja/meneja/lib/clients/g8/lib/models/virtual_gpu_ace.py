# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VirtualGpuACE(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "explicit": "bool",
        "guid": "str",
        "right": "str",
        "status": "str",
        "type": "str",
        "user_group_id": "str",
    }

    attribute_map = {
        "explicit": "explicit",
        "guid": "guid",
        "right": "right",
        "status": "status",
        "type": "type",
        "user_group_id": "user_group_id",
    }

    def __init__(
        self, explicit=None, guid=None, right=None, status=None, type=None, user_group_id=None, _configuration=None
    ):  # noqa: E501
        """VirtualGpuACE - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._explicit = None
        self._guid = None
        self._right = None
        self._status = None
        self._type = None
        self._user_group_id = None
        self.discriminator = None

        if explicit is not None:
            self.explicit = explicit
        if guid is not None:
            self.guid = guid
        if right is not None:
            self.right = right
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type
        if user_group_id is not None:
            self.user_group_id = user_group_id

    @property
    def explicit(self):
        """Gets the explicit of this VirtualGpuACE.  # noqa: E501

        True when the ACE is granted by the system as the result of an access grant to a child object.  # noqa: E501

        :return: The explicit of this VirtualGpuACE.  # noqa: E501
        :rtype: bool
        """
        return self._explicit

    @explicit.setter
    def explicit(self, explicit):
        """Sets the explicit of this VirtualGpuACE.

        True when the ACE is granted by the system as the result of an access grant to a child object.  # noqa: E501

        :param explicit: The explicit of this VirtualGpuACE.  # noqa: E501
        :type: bool
        """

        self._explicit = explicit

    @property
    def guid(self):
        """Gets the guid of this VirtualGpuACE.  # noqa: E501

        Globally unique ACE identifier  # noqa: E501

        :return: The guid of this VirtualGpuACE.  # noqa: E501
        :rtype: str
        """
        return self._guid

    @guid.setter
    def guid(self, guid):
        """Sets the guid of this VirtualGpuACE.

        Globally unique ACE identifier  # noqa: E501

        :param guid: The guid of this VirtualGpuACE.  # noqa: E501
        :type: str
        """

        self._guid = guid

    @property
    def right(self):
        """Gets the right of this VirtualGpuACE.  # noqa: E501

        Access right definition  # noqa: E501

        :return: The right of this VirtualGpuACE.  # noqa: E501
        :rtype: str
        """
        return self._right

    @right.setter
    def right(self, right):
        """Sets the right of this VirtualGpuACE.

        Access right definition  # noqa: E501

        :param right: The right of this VirtualGpuACE.  # noqa: E501
        :type: str
        """

        self._right = right

    @property
    def status(self):
        """Gets the status of this VirtualGpuACE.  # noqa: E501

        Status of acccess control list  # noqa: E501

        :return: The status of this VirtualGpuACE.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VirtualGpuACE.

        Status of acccess control list  # noqa: E501

        :param status: The status of this VirtualGpuACE.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this VirtualGpuACE.  # noqa: E501

        Access control entry type [USER/GROUP]  # noqa: E501

        :return: The type of this VirtualGpuACE.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this VirtualGpuACE.

        Access control entry type [USER/GROUP]  # noqa: E501

        :param type: The type of this VirtualGpuACE.  # noqa: E501
        :type: str
        """
        allowed_values = ["U", "G"]  # noqa: E501
        if self._configuration.client_side_validation and type not in allowed_values:
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}".format(type, allowed_values)  # noqa: E501
            )

        self._type = type

    @property
    def user_group_id(self):
        """Gets the user_group_id of this VirtualGpuACE.  # noqa: E501

        ?  # noqa: E501

        :return: The user_group_id of this VirtualGpuACE.  # noqa: E501
        :rtype: str
        """
        return self._user_group_id

    @user_group_id.setter
    def user_group_id(self, user_group_id):
        """Sets the user_group_id of this VirtualGpuACE.

        ?  # noqa: E501

        :param user_group_id: The user_group_id of this VirtualGpuACE.  # noqa: E501
        :type: str
        """

        self._user_group_id = user_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VirtualGpuACE, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VirtualGpuACE):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VirtualGpuACE):
            return True

        return self.to_dict() != other.to_dict()
