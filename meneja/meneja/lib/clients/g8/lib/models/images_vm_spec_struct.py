# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ImagesVmSpecStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "minimum_memory_size": "int",
        "maximum_memory_size": "int",
        "hot_add_memory": "bool",
        "hot_add_vcu": "bool",
        "hot_add_disks": "bool",
        "hot_add_nics": "bool",
        "hot_remove_vcu": "bool",
        "hot_remove_disks": "bool",
        "hot_remove_nics": "bool",
    }

    attribute_map = {
        "minimum_memory_size": "minimum_memory_size",
        "maximum_memory_size": "maximum_memory_size",
        "hot_add_memory": "hot_add_memory",
        "hot_add_vcu": "hot_add_vcu",
        "hot_add_disks": "hot_add_disks",
        "hot_add_nics": "hot_add_nics",
        "hot_remove_vcu": "hot_remove_vcu",
        "hot_remove_disks": "hot_remove_disks",
        "hot_remove_nics": "hot_remove_nics",
    }

    def __init__(
        self,
        minimum_memory_size=None,
        maximum_memory_size=None,
        hot_add_memory=None,
        hot_add_vcu=None,
        hot_add_disks=None,
        hot_add_nics=None,
        hot_remove_vcu=None,
        hot_remove_disks=None,
        hot_remove_nics=None,
        _configuration=None,
    ):  # noqa: E501
        """ImagesVmSpecStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._minimum_memory_size = None
        self._maximum_memory_size = None
        self._hot_add_memory = None
        self._hot_add_vcu = None
        self._hot_add_disks = None
        self._hot_add_nics = None
        self._hot_remove_vcu = None
        self._hot_remove_disks = None
        self._hot_remove_nics = None
        self.discriminator = None

        self.minimum_memory_size = minimum_memory_size
        self.maximum_memory_size = maximum_memory_size
        self.hot_add_memory = hot_add_memory
        self.hot_add_vcu = hot_add_vcu
        self.hot_add_disks = hot_add_disks
        self.hot_add_nics = hot_add_nics
        self.hot_remove_vcu = hot_remove_vcu
        self.hot_remove_disks = hot_remove_disks
        self.hot_remove_nics = hot_remove_nics

    @property
    def minimum_memory_size(self):
        """Gets the minimum_memory_size of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The minimum_memory_size of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: int
        """
        return self._minimum_memory_size

    @minimum_memory_size.setter
    def minimum_memory_size(self, minimum_memory_size):
        """Sets the minimum_memory_size of this ImagesVmSpecStruct.

          # noqa: E501

        :param minimum_memory_size: The minimum_memory_size of this ImagesVmSpecStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and minimum_memory_size is None:
            raise ValueError("Invalid value for `minimum_memory_size`, must not be `None`")  # noqa: E501

        self._minimum_memory_size = minimum_memory_size

    @property
    def maximum_memory_size(self):
        """Gets the maximum_memory_size of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The maximum_memory_size of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: int
        """
        return self._maximum_memory_size

    @maximum_memory_size.setter
    def maximum_memory_size(self, maximum_memory_size):
        """Sets the maximum_memory_size of this ImagesVmSpecStruct.

          # noqa: E501

        :param maximum_memory_size: The maximum_memory_size of this ImagesVmSpecStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and maximum_memory_size is None:
            raise ValueError("Invalid value for `maximum_memory_size`, must not be `None`")  # noqa: E501

        self._maximum_memory_size = maximum_memory_size

    @property
    def hot_add_memory(self):
        """Gets the hot_add_memory of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The hot_add_memory of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: bool
        """
        return self._hot_add_memory

    @hot_add_memory.setter
    def hot_add_memory(self, hot_add_memory):
        """Sets the hot_add_memory of this ImagesVmSpecStruct.

          # noqa: E501

        :param hot_add_memory: The hot_add_memory of this ImagesVmSpecStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and hot_add_memory is None:
            raise ValueError("Invalid value for `hot_add_memory`, must not be `None`")  # noqa: E501

        self._hot_add_memory = hot_add_memory

    @property
    def hot_add_vcu(self):
        """Gets the hot_add_vcu of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The hot_add_vcu of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: bool
        """
        return self._hot_add_vcu

    @hot_add_vcu.setter
    def hot_add_vcu(self, hot_add_vcu):
        """Sets the hot_add_vcu of this ImagesVmSpecStruct.

          # noqa: E501

        :param hot_add_vcu: The hot_add_vcu of this ImagesVmSpecStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and hot_add_vcu is None:
            raise ValueError("Invalid value for `hot_add_vcu`, must not be `None`")  # noqa: E501

        self._hot_add_vcu = hot_add_vcu

    @property
    def hot_add_disks(self):
        """Gets the hot_add_disks of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The hot_add_disks of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: bool
        """
        return self._hot_add_disks

    @hot_add_disks.setter
    def hot_add_disks(self, hot_add_disks):
        """Sets the hot_add_disks of this ImagesVmSpecStruct.

          # noqa: E501

        :param hot_add_disks: The hot_add_disks of this ImagesVmSpecStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and hot_add_disks is None:
            raise ValueError("Invalid value for `hot_add_disks`, must not be `None`")  # noqa: E501

        self._hot_add_disks = hot_add_disks

    @property
    def hot_add_nics(self):
        """Gets the hot_add_nics of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The hot_add_nics of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: bool
        """
        return self._hot_add_nics

    @hot_add_nics.setter
    def hot_add_nics(self, hot_add_nics):
        """Sets the hot_add_nics of this ImagesVmSpecStruct.

          # noqa: E501

        :param hot_add_nics: The hot_add_nics of this ImagesVmSpecStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and hot_add_nics is None:
            raise ValueError("Invalid value for `hot_add_nics`, must not be `None`")  # noqa: E501

        self._hot_add_nics = hot_add_nics

    @property
    def hot_remove_vcu(self):
        """Gets the hot_remove_vcu of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The hot_remove_vcu of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: bool
        """
        return self._hot_remove_vcu

    @hot_remove_vcu.setter
    def hot_remove_vcu(self, hot_remove_vcu):
        """Sets the hot_remove_vcu of this ImagesVmSpecStruct.

          # noqa: E501

        :param hot_remove_vcu: The hot_remove_vcu of this ImagesVmSpecStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and hot_remove_vcu is None:
            raise ValueError("Invalid value for `hot_remove_vcu`, must not be `None`")  # noqa: E501

        self._hot_remove_vcu = hot_remove_vcu

    @property
    def hot_remove_disks(self):
        """Gets the hot_remove_disks of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The hot_remove_disks of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: bool
        """
        return self._hot_remove_disks

    @hot_remove_disks.setter
    def hot_remove_disks(self, hot_remove_disks):
        """Sets the hot_remove_disks of this ImagesVmSpecStruct.

          # noqa: E501

        :param hot_remove_disks: The hot_remove_disks of this ImagesVmSpecStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and hot_remove_disks is None:
            raise ValueError("Invalid value for `hot_remove_disks`, must not be `None`")  # noqa: E501

        self._hot_remove_disks = hot_remove_disks

    @property
    def hot_remove_nics(self):
        """Gets the hot_remove_nics of this ImagesVmSpecStruct.  # noqa: E501

          # noqa: E501

        :return: The hot_remove_nics of this ImagesVmSpecStruct.  # noqa: E501
        :rtype: bool
        """
        return self._hot_remove_nics

    @hot_remove_nics.setter
    def hot_remove_nics(self, hot_remove_nics):
        """Sets the hot_remove_nics of this ImagesVmSpecStruct.

          # noqa: E501

        :param hot_remove_nics: The hot_remove_nics of this ImagesVmSpecStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and hot_remove_nics is None:
            raise ValueError("Invalid value for `hot_remove_nics`, must not be `None`")  # noqa: E501

        self._hot_remove_nics = hot_remove_nics

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ImagesVmSpecStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ImagesVmSpecStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ImagesVmSpecStruct):
            return True

        return self.to_dict() != other.to_dict()
