# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class PortForwardStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "protocol": "str",
        "external": "PortForwardStructExternal",
        "internal": "PortForwardStructInternal",
    }

    attribute_map = {"protocol": "protocol", "external": "external", "internal": "internal"}

    def __init__(self, protocol=None, external=None, internal=None, _configuration=None):  # noqa: E501
        """PortForwardStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._protocol = None
        self._external = None
        self._internal = None
        self.discriminator = None

        self.protocol = protocol
        self.external = external
        self.internal = internal

    @property
    def protocol(self):
        """Gets the protocol of this PortForwardStruct.  # noqa: E501

        Port forward protocol  # noqa: E501

        :return: The protocol of this PortForwardStruct.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this PortForwardStruct.

        Port forward protocol  # noqa: E501

        :param protocol: The protocol of this PortForwardStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and protocol is None:
            raise ValueError("Invalid value for `protocol`, must not be `None`")  # noqa: E501

        self._protocol = protocol

    @property
    def external(self):
        """Gets the external of this PortForwardStruct.  # noqa: E501


        :return: The external of this PortForwardStruct.  # noqa: E501
        :rtype: PortForwardStructExternal
        """
        return self._external

    @external.setter
    def external(self, external):
        """Sets the external of this PortForwardStruct.


        :param external: The external of this PortForwardStruct.  # noqa: E501
        :type: PortForwardStructExternal
        """
        if self._configuration.client_side_validation and external is None:
            raise ValueError("Invalid value for `external`, must not be `None`")  # noqa: E501

        self._external = external

    @property
    def internal(self):
        """Gets the internal of this PortForwardStruct.  # noqa: E501


        :return: The internal of this PortForwardStruct.  # noqa: E501
        :rtype: PortForwardStructInternal
        """
        return self._internal

    @internal.setter
    def internal(self, internal):
        """Sets the internal of this PortForwardStruct.


        :param internal: The internal of this PortForwardStruct.  # noqa: E501
        :type: PortForwardStructInternal
        """
        if self._configuration.client_side_validation and internal is None:
            raise ValueError("Invalid value for `internal`, must not be `None`")  # noqa: E501

        self._internal = internal

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(PortForwardStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PortForwardStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PortForwardStruct):
            return True

        return self.to_dict() != other.to_dict()
