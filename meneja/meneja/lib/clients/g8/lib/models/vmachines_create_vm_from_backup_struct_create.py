# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VmachinesCreateVMFromBackupStructCREATE(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "target_id": "int",
        "backup_id": "str",
        "cloudspace_id": "int",
        "name": "str",
        "description": "str",
        "hostname": "str",
        "private_ip": "str",
        "tpm_secret": "str",
    }

    attribute_map = {
        "target_id": "target_id",
        "backup_id": "backup_id",
        "cloudspace_id": "cloudspace_id",
        "name": "name",
        "description": "description",
        "hostname": "hostname",
        "private_ip": "private_ip",
        "tpm_secret": "tpm_secret",
    }

    def __init__(
        self,
        target_id=None,
        backup_id=None,
        cloudspace_id=None,
        name=None,
        description=None,
        hostname=None,
        private_ip=None,
        tpm_secret=None,
        _configuration=None,
    ):  # noqa: E501
        """VmachinesCreateVMFromBackupStructCREATE - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._target_id = None
        self._backup_id = None
        self._cloudspace_id = None
        self._name = None
        self._description = None
        self._hostname = None
        self._private_ip = None
        self._tpm_secret = None
        self.discriminator = None

        self.target_id = target_id
        self.backup_id = backup_id
        self.cloudspace_id = cloudspace_id
        if name is not None:
            self.name = name
        if description is not None:
            self.description = description
        if hostname is not None:
            self.hostname = hostname
        if private_ip is not None:
            self.private_ip = private_ip
        if tpm_secret is not None:
            self.tpm_secret = tpm_secret

    @property
    def target_id(self):
        """Gets the target_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501

        backup target id  # noqa: E501

        :return: The target_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :rtype: int
        """
        return self._target_id

    @target_id.setter
    def target_id(self, target_id):
        """Sets the target_id of this VmachinesCreateVMFromBackupStructCREATE.

        backup target id  # noqa: E501

        :param target_id: The target_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and target_id is None:
            raise ValueError("Invalid value for `target_id`, must not be `None`")  # noqa: E501

        self._target_id = target_id

    @property
    def backup_id(self):
        """Gets the backup_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501

        backup id  # noqa: E501

        :return: The backup_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :rtype: str
        """
        return self._backup_id

    @backup_id.setter
    def backup_id(self, backup_id):
        """Sets the backup_id of this VmachinesCreateVMFromBackupStructCREATE.

        backup id  # noqa: E501

        :param backup_id: The backup_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and backup_id is None:
            raise ValueError("Invalid value for `backup_id`, must not be `None`")  # noqa: E501

        self._backup_id = backup_id

    @property
    def cloudspace_id(self):
        """Gets the cloudspace_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501

        cloudspace id  # noqa: E501

        :return: The cloudspace_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :rtype: int
        """
        return self._cloudspace_id

    @cloudspace_id.setter
    def cloudspace_id(self, cloudspace_id):
        """Sets the cloudspace_id of this VmachinesCreateVMFromBackupStructCREATE.

        cloudspace id  # noqa: E501

        :param cloudspace_id: The cloudspace_id of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and cloudspace_id is None:
            raise ValueError("Invalid value for `cloudspace_id`, must not be `None`")  # noqa: E501

        self._cloudspace_id = cloudspace_id

    @property
    def name(self):
        """Gets the name of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501

        machine name  # noqa: E501

        :return: The name of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VmachinesCreateVMFromBackupStructCREATE.

        machine name  # noqa: E501

        :param name: The name of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def description(self):
        """Gets the description of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501

        machine description  # noqa: E501

        :return: The description of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this VmachinesCreateVMFromBackupStructCREATE.

        machine description  # noqa: E501

        :param description: The description of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def hostname(self):
        """Gets the hostname of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501

        machine hostname  # noqa: E501

        :return: The hostname of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this VmachinesCreateVMFromBackupStructCREATE.

        machine hostname  # noqa: E501

        :param hostname: The hostname of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def private_ip(self):
        """Gets the private_ip of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501

        machine private ip  # noqa: E501

        :return: The private_ip of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :rtype: str
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this VmachinesCreateVMFromBackupStructCREATE.

        machine private ip  # noqa: E501

        :param private_ip: The private_ip of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :type: str
        """

        self._private_ip = private_ip

    @property
    def tpm_secret(self):
        """Gets the tpm_secret of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501

        tpm secret  # noqa: E501

        :return: The tpm_secret of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :rtype: str
        """
        return self._tpm_secret

    @tpm_secret.setter
    def tpm_secret(self, tpm_secret):
        """Sets the tpm_secret of this VmachinesCreateVMFromBackupStructCREATE.

        tpm secret  # noqa: E501

        :param tpm_secret: The tpm_secret of this VmachinesCreateVMFromBackupStructCREATE.  # noqa: E501
        :type: str
        """

        self._tpm_secret = tpm_secret

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VmachinesCreateVMFromBackupStructCREATE, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VmachinesCreateVMFromBackupStructCREATE):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VmachinesCreateVMFromBackupStructCREATE):
            return True

        return self.to_dict() != other.to_dict()
