# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ObjectspacesObjectSpaceAccessStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"name": "str", "access_type": "str", "access_key": "str", "secret_key": "str"}

    attribute_map = {
        "name": "name",
        "access_type": "access_type",
        "access_key": "access_key",
        "secret_key": "secret_key",
    }

    def __init__(
        self, name=None, access_type=None, access_key=None, secret_key=None, _configuration=None
    ):  # noqa: E501
        """ObjectspacesObjectSpaceAccessStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._access_type = None
        self._access_key = None
        self._secret_key = None
        self.discriminator = None

        self.name = name
        self.access_type = access_type
        self.access_key = access_key
        self.secret_key = secret_key

    @property
    def name(self):
        """Gets the name of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501

        access name  # noqa: E501

        :return: The name of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ObjectspacesObjectSpaceAccessStruct.

        access name  # noqa: E501

        :param name: The name of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def access_type(self):
        """Gets the access_type of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501

        access type  # noqa: E501

        :return: The access_type of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501
        :rtype: str
        """
        return self._access_type

    @access_type.setter
    def access_type(self, access_type):
        """Sets the access_type of this ObjectspacesObjectSpaceAccessStruct.

        access type  # noqa: E501

        :param access_type: The access_type of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and access_type is None:
            raise ValueError("Invalid value for `access_type`, must not be `None`")  # noqa: E501
        allowed_values = ["r", "w", "rw"]  # noqa: E501
        if self._configuration.client_side_validation and access_type not in allowed_values:
            raise ValueError(
                "Invalid value for `access_type` ({0}), must be one of {1}".format(  # noqa: E501
                    access_type, allowed_values
                )
            )

        self._access_type = access_type

    @property
    def access_key(self):
        """Gets the access_key of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501

        access key  # noqa: E501

        :return: The access_key of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501
        :rtype: str
        """
        return self._access_key

    @access_key.setter
    def access_key(self, access_key):
        """Sets the access_key of this ObjectspacesObjectSpaceAccessStruct.

        access key  # noqa: E501

        :param access_key: The access_key of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and access_key is None:
            raise ValueError("Invalid value for `access_key`, must not be `None`")  # noqa: E501

        self._access_key = access_key

    @property
    def secret_key(self):
        """Gets the secret_key of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501

        access secret  # noqa: E501

        :return: The secret_key of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501
        :rtype: str
        """
        return self._secret_key

    @secret_key.setter
    def secret_key(self, secret_key):
        """Sets the secret_key of this ObjectspacesObjectSpaceAccessStruct.

        access secret  # noqa: E501

        :param secret_key: The secret_key of this ObjectspacesObjectSpaceAccessStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and secret_key is None:
            raise ValueError("Invalid value for `secret_key`, must not be `None`")  # noqa: E501

        self._secret_key = secret_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ObjectspacesObjectSpaceAccessStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ObjectspacesObjectSpaceAccessStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ObjectspacesObjectSpaceAccessStruct):
            return True

        return self.to_dict() != other.to_dict()
