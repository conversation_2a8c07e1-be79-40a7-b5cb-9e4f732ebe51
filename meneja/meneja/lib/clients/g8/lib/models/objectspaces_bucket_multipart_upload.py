# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ObjectspacesBucketMultipartUpload(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"upload_id": "str", "key": "str", "initiated": "str"}

    attribute_map = {"upload_id": "UploadId", "key": "Key", "initiated": "Initiated"}

    def __init__(self, upload_id=None, key=None, initiated=None, _configuration=None):  # noqa: E501
        """ObjectspacesBucketMultipartUpload - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._upload_id = None
        self._key = None
        self._initiated = None
        self.discriminator = None

        self.upload_id = upload_id
        self.key = key
        self.initiated = initiated

    @property
    def upload_id(self):
        """Gets the upload_id of this ObjectspacesBucketMultipartUpload.  # noqa: E501

        upload id  # noqa: E501

        :return: The upload_id of this ObjectspacesBucketMultipartUpload.  # noqa: E501
        :rtype: str
        """
        return self._upload_id

    @upload_id.setter
    def upload_id(self, upload_id):
        """Sets the upload_id of this ObjectspacesBucketMultipartUpload.

        upload id  # noqa: E501

        :param upload_id: The upload_id of this ObjectspacesBucketMultipartUpload.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and upload_id is None:
            raise ValueError("Invalid value for `upload_id`, must not be `None`")  # noqa: E501

        self._upload_id = upload_id

    @property
    def key(self):
        """Gets the key of this ObjectspacesBucketMultipartUpload.  # noqa: E501

        object key  # noqa: E501

        :return: The key of this ObjectspacesBucketMultipartUpload.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this ObjectspacesBucketMultipartUpload.

        object key  # noqa: E501

        :param key: The key of this ObjectspacesBucketMultipartUpload.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and key is None:
            raise ValueError("Invalid value for `key`, must not be `None`")  # noqa: E501

        self._key = key

    @property
    def initiated(self):
        """Gets the initiated of this ObjectspacesBucketMultipartUpload.  # noqa: E501

        when the upload is initiated  # noqa: E501

        :return: The initiated of this ObjectspacesBucketMultipartUpload.  # noqa: E501
        :rtype: str
        """
        return self._initiated

    @initiated.setter
    def initiated(self, initiated):
        """Sets the initiated of this ObjectspacesBucketMultipartUpload.

        when the upload is initiated  # noqa: E501

        :param initiated: The initiated of this ObjectspacesBucketMultipartUpload.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and initiated is None:
            raise ValueError("Invalid value for `initiated`, must not be `None`")  # noqa: E501

        self._initiated = initiated

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ObjectspacesBucketMultipartUpload, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ObjectspacesBucketMultipartUpload):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ObjectspacesBucketMultipartUpload):
            return True

        return self.to_dict() != other.to_dict()
