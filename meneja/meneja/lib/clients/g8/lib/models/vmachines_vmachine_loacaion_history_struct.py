# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VmachinesVmachineLoacaionHistoryStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"stack_id": "int", "stack_name": "str", "start_time": "int", "end_time": "int"}

    attribute_map = {
        "stack_id": "stack_id",
        "stack_name": "stack_name",
        "start_time": "start_time",
        "end_time": "end_time",
    }

    def __init__(
        self, stack_id=None, stack_name=None, start_time=None, end_time=None, _configuration=None
    ):  # noqa: E501
        """VmachinesVmachineLoacaionHistoryStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._stack_id = None
        self._stack_name = None
        self._start_time = None
        self._end_time = None
        self.discriminator = None

        self.stack_id = stack_id
        self.stack_name = stack_name
        self.start_time = start_time
        self.end_time = end_time

    @property
    def stack_id(self):
        """Gets the stack_id of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501

        Stack id  # noqa: E501

        :return: The stack_id of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501
        :rtype: int
        """
        return self._stack_id

    @stack_id.setter
    def stack_id(self, stack_id):
        """Sets the stack_id of this VmachinesVmachineLoacaionHistoryStruct.

        Stack id  # noqa: E501

        :param stack_id: The stack_id of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and stack_id is None:
            raise ValueError("Invalid value for `stack_id`, must not be `None`")  # noqa: E501

        self._stack_id = stack_id

    @property
    def stack_name(self):
        """Gets the stack_name of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501

        Stack name  # noqa: E501

        :return: The stack_name of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501
        :rtype: str
        """
        return self._stack_name

    @stack_name.setter
    def stack_name(self, stack_name):
        """Sets the stack_name of this VmachinesVmachineLoacaionHistoryStruct.

        Stack name  # noqa: E501

        :param stack_name: The stack_name of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and stack_name is None:
            raise ValueError("Invalid value for `stack_name`, must not be `None`")  # noqa: E501

        self._stack_name = stack_name

    @property
    def start_time(self):
        """Gets the start_time of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501

        Start time in epoch  # noqa: E501

        :return: The start_time of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this VmachinesVmachineLoacaionHistoryStruct.

        Start time in epoch  # noqa: E501

        :param start_time: The start_time of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    @property
    def end_time(self):
        """Gets the end_time of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501

        End time in epoch  # noqa: E501

        :return: The end_time of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this VmachinesVmachineLoacaionHistoryStruct.

        End time in epoch  # noqa: E501

        :param end_time: The end_time of this VmachinesVmachineLoacaionHistoryStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VmachinesVmachineLoacaionHistoryStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VmachinesVmachineLoacaionHistoryStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VmachinesVmachineLoacaionHistoryStruct):
            return True

        return self.to_dict() != other.to_dict()
