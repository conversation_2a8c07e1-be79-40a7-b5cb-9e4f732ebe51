# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class SwitchesSwitchConfig(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "name": "str",
        "ip_address": "str",
        "manufacturer": "str",
        "config": "str",
        "success": "bool",
        "message": "str",
    }

    attribute_map = {
        "name": "name",
        "ip_address": "ip_address",
        "manufacturer": "manufacturer",
        "config": "config",
        "success": "success",
        "message": "message",
    }

    def __init__(
        self,
        name=None,
        ip_address=None,
        manufacturer=None,
        config=None,
        success=None,
        message=None,
        _configuration=None,
    ):  # noqa: E501
        """SwitchesSwitchConfig - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._ip_address = None
        self._manufacturer = None
        self._config = None
        self._success = None
        self._message = None
        self.discriminator = None

        self.name = name
        self.ip_address = ip_address
        self.manufacturer = manufacturer
        self.config = config
        self.success = success
        self.message = message

    @property
    def name(self):
        """Gets the name of this SwitchesSwitchConfig.  # noqa: E501

        switch name  # noqa: E501

        :return: The name of this SwitchesSwitchConfig.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this SwitchesSwitchConfig.

        switch name  # noqa: E501

        :param name: The name of this SwitchesSwitchConfig.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def ip_address(self):
        """Gets the ip_address of this SwitchesSwitchConfig.  # noqa: E501

        switch ip address  # noqa: E501

        :return: The ip_address of this SwitchesSwitchConfig.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this SwitchesSwitchConfig.

        switch ip address  # noqa: E501

        :param ip_address: The ip_address of this SwitchesSwitchConfig.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_address is None:
            raise ValueError("Invalid value for `ip_address`, must not be `None`")  # noqa: E501

        self._ip_address = ip_address

    @property
    def manufacturer(self):
        """Gets the manufacturer of this SwitchesSwitchConfig.  # noqa: E501

        switch manufacturer  # noqa: E501

        :return: The manufacturer of this SwitchesSwitchConfig.  # noqa: E501
        :rtype: str
        """
        return self._manufacturer

    @manufacturer.setter
    def manufacturer(self, manufacturer):
        """Sets the manufacturer of this SwitchesSwitchConfig.

        switch manufacturer  # noqa: E501

        :param manufacturer: The manufacturer of this SwitchesSwitchConfig.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and manufacturer is None:
            raise ValueError("Invalid value for `manufacturer`, must not be `None`")  # noqa: E501

        self._manufacturer = manufacturer

    @property
    def config(self):
        """Gets the config of this SwitchesSwitchConfig.  # noqa: E501

        switch configuration  # noqa: E501

        :return: The config of this SwitchesSwitchConfig.  # noqa: E501
        :rtype: str
        """
        return self._config

    @config.setter
    def config(self, config):
        """Sets the config of this SwitchesSwitchConfig.

        switch configuration  # noqa: E501

        :param config: The config of this SwitchesSwitchConfig.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and config is None:
            raise ValueError("Invalid value for `config`, must not be `None`")  # noqa: E501

        self._config = config

    @property
    def success(self):
        """Gets the success of this SwitchesSwitchConfig.  # noqa: E501

        Whether it succeeded in fetching the switch config or not  # noqa: E501

        :return: The success of this SwitchesSwitchConfig.  # noqa: E501
        :rtype: bool
        """
        return self._success

    @success.setter
    def success(self, success):
        """Sets the success of this SwitchesSwitchConfig.

        Whether it succeeded in fetching the switch config or not  # noqa: E501

        :param success: The success of this SwitchesSwitchConfig.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and success is None:
            raise ValueError("Invalid value for `success`, must not be `None`")  # noqa: E501

        self._success = success

    @property
    def message(self):
        """Gets the message of this SwitchesSwitchConfig.  # noqa: E501

        error message if exists  # noqa: E501

        :return: The message of this SwitchesSwitchConfig.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this SwitchesSwitchConfig.

        error message if exists  # noqa: E501

        :param message: The message of this SwitchesSwitchConfig.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and message is None:
            raise ValueError("Invalid value for `message`, must not be `None`")  # noqa: E501

        self._message = message

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(SwitchesSwitchConfig, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SwitchesSwitchConfig):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SwitchesSwitchConfig):
            return True

        return self.to_dict() != other.to_dict()
