# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ObjectspacesObjectspaceStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"id": "int", "name": "str", "status": "str", "creation_time": "int", "account": "object"}

    attribute_map = {
        "id": "id",
        "name": "name",
        "status": "status",
        "creation_time": "creation_time",
        "account": "account",
    }

    def __init__(
        self, id=None, name=None, status=None, creation_time=None, account=None, _configuration=None
    ):  # noqa: E501
        """ObjectspacesObjectspaceStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._status = None
        self._creation_time = None
        self._account = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.status = status
        self.creation_time = creation_time
        self.account = account

    @property
    def id(self):
        """Gets the id of this ObjectspacesObjectspaceStruct.  # noqa: E501

        id of objectspace  # noqa: E501

        :return: The id of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ObjectspacesObjectspaceStruct.

        id of objectspace  # noqa: E501

        :param id: The id of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this ObjectspacesObjectspaceStruct.  # noqa: E501

        name of objectspace  # noqa: E501

        :return: The name of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ObjectspacesObjectspaceStruct.

        name of objectspace  # noqa: E501

        :param name: The name of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def status(self):
        """Gets the status of this ObjectspacesObjectspaceStruct.  # noqa: E501

        status of objectspace  # noqa: E501

        :return: The status of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ObjectspacesObjectspaceStruct.

        status of objectspace  # noqa: E501

        :param status: The status of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    @property
    def creation_time(self):
        """Gets the creation_time of this ObjectspacesObjectspaceStruct.  # noqa: E501

        creation timestamp of the objectspace  # noqa: E501

        :return: The creation_time of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :rtype: int
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this ObjectspacesObjectspaceStruct.

        creation timestamp of the objectspace  # noqa: E501

        :param creation_time: The creation_time of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and creation_time is None:
            raise ValueError("Invalid value for `creation_time`, must not be `None`")  # noqa: E501

        self._creation_time = creation_time

    @property
    def account(self):
        """Gets the account of this ObjectspacesObjectspaceStruct.  # noqa: E501

        account owning the objectspace  # noqa: E501

        :return: The account of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :rtype: object
        """
        return self._account

    @account.setter
    def account(self, account):
        """Sets the account of this ObjectspacesObjectspaceStruct.

        account owning the objectspace  # noqa: E501

        :param account: The account of this ObjectspacesObjectspaceStruct.  # noqa: E501
        :type: object
        """
        if self._configuration.client_side_validation and account is None:
            raise ValueError("Invalid value for `account`, must not be `None`")  # noqa: E501

        self._account = account

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ObjectspacesObjectspaceStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ObjectspacesObjectspaceStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ObjectspacesObjectspaceStruct):
            return True

        return self.to_dict() != other.to_dict()
