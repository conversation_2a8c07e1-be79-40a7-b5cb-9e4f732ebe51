# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class NodeDetails(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "active": "bool",
        "description": "str",
        "id": "int",
        "last_checked_timestamp": "int",
        "name": "str",
        "net_addresses": "list[NodeDetailsNetAddress]",
        "roles": "list[str]",
        "status": "str",
        "total_memory": "int",
        "virtual_functions": "int",
        "vm_memory_allocation": "int",
        "vm_memory_capacity": "int",
        "allowed_os_categories": "list[str]",
        "disallowed_os_categories": "list[str]",
    }

    attribute_map = {
        "active": "active",
        "description": "description",
        "id": "id",
        "last_checked_timestamp": "last_checked_timestamp",
        "name": "name",
        "net_addresses": "net_addresses",
        "roles": "roles",
        "status": "status",
        "total_memory": "total_memory",
        "virtual_functions": "virtual_functions",
        "vm_memory_allocation": "vm_memory_allocation",
        "vm_memory_capacity": "vm_memory_capacity",
        "allowed_os_categories": "allowed_os_categories",
        "disallowed_os_categories": "disallowed_os_categories",
    }

    def __init__(
        self,
        active=None,
        description=None,
        id=None,
        last_checked_timestamp=None,
        name=None,
        net_addresses=None,
        roles=None,
        status=None,
        total_memory=None,
        virtual_functions=None,
        vm_memory_allocation=None,
        vm_memory_capacity=None,
        allowed_os_categories=None,
        disallowed_os_categories=None,
        _configuration=None,
    ):  # noqa: E501
        """NodeDetails - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._active = None
        self._description = None
        self._id = None
        self._last_checked_timestamp = None
        self._name = None
        self._net_addresses = None
        self._roles = None
        self._status = None
        self._total_memory = None
        self._virtual_functions = None
        self._vm_memory_allocation = None
        self._vm_memory_capacity = None
        self._allowed_os_categories = None
        self._disallowed_os_categories = None
        self.discriminator = None

        if active is not None:
            self.active = active
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if last_checked_timestamp is not None:
            self.last_checked_timestamp = last_checked_timestamp
        if name is not None:
            self.name = name
        if net_addresses is not None:
            self.net_addresses = net_addresses
        if roles is not None:
            self.roles = roles
        if status is not None:
            self.status = status
        if total_memory is not None:
            self.total_memory = total_memory
        if virtual_functions is not None:
            self.virtual_functions = virtual_functions
        if vm_memory_allocation is not None:
            self.vm_memory_allocation = vm_memory_allocation
        if vm_memory_capacity is not None:
            self.vm_memory_capacity = vm_memory_capacity
        if allowed_os_categories is not None:
            self.allowed_os_categories = allowed_os_categories
        if disallowed_os_categories is not None:
            self.disallowed_os_categories = disallowed_os_categories

    @property
    def active(self):
        """Gets the active of this NodeDetails.  # noqa: E501

        A flag to show wheather the node is active or not  # noqa: E501

        :return: The active of this NodeDetails.  # noqa: E501
        :rtype: bool
        """
        return self._active

    @active.setter
    def active(self, active):
        """Sets the active of this NodeDetails.

        A flag to show wheather the node is active or not  # noqa: E501

        :param active: The active of this NodeDetails.  # noqa: E501
        :type: bool
        """

        self._active = active

    @property
    def description(self):
        """Gets the description of this NodeDetails.  # noqa: E501

        Node description  # noqa: E501

        :return: The description of this NodeDetails.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this NodeDetails.

        Node description  # noqa: E501

        :param description: The description of this NodeDetails.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this NodeDetails.  # noqa: E501

        sequence id for node  # noqa: E501

        :return: The id of this NodeDetails.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this NodeDetails.

        sequence id for node  # noqa: E501

        :param id: The id of this NodeDetails.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def last_checked_timestamp(self):
        """Gets the last_checked_timestamp of this NodeDetails.  # noqa: E501

        Epoch of last time the info updated  # noqa: E501

        :return: The last_checked_timestamp of this NodeDetails.  # noqa: E501
        :rtype: int
        """
        return self._last_checked_timestamp

    @last_checked_timestamp.setter
    def last_checked_timestamp(self, last_checked_timestamp):
        """Sets the last_checked_timestamp of this NodeDetails.

        Epoch of last time the info updated  # noqa: E501

        :param last_checked_timestamp: The last_checked_timestamp of this NodeDetails.  # noqa: E501
        :type: int
        """

        self._last_checked_timestamp = last_checked_timestamp

    @property
    def name(self):
        """Gets the name of this NodeDetails.  # noqa: E501

        Name oof the node  # noqa: E501

        :return: The name of this NodeDetails.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this NodeDetails.

        Name oof the node  # noqa: E501

        :param name: The name of this NodeDetails.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def net_addresses(self):
        """Gets the net_addresses of this NodeDetails.  # noqa: E501

        Network address details for each network interface in the node  # noqa: E501

        :return: The net_addresses of this NodeDetails.  # noqa: E501
        :rtype: list[NodeDetailsNetAddress]
        """
        return self._net_addresses

    @net_addresses.setter
    def net_addresses(self, net_addresses):
        """Sets the net_addresses of this NodeDetails.

        Network address details for each network interface in the node  # noqa: E501

        :param net_addresses: The net_addresses of this NodeDetails.  # noqa: E501
        :type: list[NodeDetailsNetAddress]
        """

        self._net_addresses = net_addresses

    @property
    def roles(self):
        """Gets the roles of this NodeDetails.  # noqa: E501

        A list of node roles  # noqa: E501

        :return: The roles of this NodeDetails.  # noqa: E501
        :rtype: list[str]
        """
        return self._roles

    @roles.setter
    def roles(self, roles):
        """Sets the roles of this NodeDetails.

        A list of node roles  # noqa: E501

        :param roles: The roles of this NodeDetails.  # noqa: E501
        :type: list[str]
        """

        self._roles = roles

    @property
    def status(self):
        """Gets the status of this NodeDetails.  # noqa: E501

        Node status  # noqa: E501

        :return: The status of this NodeDetails.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this NodeDetails.

        Node status  # noqa: E501

        :param status: The status of this NodeDetails.  # noqa: E501
        :type: str
        """
        allowed_values = [
            "ENABLED",
            "MAINTENANCE",
            "DECOMMISSIONED",
            "ERROR",
            "DEACTIVATING",
            "ENABLING",
            "DECOMMISSIONING",
            "UPDATING",
            "UPGRADING",
        ]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    @property
    def total_memory(self):
        """Gets the total_memory of this NodeDetails.  # noqa: E501

        Amount of memory in megabyte for the Node  # noqa: E501

        :return: The total_memory of this NodeDetails.  # noqa: E501
        :rtype: int
        """
        return self._total_memory

    @total_memory.setter
    def total_memory(self, total_memory):
        """Sets the total_memory of this NodeDetails.

        Amount of memory in megabyte for the Node  # noqa: E501

        :param total_memory: The total_memory of this NodeDetails.  # noqa: E501
        :type: int
        """

        self._total_memory = total_memory

    @property
    def virtual_functions(self):
        """Gets the virtual_functions of this NodeDetails.  # noqa: E501

        Amount of exposed virtual functions  # noqa: E501

        :return: The virtual_functions of this NodeDetails.  # noqa: E501
        :rtype: int
        """
        return self._virtual_functions

    @virtual_functions.setter
    def virtual_functions(self, virtual_functions):
        """Sets the virtual_functions of this NodeDetails.

        Amount of exposed virtual functions  # noqa: E501

        :param virtual_functions: The virtual_functions of this NodeDetails.  # noqa: E501
        :type: int
        """

        self._virtual_functions = virtual_functions

    @property
    def vm_memory_allocation(self):
        """Gets the vm_memory_allocation of this NodeDetails.  # noqa: E501

        Virtual machine allocated memory  # noqa: E501

        :return: The vm_memory_allocation of this NodeDetails.  # noqa: E501
        :rtype: int
        """
        return self._vm_memory_allocation

    @vm_memory_allocation.setter
    def vm_memory_allocation(self, vm_memory_allocation):
        """Sets the vm_memory_allocation of this NodeDetails.

        Virtual machine allocated memory  # noqa: E501

        :param vm_memory_allocation: The vm_memory_allocation of this NodeDetails.  # noqa: E501
        :type: int
        """

        self._vm_memory_allocation = vm_memory_allocation

    @property
    def vm_memory_capacity(self):
        """Gets the vm_memory_capacity of this NodeDetails.  # noqa: E501

        Memory that can be allocated by virtual machines  # noqa: E501

        :return: The vm_memory_capacity of this NodeDetails.  # noqa: E501
        :rtype: int
        """
        return self._vm_memory_capacity

    @vm_memory_capacity.setter
    def vm_memory_capacity(self, vm_memory_capacity):
        """Sets the vm_memory_capacity of this NodeDetails.

        Memory that can be allocated by virtual machines  # noqa: E501

        :param vm_memory_capacity: The vm_memory_capacity of this NodeDetails.  # noqa: E501
        :type: int
        """

        self._vm_memory_capacity = vm_memory_capacity

    @property
    def allowed_os_categories(self):
        """Gets the allowed_os_categories of this NodeDetails.  # noqa: E501

        List of allowed_os on categories the node  # noqa: E501

        :return: The allowed_os_categories of this NodeDetails.  # noqa: E501
        :rtype: list[str]
        """
        return self._allowed_os_categories

    @allowed_os_categories.setter
    def allowed_os_categories(self, allowed_os_categories):
        """Sets the allowed_os_categories of this NodeDetails.

        List of allowed_os on categories the node  # noqa: E501

        :param allowed_os_categories: The allowed_os_categories of this NodeDetails.  # noqa: E501
        :type: list[str]
        """

        self._allowed_os_categories = allowed_os_categories

    @property
    def disallowed_os_categories(self):
        """Gets the disallowed_os_categories of this NodeDetails.  # noqa: E501

        List of disallowed_os categories on the node  # noqa: E501

        :return: The disallowed_os_categories of this NodeDetails.  # noqa: E501
        :rtype: list[str]
        """
        return self._disallowed_os_categories

    @disallowed_os_categories.setter
    def disallowed_os_categories(self, disallowed_os_categories):
        """Sets the disallowed_os_categories of this NodeDetails.

        List of disallowed_os categories on the node  # noqa: E501

        :param disallowed_os_categories: The disallowed_os_categories of this NodeDetails.  # noqa: E501
        :type: list[str]
        """

        self._disallowed_os_categories = disallowed_os_categories

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(NodeDetails, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeDetails):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeDetails):
            return True

        return self.to_dict() != other.to_dict()
