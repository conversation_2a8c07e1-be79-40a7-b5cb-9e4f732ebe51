# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VgpusGPUStructLIST(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "pci_address": "str",
        "model": "str",
        "vendor": "str",
        "vgpu_types": "list[VgpusVGPUTypeStructLIST]",
        "id": "str",
        "node_id": "int",
        "node_name": "str",
        "vgpu_multiplier": "float",
        "description": "str",
    }

    attribute_map = {
        "pci_address": "pci_address",
        "model": "model",
        "vendor": "vendor",
        "vgpu_types": "vgpu_types",
        "id": "id",
        "node_id": "node_id",
        "node_name": "node_name",
        "vgpu_multiplier": "vgpu_multiplier",
        "description": "description",
    }

    def __init__(
        self,
        pci_address=None,
        model=None,
        vendor=None,
        vgpu_types=None,
        id=None,
        node_id=None,
        node_name=None,
        vgpu_multiplier=None,
        description="",
        _configuration=None,
    ):  # noqa: E501
        """VgpusGPUStructLIST - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pci_address = None
        self._model = None
        self._vendor = None
        self._vgpu_types = None
        self._id = None
        self._node_id = None
        self._node_name = None
        self._vgpu_multiplier = None
        self._description = None
        self.discriminator = None

        self.pci_address = pci_address
        self.model = model
        self.vendor = vendor
        self.vgpu_types = vgpu_types
        if id is not None:
            self.id = id
        if node_id is not None:
            self.node_id = node_id
        if node_name is not None:
            self.node_name = node_name
        if vgpu_multiplier is not None:
            self.vgpu_multiplier = vgpu_multiplier
        if description is not None:
            self.description = description

    @property
    def pci_address(self):
        """Gets the pci_address of this VgpusGPUStructLIST.  # noqa: E501

        PCI address  # noqa: E501

        :return: The pci_address of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._pci_address

    @pci_address.setter
    def pci_address(self, pci_address):
        """Sets the pci_address of this VgpusGPUStructLIST.

        PCI address  # noqa: E501

        :param pci_address: The pci_address of this VgpusGPUStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and pci_address is None:
            raise ValueError("Invalid value for `pci_address`, must not be `None`")  # noqa: E501

        self._pci_address = pci_address

    @property
    def model(self):
        """Gets the model of this VgpusGPUStructLIST.  # noqa: E501

        GPU model  # noqa: E501

        :return: The model of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._model

    @model.setter
    def model(self, model):
        """Sets the model of this VgpusGPUStructLIST.

        GPU model  # noqa: E501

        :param model: The model of this VgpusGPUStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and model is None:
            raise ValueError("Invalid value for `model`, must not be `None`")  # noqa: E501

        self._model = model

    @property
    def vendor(self):
        """Gets the vendor of this VgpusGPUStructLIST.  # noqa: E501

        GPU vendor  # noqa: E501

        :return: The vendor of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._vendor

    @vendor.setter
    def vendor(self, vendor):
        """Sets the vendor of this VgpusGPUStructLIST.

        GPU vendor  # noqa: E501

        :param vendor: The vendor of this VgpusGPUStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vendor is None:
            raise ValueError("Invalid value for `vendor`, must not be `None`")  # noqa: E501

        self._vendor = vendor

    @property
    def vgpu_types(self):
        """Gets the vgpu_types of this VgpusGPUStructLIST.  # noqa: E501

        List of VGPU types  # noqa: E501

        :return: The vgpu_types of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: list[VgpusVGPUTypeStructLIST]
        """
        return self._vgpu_types

    @vgpu_types.setter
    def vgpu_types(self, vgpu_types):
        """Sets the vgpu_types of this VgpusGPUStructLIST.

        List of VGPU types  # noqa: E501

        :param vgpu_types: The vgpu_types of this VgpusGPUStructLIST.  # noqa: E501
        :type: list[VgpusVGPUTypeStructLIST]
        """
        if self._configuration.client_side_validation and vgpu_types is None:
            raise ValueError("Invalid value for `vgpu_types`, must not be `None`")  # noqa: E501

        self._vgpu_types = vgpu_types

    @property
    def id(self):
        """Gets the id of this VgpusGPUStructLIST.  # noqa: E501

        GPU id for enabled gpus only  # noqa: E501

        :return: The id of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VgpusGPUStructLIST.

        GPU id for enabled gpus only  # noqa: E501

        :param id: The id of this VgpusGPUStructLIST.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def node_id(self):
        """Gets the node_id of this VgpusGPUStructLIST.  # noqa: E501

        Node id  # noqa: E501

        :return: The node_id of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this VgpusGPUStructLIST.

        Node id  # noqa: E501

        :param node_id: The node_id of this VgpusGPUStructLIST.  # noqa: E501
        :type: int
        """

        self._node_id = node_id

    @property
    def node_name(self):
        """Gets the node_name of this VgpusGPUStructLIST.  # noqa: E501

        Node name  # noqa: E501

        :return: The node_name of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this VgpusGPUStructLIST.

        Node name  # noqa: E501

        :param node_name: The node_name of this VgpusGPUStructLIST.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def vgpu_multiplier(self):
        """Gets the vgpu_multiplier of this VgpusGPUStructLIST.  # noqa: E501

        Vgpu multiplier  # noqa: E501

        :return: The vgpu_multiplier of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: float
        """
        return self._vgpu_multiplier

    @vgpu_multiplier.setter
    def vgpu_multiplier(self, vgpu_multiplier):
        """Sets the vgpu_multiplier of this VgpusGPUStructLIST.

        Vgpu multiplier  # noqa: E501

        :param vgpu_multiplier: The vgpu_multiplier of this VgpusGPUStructLIST.  # noqa: E501
        :type: float
        """

        self._vgpu_multiplier = vgpu_multiplier

    @property
    def description(self):
        """Gets the description of this VgpusGPUStructLIST.  # noqa: E501

        Description  # noqa: E501

        :return: The description of this VgpusGPUStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this VgpusGPUStructLIST.

        Description  # noqa: E501

        :param description: The description of this VgpusGPUStructLIST.  # noqa: E501
        :type: str
        """

        self._description = description

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VgpusGPUStructLIST, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VgpusGPUStructLIST):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VgpusGPUStructLIST):
            return True

        return self.to_dict() != other.to_dict()
