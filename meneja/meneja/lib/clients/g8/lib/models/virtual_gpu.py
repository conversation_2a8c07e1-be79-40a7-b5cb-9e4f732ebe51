# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VirtualGpu(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "account_id": "int",
        "acl": "list[VirtualGpuACE]",
        "creation_time": "int",
        "deletion_time": "int",
        "device_xml": "str",
        "guid": "str",
        "name": "str",
        "status": "str",
        "update_time": "int",
        "id": "str",
        "gpu_id": "str",
        "description": "str",
        "subdevices_count": "int",
        "virtual_machine": "VirtualGpuVirtualMachine",
    }

    attribute_map = {
        "account_id": "account_id",
        "acl": "acl",
        "creation_time": "creation_time",
        "deletion_time": "deletion_time",
        "device_xml": "device_xml",
        "guid": "guid",
        "name": "name",
        "status": "status",
        "update_time": "update_time",
        "id": "id",
        "gpu_id": "gpu_id",
        "description": "description",
        "subdevices_count": "subdevices_count",
        "virtual_machine": "virtual_machine",
    }

    def __init__(
        self,
        account_id=None,
        acl=None,
        creation_time=None,
        deletion_time=None,
        device_xml=None,
        guid=None,
        name=None,
        status=None,
        update_time=None,
        id=None,
        gpu_id=None,
        description=None,
        subdevices_count=None,
        virtual_machine=None,
        _configuration=None,
    ):  # noqa: E501
        """VirtualGpu - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._acl = None
        self._creation_time = None
        self._deletion_time = None
        self._device_xml = None
        self._guid = None
        self._name = None
        self._status = None
        self._update_time = None
        self._id = None
        self._gpu_id = None
        self._description = None
        self._subdevices_count = None
        self._virtual_machine = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if acl is not None:
            self.acl = acl
        if creation_time is not None:
            self.creation_time = creation_time
        if deletion_time is not None:
            self.deletion_time = deletion_time
        if device_xml is not None:
            self.device_xml = device_xml
        if guid is not None:
            self.guid = guid
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time
        if id is not None:
            self.id = id
        if gpu_id is not None:
            self.gpu_id = gpu_id
        if description is not None:
            self.description = description
        if subdevices_count is not None:
            self.subdevices_count = subdevices_count
        if virtual_machine is not None:
            self.virtual_machine = virtual_machine

    @property
    def account_id(self):
        """Gets the account_id of this VirtualGpu.  # noqa: E501

        Account id  # noqa: E501

        :return: The account_id of this VirtualGpu.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this VirtualGpu.

        Account id  # noqa: E501

        :param account_id: The account_id of this VirtualGpu.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def acl(self):
        """Gets the acl of this VirtualGpu.  # noqa: E501

        Access control list  # noqa: E501

        :return: The acl of this VirtualGpu.  # noqa: E501
        :rtype: list[VirtualGpuACE]
        """
        return self._acl

    @acl.setter
    def acl(self, acl):
        """Sets the acl of this VirtualGpu.

        Access control list  # noqa: E501

        :param acl: The acl of this VirtualGpu.  # noqa: E501
        :type: list[VirtualGpuACE]
        """

        self._acl = acl

    @property
    def creation_time(self):
        """Gets the creation_time of this VirtualGpu.  # noqa: E501

        Epoch for creation time  # noqa: E501

        :return: The creation_time of this VirtualGpu.  # noqa: E501
        :rtype: int
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this VirtualGpu.

        Epoch for creation time  # noqa: E501

        :param creation_time: The creation_time of this VirtualGpu.  # noqa: E501
        :type: int
        """

        self._creation_time = creation_time

    @property
    def deletion_time(self):
        """Gets the deletion_time of this VirtualGpu.  # noqa: E501

        Epoch for deletion time  # noqa: E501

        :return: The deletion_time of this VirtualGpu.  # noqa: E501
        :rtype: int
        """
        return self._deletion_time

    @deletion_time.setter
    def deletion_time(self, deletion_time):
        """Sets the deletion_time of this VirtualGpu.

        Epoch for deletion time  # noqa: E501

        :param deletion_time: The deletion_time of this VirtualGpu.  # noqa: E501
        :type: int
        """

        self._deletion_time = deletion_time

    @property
    def device_xml(self):
        """Gets the device_xml of this VirtualGpu.  # noqa: E501

        Device xml  # noqa: E501

        :return: The device_xml of this VirtualGpu.  # noqa: E501
        :rtype: str
        """
        return self._device_xml

    @device_xml.setter
    def device_xml(self, device_xml):
        """Sets the device_xml of this VirtualGpu.

        Device xml  # noqa: E501

        :param device_xml: The device_xml of this VirtualGpu.  # noqa: E501
        :type: str
        """

        self._device_xml = device_xml

    @property
    def guid(self):
        """Gets the guid of this VirtualGpu.  # noqa: E501

        UUID to attach to vm  # noqa: E501

        :return: The guid of this VirtualGpu.  # noqa: E501
        :rtype: str
        """
        return self._guid

    @guid.setter
    def guid(self, guid):
        """Sets the guid of this VirtualGpu.

        UUID to attach to vm  # noqa: E501

        :param guid: The guid of this VirtualGpu.  # noqa: E501
        :type: str
        """

        self._guid = guid

    @property
    def name(self):
        """Gets the name of this VirtualGpu.  # noqa: E501

        Virtual gpu name givin by teh user  # noqa: E501

        :return: The name of this VirtualGpu.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VirtualGpu.

        Virtual gpu name givin by teh user  # noqa: E501

        :param name: The name of this VirtualGpu.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this VirtualGpu.  # noqa: E501

        VGPU Status  # noqa: E501

        :return: The status of this VirtualGpu.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VirtualGpu.

        VGPU Status  # noqa: E501

        :param status: The status of this VirtualGpu.  # noqa: E501
        :type: str
        """
        allowed_values = ["CREATED", "DELETED", "DESTROYED", "ASSIGNED"]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this VirtualGpu.  # noqa: E501

        Epoch for update time  # noqa: E501

        :return: The update_time of this VirtualGpu.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this VirtualGpu.

        Epoch for update time  # noqa: E501

        :param update_time: The update_time of this VirtualGpu.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def id(self):
        """Gets the id of this VirtualGpu.  # noqa: E501

        VGPU id  # noqa: E501

        :return: The id of this VirtualGpu.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VirtualGpu.

        VGPU id  # noqa: E501

        :param id: The id of this VirtualGpu.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def gpu_id(self):
        """Gets the gpu_id of this VirtualGpu.  # noqa: E501

        GPU id  # noqa: E501

        :return: The gpu_id of this VirtualGpu.  # noqa: E501
        :rtype: str
        """
        return self._gpu_id

    @gpu_id.setter
    def gpu_id(self, gpu_id):
        """Sets the gpu_id of this VirtualGpu.

        GPU id  # noqa: E501

        :param gpu_id: The gpu_id of this VirtualGpu.  # noqa: E501
        :type: str
        """

        self._gpu_id = gpu_id

    @property
    def description(self):
        """Gets the description of this VirtualGpu.  # noqa: E501

        VGPU description  # noqa: E501

        :return: The description of this VirtualGpu.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this VirtualGpu.

        VGPU description  # noqa: E501

        :param description: The description of this VirtualGpu.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def subdevices_count(self):
        """Gets the subdevices_count of this VirtualGpu.  # noqa: E501

        Number of subdevices of the gpu  # noqa: E501

        :return: The subdevices_count of this VirtualGpu.  # noqa: E501
        :rtype: int
        """
        return self._subdevices_count

    @subdevices_count.setter
    def subdevices_count(self, subdevices_count):
        """Sets the subdevices_count of this VirtualGpu.

        Number of subdevices of the gpu  # noqa: E501

        :param subdevices_count: The subdevices_count of this VirtualGpu.  # noqa: E501
        :type: int
        """

        self._subdevices_count = subdevices_count

    @property
    def virtual_machine(self):
        """Gets the virtual_machine of this VirtualGpu.  # noqa: E501


        :return: The virtual_machine of this VirtualGpu.  # noqa: E501
        :rtype: VirtualGpuVirtualMachine
        """
        return self._virtual_machine

    @virtual_machine.setter
    def virtual_machine(self, virtual_machine):
        """Sets the virtual_machine of this VirtualGpu.


        :param virtual_machine: The virtual_machine of this VirtualGpu.  # noqa: E501
        :type: VirtualGpuVirtualMachine
        """

        self._virtual_machine = virtual_machine

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VirtualGpu, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VirtualGpu):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VirtualGpu):
            return True

        return self.to_dict() != other.to_dict()
