# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ObjectspacesAuditInfo(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"user": "str", "api_call": "str", "timestamp": "int", "status": "int"}

    attribute_map = {"user": "user", "api_call": "api_call", "timestamp": "timestamp", "status": "status"}

    def __init__(self, user=None, api_call=None, timestamp=None, status=None, _configuration=None):  # noqa: E501
        """ObjectspacesAuditInfo - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._user = None
        self._api_call = None
        self._timestamp = None
        self._status = None
        self.discriminator = None

        self.user = user
        self.api_call = api_call
        self.timestamp = timestamp
        self.status = status

    @property
    def user(self):
        """Gets the user of this ObjectspacesAuditInfo.  # noqa: E501

        user associated with this call  # noqa: E501

        :return: The user of this ObjectspacesAuditInfo.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this ObjectspacesAuditInfo.

        user associated with this call  # noqa: E501

        :param user: The user of this ObjectspacesAuditInfo.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and user is None:
            raise ValueError("Invalid value for `user`, must not be `None`")  # noqa: E501

        self._user = user

    @property
    def api_call(self):
        """Gets the api_call of this ObjectspacesAuditInfo.  # noqa: E501

        api_call  # noqa: E501

        :return: The api_call of this ObjectspacesAuditInfo.  # noqa: E501
        :rtype: str
        """
        return self._api_call

    @api_call.setter
    def api_call(self, api_call):
        """Sets the api_call of this ObjectspacesAuditInfo.

        api_call  # noqa: E501

        :param api_call: The api_call of this ObjectspacesAuditInfo.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and api_call is None:
            raise ValueError("Invalid value for `api_call`, must not be `None`")  # noqa: E501

        self._api_call = api_call

    @property
    def timestamp(self):
        """Gets the timestamp of this ObjectspacesAuditInfo.  # noqa: E501

        id of account owning objectspace  # noqa: E501

        :return: The timestamp of this ObjectspacesAuditInfo.  # noqa: E501
        :rtype: int
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this ObjectspacesAuditInfo.

        id of account owning objectspace  # noqa: E501

        :param timestamp: The timestamp of this ObjectspacesAuditInfo.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and timestamp is None:
            raise ValueError("Invalid value for `timestamp`, must not be `None`")  # noqa: E501

        self._timestamp = timestamp

    @property
    def status(self):
        """Gets the status of this ObjectspacesAuditInfo.  # noqa: E501

        status code of api call  # noqa: E501

        :return: The status of this ObjectspacesAuditInfo.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ObjectspacesAuditInfo.

        status code of api call  # noqa: E501

        :param status: The status of this ObjectspacesAuditInfo.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ObjectspacesAuditInfo, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ObjectspacesAuditInfo):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ObjectspacesAuditInfo):
            return True

        return self.to_dict() != other.to_dict()
