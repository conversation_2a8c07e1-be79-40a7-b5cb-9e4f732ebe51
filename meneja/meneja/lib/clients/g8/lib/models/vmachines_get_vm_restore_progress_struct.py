# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VmachinesGetVMRestoreProgressStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"disks": "list[VmachinesDiskProgress]", "starttime": "int", "status": "str"}

    attribute_map = {"disks": "disks", "starttime": "starttime", "status": "status"}

    def __init__(self, disks=None, starttime=None, status=None, _configuration=None):  # noqa: E501
        """VmachinesGetVMRestoreProgressStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._disks = None
        self._starttime = None
        self._status = None
        self.discriminator = None

        self.disks = disks
        self.starttime = starttime
        self.status = status

    @property
    def disks(self):
        """Gets the disks of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501

        default  # noqa: E501

        :return: The disks of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501
        :rtype: list[VmachinesDiskProgress]
        """
        return self._disks

    @disks.setter
    def disks(self, disks):
        """Sets the disks of this VmachinesGetVMRestoreProgressStruct.

        default  # noqa: E501

        :param disks: The disks of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501
        :type: list[VmachinesDiskProgress]
        """
        if self._configuration.client_side_validation and disks is None:
            raise ValueError("Invalid value for `disks`, must not be `None`")  # noqa: E501

        self._disks = disks

    @property
    def starttime(self):
        """Gets the starttime of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501

        default  # noqa: E501

        :return: The starttime of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501
        :rtype: int
        """
        return self._starttime

    @starttime.setter
    def starttime(self, starttime):
        """Sets the starttime of this VmachinesGetVMRestoreProgressStruct.

        default  # noqa: E501

        :param starttime: The starttime of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and starttime is None:
            raise ValueError("Invalid value for `starttime`, must not be `None`")  # noqa: E501

        self._starttime = starttime

    @property
    def status(self):
        """Gets the status of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501

        default  # noqa: E501

        :return: The status of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VmachinesGetVMRestoreProgressStruct.

        default  # noqa: E501

        :param status: The status of this VmachinesGetVMRestoreProgressStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501
        allowed_values = ["RESTORING", "COMPLETED", "FAILED"]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VmachinesGetVMRestoreProgressStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VmachinesGetVMRestoreProgressStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VmachinesGetVMRestoreProgressStruct):
            return True

        return self.to_dict() != other.to_dict()
