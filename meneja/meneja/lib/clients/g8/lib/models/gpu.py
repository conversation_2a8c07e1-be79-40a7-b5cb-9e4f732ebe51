# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class Gpu(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "description": "str",
        "max_instances": "int",
        "node_id": "int",
        "pci_address": "str",
        "vendor": "str",
        "vgpu_multiplier": "float",
        "vgpu_type_id": "str",
        "id": "str",
    }

    attribute_map = {
        "description": "description",
        "max_instances": "max_instances",
        "node_id": "node_id",
        "pci_address": "pci_address",
        "vendor": "vendor",
        "vgpu_multiplier": "vgpu_multiplier",
        "vgpu_type_id": "vgpu_type_id",
        "id": "id",
    }

    def __init__(
        self,
        description=None,
        max_instances=None,
        node_id=None,
        pci_address=None,
        vendor=None,
        vgpu_multiplier=None,
        vgpu_type_id=None,
        id=None,
        _configuration=None,
    ):  # noqa: E501
        """Gpu - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._max_instances = None
        self._node_id = None
        self._pci_address = None
        self._vendor = None
        self._vgpu_multiplier = None
        self._vgpu_type_id = None
        self._id = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if max_instances is not None:
            self.max_instances = max_instances
        if node_id is not None:
            self.node_id = node_id
        if pci_address is not None:
            self.pci_address = pci_address
        if vendor is not None:
            self.vendor = vendor
        if vgpu_multiplier is not None:
            self.vgpu_multiplier = vgpu_multiplier
        if vgpu_type_id is not None:
            self.vgpu_type_id = vgpu_type_id
        if id is not None:
            self.id = id

    @property
    def description(self):
        """Gets the description of this Gpu.  # noqa: E501

        Description  # noqa: E501

        :return: The description of this Gpu.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this Gpu.

        Description  # noqa: E501

        :param description: The description of this Gpu.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def max_instances(self):
        """Gets the max_instances of this Gpu.  # noqa: E501

        Max instances of vgpu can be created  # noqa: E501

        :return: The max_instances of this Gpu.  # noqa: E501
        :rtype: int
        """
        return self._max_instances

    @max_instances.setter
    def max_instances(self, max_instances):
        """Sets the max_instances of this Gpu.

        Max instances of vgpu can be created  # noqa: E501

        :param max_instances: The max_instances of this Gpu.  # noqa: E501
        :type: int
        """

        self._max_instances = max_instances

    @property
    def node_id(self):
        """Gets the node_id of this Gpu.  # noqa: E501

        Node id  # noqa: E501

        :return: The node_id of this Gpu.  # noqa: E501
        :rtype: int
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this Gpu.

        Node id  # noqa: E501

        :param node_id: The node_id of this Gpu.  # noqa: E501
        :type: int
        """

        self._node_id = node_id

    @property
    def pci_address(self):
        """Gets the pci_address of this Gpu.  # noqa: E501

        Pci address  # noqa: E501

        :return: The pci_address of this Gpu.  # noqa: E501
        :rtype: str
        """
        return self._pci_address

    @pci_address.setter
    def pci_address(self, pci_address):
        """Sets the pci_address of this Gpu.

        Pci address  # noqa: E501

        :param pci_address: The pci_address of this Gpu.  # noqa: E501
        :type: str
        """

        self._pci_address = pci_address

    @property
    def vendor(self):
        """Gets the vendor of this Gpu.  # noqa: E501

        GPU vendor  # noqa: E501

        :return: The vendor of this Gpu.  # noqa: E501
        :rtype: str
        """
        return self._vendor

    @vendor.setter
    def vendor(self, vendor):
        """Sets the vendor of this Gpu.

        GPU vendor  # noqa: E501

        :param vendor: The vendor of this Gpu.  # noqa: E501
        :type: str
        """

        self._vendor = vendor

    @property
    def vgpu_multiplier(self):
        """Gets the vgpu_multiplier of this Gpu.  # noqa: E501

        Vgpu multiplier  # noqa: E501

        :return: The vgpu_multiplier of this Gpu.  # noqa: E501
        :rtype: float
        """
        return self._vgpu_multiplier

    @vgpu_multiplier.setter
    def vgpu_multiplier(self, vgpu_multiplier):
        """Sets the vgpu_multiplier of this Gpu.

        Vgpu multiplier  # noqa: E501

        :param vgpu_multiplier: The vgpu_multiplier of this Gpu.  # noqa: E501
        :type: float
        """

        self._vgpu_multiplier = vgpu_multiplier

    @property
    def vgpu_type_id(self):
        """Gets the vgpu_type_id of this Gpu.  # noqa: E501

        Vgpu type id  # noqa: E501

        :return: The vgpu_type_id of this Gpu.  # noqa: E501
        :rtype: str
        """
        return self._vgpu_type_id

    @vgpu_type_id.setter
    def vgpu_type_id(self, vgpu_type_id):
        """Sets the vgpu_type_id of this Gpu.

        Vgpu type id  # noqa: E501

        :param vgpu_type_id: The vgpu_type_id of this Gpu.  # noqa: E501
        :type: str
        """

        self._vgpu_type_id = vgpu_type_id

    @property
    def id(self):
        """Gets the id of this Gpu.  # noqa: E501

        GPU id  # noqa: E501

        :return: The id of this Gpu.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this Gpu.

        GPU id  # noqa: E501

        :param id: The id of this Gpu.  # noqa: E501
        :type: str
        """

        self._id = id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(Gpu, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Gpu):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, Gpu):
            return True

        return self.to_dict() != other.to_dict()
