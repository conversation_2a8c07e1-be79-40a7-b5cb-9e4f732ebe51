# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VgpusGPUShortStructLIST(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "model": "str",
        "vendor": "str",
        "available_instances": "int",
        "description": "str",
        "id": "str",
        "subdevices_count": "int",
    }

    attribute_map = {
        "model": "model",
        "vendor": "vendor",
        "available_instances": "available_instances",
        "description": "description",
        "id": "id",
        "subdevices_count": "subdevices_count",
    }

    def __init__(
        self,
        model=None,
        vendor=None,
        available_instances=None,
        description="",
        id=None,
        subdevices_count=None,
        _configuration=None,
    ):  # noqa: E501
        """VgpusGPUShortStructLIST - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._model = None
        self._vendor = None
        self._available_instances = None
        self._description = None
        self._id = None
        self._subdevices_count = None
        self.discriminator = None

        self.model = model
        self.vendor = vendor
        self.available_instances = available_instances
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        self.subdevices_count = subdevices_count

    @property
    def model(self):
        """Gets the model of this VgpusGPUShortStructLIST.  # noqa: E501

        GPU model  # noqa: E501

        :return: The model of this VgpusGPUShortStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._model

    @model.setter
    def model(self, model):
        """Sets the model of this VgpusGPUShortStructLIST.

        GPU model  # noqa: E501

        :param model: The model of this VgpusGPUShortStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and model is None:
            raise ValueError("Invalid value for `model`, must not be `None`")  # noqa: E501

        self._model = model

    @property
    def vendor(self):
        """Gets the vendor of this VgpusGPUShortStructLIST.  # noqa: E501

        GPU vendor  # noqa: E501

        :return: The vendor of this VgpusGPUShortStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._vendor

    @vendor.setter
    def vendor(self, vendor):
        """Sets the vendor of this VgpusGPUShortStructLIST.

        GPU vendor  # noqa: E501

        :param vendor: The vendor of this VgpusGPUShortStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vendor is None:
            raise ValueError("Invalid value for `vendor`, must not be `None`")  # noqa: E501

        self._vendor = vendor

    @property
    def available_instances(self):
        """Gets the available_instances of this VgpusGPUShortStructLIST.  # noqa: E501

        Available instances  # noqa: E501

        :return: The available_instances of this VgpusGPUShortStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._available_instances

    @available_instances.setter
    def available_instances(self, available_instances):
        """Sets the available_instances of this VgpusGPUShortStructLIST.

        Available instances  # noqa: E501

        :param available_instances: The available_instances of this VgpusGPUShortStructLIST.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and available_instances is None:
            raise ValueError("Invalid value for `available_instances`, must not be `None`")  # noqa: E501

        self._available_instances = available_instances

    @property
    def description(self):
        """Gets the description of this VgpusGPUShortStructLIST.  # noqa: E501

        Description  # noqa: E501

        :return: The description of this VgpusGPUShortStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this VgpusGPUShortStructLIST.

        Description  # noqa: E501

        :param description: The description of this VgpusGPUShortStructLIST.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this VgpusGPUShortStructLIST.  # noqa: E501

        GPU id for enabled gpus only  # noqa: E501

        :return: The id of this VgpusGPUShortStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VgpusGPUShortStructLIST.

        GPU id for enabled gpus only  # noqa: E501

        :param id: The id of this VgpusGPUShortStructLIST.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def subdevices_count(self):
        """Gets the subdevices_count of this VgpusGPUShortStructLIST.  # noqa: E501

        Number of subdevices of the gpu  # noqa: E501

        :return: The subdevices_count of this VgpusGPUShortStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._subdevices_count

    @subdevices_count.setter
    def subdevices_count(self, subdevices_count):
        """Sets the subdevices_count of this VgpusGPUShortStructLIST.

        Number of subdevices of the gpu  # noqa: E501

        :param subdevices_count: The subdevices_count of this VgpusGPUShortStructLIST.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and subdevices_count is None:
            raise ValueError("Invalid value for `subdevices_count`, must not be `None`")  # noqa: E501

        self._subdevices_count = subdevices_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VgpusGPUShortStructLIST, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VgpusGPUShortStructLIST):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VgpusGPUShortStructLIST):
            return True

        return self.to_dict() != other.to_dict()
