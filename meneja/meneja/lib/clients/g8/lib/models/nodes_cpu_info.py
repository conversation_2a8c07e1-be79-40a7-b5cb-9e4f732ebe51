# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class NodesCPUInfo(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "bugs": "list[str]",
        "cores_per_socket": "int",
        "cpu_model": "str",
        "flags": "list[str]",
        "sockets": "int",
        "threads_per_core": "int",
        "vendor": "str",
    }

    attribute_map = {
        "bugs": "bugs",
        "cores_per_socket": "cores_per_socket",
        "cpu_model": "cpu_model",
        "flags": "flags",
        "sockets": "sockets",
        "threads_per_core": "threads_per_core",
        "vendor": "vendor",
    }

    def __init__(
        self,
        bugs=None,
        cores_per_socket=None,
        cpu_model=None,
        flags=None,
        sockets=None,
        threads_per_core=None,
        vendor=None,
        _configuration=None,
    ):  # noqa: E501
        """NodesCPUInfo - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bugs = None
        self._cores_per_socket = None
        self._cpu_model = None
        self._flags = None
        self._sockets = None
        self._threads_per_core = None
        self._vendor = None
        self.discriminator = None

        if bugs is not None:
            self.bugs = bugs
        if cores_per_socket is not None:
            self.cores_per_socket = cores_per_socket
        if cpu_model is not None:
            self.cpu_model = cpu_model
        if flags is not None:
            self.flags = flags
        if sockets is not None:
            self.sockets = sockets
        if threads_per_core is not None:
            self.threads_per_core = threads_per_core
        if vendor is not None:
            self.vendor = vendor

    @property
    def bugs(self):
        """Gets the bugs of this NodesCPUInfo.  # noqa: E501

        Known cpu bugs  # noqa: E501

        :return: The bugs of this NodesCPUInfo.  # noqa: E501
        :rtype: list[str]
        """
        return self._bugs

    @bugs.setter
    def bugs(self, bugs):
        """Sets the bugs of this NodesCPUInfo.

        Known cpu bugs  # noqa: E501

        :param bugs: The bugs of this NodesCPUInfo.  # noqa: E501
        :type: list[str]
        """

        self._bugs = bugs

    @property
    def cores_per_socket(self):
        """Gets the cores_per_socket of this NodesCPUInfo.  # noqa: E501

        Amount of core per cpu  # noqa: E501

        :return: The cores_per_socket of this NodesCPUInfo.  # noqa: E501
        :rtype: int
        """
        return self._cores_per_socket

    @cores_per_socket.setter
    def cores_per_socket(self, cores_per_socket):
        """Sets the cores_per_socket of this NodesCPUInfo.

        Amount of core per cpu  # noqa: E501

        :param cores_per_socket: The cores_per_socket of this NodesCPUInfo.  # noqa: E501
        :type: int
        """

        self._cores_per_socket = cores_per_socket

    @property
    def cpu_model(self):
        """Gets the cpu_model of this NodesCPUInfo.  # noqa: E501

        CPU Model  # noqa: E501

        :return: The cpu_model of this NodesCPUInfo.  # noqa: E501
        :rtype: str
        """
        return self._cpu_model

    @cpu_model.setter
    def cpu_model(self, cpu_model):
        """Sets the cpu_model of this NodesCPUInfo.

        CPU Model  # noqa: E501

        :param cpu_model: The cpu_model of this NodesCPUInfo.  # noqa: E501
        :type: str
        """

        self._cpu_model = cpu_model

    @property
    def flags(self):
        """Gets the flags of this NodesCPUInfo.  # noqa: E501

        CPU flags  # noqa: E501

        :return: The flags of this NodesCPUInfo.  # noqa: E501
        :rtype: list[str]
        """
        return self._flags

    @flags.setter
    def flags(self, flags):
        """Sets the flags of this NodesCPUInfo.

        CPU flags  # noqa: E501

        :param flags: The flags of this NodesCPUInfo.  # noqa: E501
        :type: list[str]
        """

        self._flags = flags

    @property
    def sockets(self):
        """Gets the sockets of this NodesCPUInfo.  # noqa: E501

        Amount of active cpu sockets  # noqa: E501

        :return: The sockets of this NodesCPUInfo.  # noqa: E501
        :rtype: int
        """
        return self._sockets

    @sockets.setter
    def sockets(self, sockets):
        """Sets the sockets of this NodesCPUInfo.

        Amount of active cpu sockets  # noqa: E501

        :param sockets: The sockets of this NodesCPUInfo.  # noqa: E501
        :type: int
        """

        self._sockets = sockets

    @property
    def threads_per_core(self):
        """Gets the threads_per_core of this NodesCPUInfo.  # noqa: E501

        Amount of cores in the cpu  # noqa: E501

        :return: The threads_per_core of this NodesCPUInfo.  # noqa: E501
        :rtype: int
        """
        return self._threads_per_core

    @threads_per_core.setter
    def threads_per_core(self, threads_per_core):
        """Sets the threads_per_core of this NodesCPUInfo.

        Amount of cores in the cpu  # noqa: E501

        :param threads_per_core: The threads_per_core of this NodesCPUInfo.  # noqa: E501
        :type: int
        """

        self._threads_per_core = threads_per_core

    @property
    def vendor(self):
        """Gets the vendor of this NodesCPUInfo.  # noqa: E501

        Vendor name of the cpu  # noqa: E501

        :return: The vendor of this NodesCPUInfo.  # noqa: E501
        :rtype: str
        """
        return self._vendor

    @vendor.setter
    def vendor(self, vendor):
        """Sets the vendor of this NodesCPUInfo.

        Vendor name of the cpu  # noqa: E501

        :param vendor: The vendor of this NodesCPUInfo.  # noqa: E501
        :type: str
        """

        self._vendor = vendor

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(NodesCPUInfo, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodesCPUInfo):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodesCPUInfo):
            return True

        return self.to_dict() != other.to_dict()
