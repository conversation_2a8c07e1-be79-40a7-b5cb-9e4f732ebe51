# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class HealthCheckMessage(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "category": "str",
        "last_error_timestamp": "int",
        "message": "str",
        "node_id": "int",
        "path": "str",
        "state": "str",
        "uid": "str",
        "mute_details": "HealthCheckMessageMuteDetails",
    }

    attribute_map = {
        "category": "category",
        "last_error_timestamp": "last_error_timestamp",
        "message": "message",
        "node_id": "node_id",
        "path": "path",
        "state": "state",
        "uid": "uid",
        "mute_details": "mute_details",
    }

    def __init__(
        self,
        category=None,
        last_error_timestamp=None,
        message=None,
        node_id=None,
        path=None,
        state=None,
        uid=None,
        mute_details=None,
        _configuration=None,
    ):  # noqa: E501
        """HealthCheckMessage - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._category = None
        self._last_error_timestamp = None
        self._message = None
        self._node_id = None
        self._path = None
        self._state = None
        self._uid = None
        self._mute_details = None
        self.discriminator = None

        if category is not None:
            self.category = category
        if last_error_timestamp is not None:
            self.last_error_timestamp = last_error_timestamp
        if message is not None:
            self.message = message
        if node_id is not None:
            self.node_id = node_id
        if path is not None:
            self.path = path
        if state is not None:
            self.state = state
        if uid is not None:
            self.uid = uid
        if mute_details is not None:
            self.mute_details = mute_details

    @property
    def category(self):
        """Gets the category of this HealthCheckMessage.  # noqa: E501

        category of the health check message  # noqa: E501

        :return: The category of this HealthCheckMessage.  # noqa: E501
        :rtype: str
        """
        return self._category

    @category.setter
    def category(self, category):
        """Sets the category of this HealthCheckMessage.

        category of the health check message  # noqa: E501

        :param category: The category of this HealthCheckMessage.  # noqa: E501
        :type: str
        """

        self._category = category

    @property
    def last_error_timestamp(self):
        """Gets the last_error_timestamp of this HealthCheckMessage.  # noqa: E501

        Epoch for when of the last error occurred  # noqa: E501

        :return: The last_error_timestamp of this HealthCheckMessage.  # noqa: E501
        :rtype: int
        """
        return self._last_error_timestamp

    @last_error_timestamp.setter
    def last_error_timestamp(self, last_error_timestamp):
        """Sets the last_error_timestamp of this HealthCheckMessage.

        Epoch for when of the last error occurred  # noqa: E501

        :param last_error_timestamp: The last_error_timestamp of this HealthCheckMessage.  # noqa: E501
        :type: int
        """

        self._last_error_timestamp = last_error_timestamp

    @property
    def message(self):
        """Gets the message of this HealthCheckMessage.  # noqa: E501

        The message body for the health check  # noqa: E501

        :return: The message of this HealthCheckMessage.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this HealthCheckMessage.

        The message body for the health check  # noqa: E501

        :param message: The message of this HealthCheckMessage.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def node_id(self):
        """Gets the node_id of this HealthCheckMessage.  # noqa: E501

        Node id (Optional) where the message is related to  # noqa: E501

        :return: The node_id of this HealthCheckMessage.  # noqa: E501
        :rtype: int
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this HealthCheckMessage.

        Node id (Optional) where the message is related to  # noqa: E501

        :param node_id: The node_id of this HealthCheckMessage.  # noqa: E501
        :type: int
        """

        self._node_id = node_id

    @property
    def path(self):
        """Gets the path of this HealthCheckMessage.  # noqa: E501

        Optional file system path if the check is related to volumes  # noqa: E501

        :return: The path of this HealthCheckMessage.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this HealthCheckMessage.

        Optional file system path if the check is related to volumes  # noqa: E501

        :param path: The path of this HealthCheckMessage.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def state(self):
        """Gets the state of this HealthCheckMessage.  # noqa: E501

        the state of the check OK, WARNING, ERROR  # noqa: E501

        :return: The state of this HealthCheckMessage.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this HealthCheckMessage.

        the state of the check OK, WARNING, ERROR  # noqa: E501

        :param state: The state of this HealthCheckMessage.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def uid(self):
        """Gets the uid of this HealthCheckMessage.  # noqa: E501

        unique id for the messega  # noqa: E501

        :return: The uid of this HealthCheckMessage.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this HealthCheckMessage.

        unique id for the messega  # noqa: E501

        :param uid: The uid of this HealthCheckMessage.  # noqa: E501
        :type: str
        """

        self._uid = uid

    @property
    def mute_details(self):
        """Gets the mute_details of this HealthCheckMessage.  # noqa: E501


        :return: The mute_details of this HealthCheckMessage.  # noqa: E501
        :rtype: HealthCheckMessageMuteDetails
        """
        return self._mute_details

    @mute_details.setter
    def mute_details(self, mute_details):
        """Sets the mute_details of this HealthCheckMessage.


        :param mute_details: The mute_details of this HealthCheckMessage.  # noqa: E501
        :type: HealthCheckMessageMuteDetails
        """

        self._mute_details = mute_details

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(HealthCheckMessage, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HealthCheckMessage):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HealthCheckMessage):
            return True

        return self.to_dict() != other.to_dict()
