# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VirtualMachinesByNodePlacementStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "name": "str",
        "memory": "int",
        "sockets": "int",
        "cores_per_socket": "int",
        "threads_per_core": "int",
        "threads": "int",
        "cpu_model": "str",
        "categories": "list[VirtualMachinesByOsCategoryStruct]",
        "allowed_os_categories": "list[str]",
        "disallowed_os_categories": "list[str]",
    }

    attribute_map = {
        "id": "id",
        "name": "name",
        "memory": "memory",
        "sockets": "sockets",
        "cores_per_socket": "cores_per_socket",
        "threads_per_core": "threads_per_core",
        "threads": "threads",
        "cpu_model": "cpu_model",
        "categories": "categories",
        "allowed_os_categories": "allowed_os_categories",
        "disallowed_os_categories": "disallowed_os_categories",
    }

    def __init__(
        self,
        id=None,
        name=None,
        memory=None,
        sockets=None,
        cores_per_socket=None,
        threads_per_core=None,
        threads=None,
        cpu_model=None,
        categories=None,
        allowed_os_categories=None,
        disallowed_os_categories=None,
        _configuration=None,
    ):  # noqa: E501
        """VirtualMachinesByNodePlacementStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._memory = None
        self._sockets = None
        self._cores_per_socket = None
        self._threads_per_core = None
        self._threads = None
        self._cpu_model = None
        self._categories = None
        self._allowed_os_categories = None
        self._disallowed_os_categories = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.memory = memory
        self.sockets = sockets
        self.cores_per_socket = cores_per_socket
        self.threads_per_core = threads_per_core
        self.threads = threads
        self.cpu_model = cpu_model
        self.categories = categories
        self.allowed_os_categories = allowed_os_categories
        self.disallowed_os_categories = disallowed_os_categories

    @property
    def id(self):
        """Gets the id of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Node id  # noqa: E501

        :return: The id of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VirtualMachinesByNodePlacementStruct.

        Node id  # noqa: E501

        :param id: The id of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Node name  # noqa: E501

        :return: The name of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VirtualMachinesByNodePlacementStruct.

        Node name  # noqa: E501

        :param name: The name of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def memory(self):
        """Gets the memory of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Memory available to virtual machines  # noqa: E501

        :return: The memory of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this VirtualMachinesByNodePlacementStruct.

        Memory available to virtual machines  # noqa: E501

        :param memory: The memory of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and memory is None:
            raise ValueError("Invalid value for `memory`, must not be `None`")  # noqa: E501

        self._memory = memory

    @property
    def sockets(self):
        """Gets the sockets of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Amount of active cpu sockets  # noqa: E501

        :return: The sockets of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: int
        """
        return self._sockets

    @sockets.setter
    def sockets(self, sockets):
        """Sets the sockets of this VirtualMachinesByNodePlacementStruct.

        Amount of active cpu sockets  # noqa: E501

        :param sockets: The sockets of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and sockets is None:
            raise ValueError("Invalid value for `sockets`, must not be `None`")  # noqa: E501

        self._sockets = sockets

    @property
    def cores_per_socket(self):
        """Gets the cores_per_socket of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Cores per socket  # noqa: E501

        :return: The cores_per_socket of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: int
        """
        return self._cores_per_socket

    @cores_per_socket.setter
    def cores_per_socket(self, cores_per_socket):
        """Sets the cores_per_socket of this VirtualMachinesByNodePlacementStruct.

        Cores per socket  # noqa: E501

        :param cores_per_socket: The cores_per_socket of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and cores_per_socket is None:
            raise ValueError("Invalid value for `cores_per_socket`, must not be `None`")  # noqa: E501

        self._cores_per_socket = cores_per_socket

    @property
    def threads_per_core(self):
        """Gets the threads_per_core of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Threads per cpu core  # noqa: E501

        :return: The threads_per_core of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: int
        """
        return self._threads_per_core

    @threads_per_core.setter
    def threads_per_core(self, threads_per_core):
        """Sets the threads_per_core of this VirtualMachinesByNodePlacementStruct.

        Threads per cpu core  # noqa: E501

        :param threads_per_core: The threads_per_core of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and threads_per_core is None:
            raise ValueError("Invalid value for `threads_per_core`, must not be `None`")  # noqa: E501

        self._threads_per_core = threads_per_core

    @property
    def threads(self):
        """Gets the threads of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Threads available to virtual machines  # noqa: E501

        :return: The threads of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: int
        """
        return self._threads

    @threads.setter
    def threads(self, threads):
        """Sets the threads of this VirtualMachinesByNodePlacementStruct.

        Threads available to virtual machines  # noqa: E501

        :param threads: The threads of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and threads is None:
            raise ValueError("Invalid value for `threads`, must not be `None`")  # noqa: E501

        self._threads = threads

    @property
    def cpu_model(self):
        """Gets the cpu_model of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Model of cpu  # noqa: E501

        :return: The cpu_model of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: str
        """
        return self._cpu_model

    @cpu_model.setter
    def cpu_model(self, cpu_model):
        """Sets the cpu_model of this VirtualMachinesByNodePlacementStruct.

        Model of cpu  # noqa: E501

        :param cpu_model: The cpu_model of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cpu_model is None:
            raise ValueError("Invalid value for `cpu_model`, must not be `None`")  # noqa: E501

        self._cpu_model = cpu_model

    @property
    def categories(self):
        """Gets the categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        Vms running on this node  # noqa: E501

        :return: The categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: list[VirtualMachinesByOsCategoryStruct]
        """
        return self._categories

    @categories.setter
    def categories(self, categories):
        """Sets the categories of this VirtualMachinesByNodePlacementStruct.

        Vms running on this node  # noqa: E501

        :param categories: The categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: list[VirtualMachinesByOsCategoryStruct]
        """
        if self._configuration.client_side_validation and categories is None:
            raise ValueError("Invalid value for `categories`, must not be `None`")  # noqa: E501

        self._categories = categories

    @property
    def allowed_os_categories(self):
        """Gets the allowed_os_categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        List of allowed_os on categories the node  # noqa: E501

        :return: The allowed_os_categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: list[str]
        """
        return self._allowed_os_categories

    @allowed_os_categories.setter
    def allowed_os_categories(self, allowed_os_categories):
        """Sets the allowed_os_categories of this VirtualMachinesByNodePlacementStruct.

        List of allowed_os on categories the node  # noqa: E501

        :param allowed_os_categories: The allowed_os_categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: list[str]
        """
        if self._configuration.client_side_validation and allowed_os_categories is None:
            raise ValueError("Invalid value for `allowed_os_categories`, must not be `None`")  # noqa: E501

        self._allowed_os_categories = allowed_os_categories

    @property
    def disallowed_os_categories(self):
        """Gets the disallowed_os_categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501

        List of disallowed_os categories on the node  # noqa: E501

        :return: The disallowed_os_categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :rtype: list[str]
        """
        return self._disallowed_os_categories

    @disallowed_os_categories.setter
    def disallowed_os_categories(self, disallowed_os_categories):
        """Sets the disallowed_os_categories of this VirtualMachinesByNodePlacementStruct.

        List of disallowed_os categories on the node  # noqa: E501

        :param disallowed_os_categories: The disallowed_os_categories of this VirtualMachinesByNodePlacementStruct.  # noqa: E501
        :type: list[str]
        """
        if self._configuration.client_side_validation and disallowed_os_categories is None:
            raise ValueError("Invalid value for `disallowed_os_categories`, must not be `None`")  # noqa: E501

        self._disallowed_os_categories = disallowed_os_categories

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VirtualMachinesByNodePlacementStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VirtualMachinesByNodePlacementStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VirtualMachinesByNodePlacementStruct):
            return True

        return self.to_dict() != other.to_dict()
