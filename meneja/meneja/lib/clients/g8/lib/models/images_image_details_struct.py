# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ImagesImageDetailsStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "name": "str",
        "access": "str",
        "status": "str",
        "size": "int",
        "create_time": "int",
        "update_time": "int",
        "url": "str",
        "os": "ImagesImageDetailsStructOs",
        "type": "str",
        "vm_specifications": "ImagesImageDetailsStructVmSpecifications",
        "node_access": "list[ImagesShortNodeStruct]",
        "account_access": "ImagesImageDetailsStructAccountAccess",
    }

    attribute_map = {
        "id": "id",
        "name": "name",
        "access": "access",
        "status": "status",
        "size": "size",
        "create_time": "create_time",
        "update_time": "update_time",
        "url": "url",
        "os": "os",
        "type": "type",
        "vm_specifications": "vm_specifications",
        "node_access": "node_access",
        "account_access": "account_access",
    }

    def __init__(
        self,
        id=None,
        name=None,
        access=None,
        status=None,
        size=None,
        create_time=None,
        update_time=None,
        url=None,
        os=None,
        type=None,
        vm_specifications=None,
        node_access=None,
        account_access=None,
        _configuration=None,
    ):  # noqa: E501
        """ImagesImageDetailsStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._access = None
        self._status = None
        self._size = None
        self._create_time = None
        self._update_time = None
        self._url = None
        self._os = None
        self._type = None
        self._vm_specifications = None
        self._node_access = None
        self._account_access = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.access = access
        self.status = status
        self.size = size
        self.create_time = create_time
        self.update_time = update_time
        self.url = url
        self.os = os
        self.type = type
        self.vm_specifications = vm_specifications
        self.node_access = node_access
        self.account_access = account_access

    @property
    def id(self):
        """Gets the id of this ImagesImageDetailsStruct.  # noqa: E501

        Image id  # noqa: E501

        :return: The id of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ImagesImageDetailsStruct.

        Image id  # noqa: E501

        :param id: The id of this ImagesImageDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this ImagesImageDetailsStruct.  # noqa: E501

        Image name  # noqa: E501

        :return: The name of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ImagesImageDetailsStruct.

        Image name  # noqa: E501

        :param name: The name of this ImagesImageDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def access(self):
        """Gets the access of this ImagesImageDetailsStruct.  # noqa: E501

        Image access state  # noqa: E501

        :return: The access of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._access

    @access.setter
    def access(self, access):
        """Sets the access of this ImagesImageDetailsStruct.

        Image access state  # noqa: E501

        :param access: The access of this ImagesImageDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and access is None:
            raise ValueError("Invalid value for `access`, must not be `None`")  # noqa: E501
        allowed_values = ["PUBLIC", "PRIVATE"]  # noqa: E501
        if self._configuration.client_side_validation and access not in allowed_values:
            raise ValueError(
                "Invalid value for `access` ({0}), must be one of {1}".format(access, allowed_values)  # noqa: E501
            )

        self._access = access

    @property
    def status(self):
        """Gets the status of this ImagesImageDetailsStruct.  # noqa: E501

        Image status  # noqa: E501

        :return: The status of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ImagesImageDetailsStruct.

        Image status  # noqa: E501

        :param status: The status of this ImagesImageDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501
        allowed_values = [
            "MODELED",
            "CREATED",
            "DELETED",
            "DESTROYED",
            "TOBEDESTROYED",
            "DISABLED",
            "ERROR",
            "CREATING",
            "DELETING",
            "OBSOLETED",
        ]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    @property
    def size(self):
        """Gets the size of this ImagesImageDetailsStruct.  # noqa: E501

        Image size  # noqa: E501

        :return: The size of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this ImagesImageDetailsStruct.

        Image size  # noqa: E501

        :param size: The size of this ImagesImageDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and size is None:
            raise ValueError("Invalid value for `size`, must not be `None`")  # noqa: E501

        self._size = size

    @property
    def create_time(self):
        """Gets the create_time of this ImagesImageDetailsStruct.  # noqa: E501

        Epoch for image create timestamp  # noqa: E501

        :return: The create_time of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ImagesImageDetailsStruct.

        Epoch for image create timestamp  # noqa: E501

        :param create_time: The create_time of this ImagesImageDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and create_time is None:
            raise ValueError("Invalid value for `create_time`, must not be `None`")  # noqa: E501

        self._create_time = create_time

    @property
    def update_time(self):
        """Gets the update_time of this ImagesImageDetailsStruct.  # noqa: E501

        Epoch for image update timestamp  # noqa: E501

        :return: The update_time of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ImagesImageDetailsStruct.

        Epoch for image update timestamp  # noqa: E501

        :param update_time: The update_time of this ImagesImageDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and update_time is None:
            raise ValueError("Invalid value for `update_time`, must not be `None`")  # noqa: E501

        self._update_time = update_time

    @property
    def url(self):
        """Gets the url of this ImagesImageDetailsStruct.  # noqa: E501

        Image url  # noqa: E501

        :return: The url of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this ImagesImageDetailsStruct.

        Image url  # noqa: E501

        :param url: The url of this ImagesImageDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and url is None:
            raise ValueError("Invalid value for `url`, must not be `None`")  # noqa: E501

        self._url = url

    @property
    def os(self):
        """Gets the os of this ImagesImageDetailsStruct.  # noqa: E501


        :return: The os of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: ImagesImageDetailsStructOs
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this ImagesImageDetailsStruct.


        :param os: The os of this ImagesImageDetailsStruct.  # noqa: E501
        :type: ImagesImageDetailsStructOs
        """
        if self._configuration.client_side_validation and os is None:
            raise ValueError("Invalid value for `os`, must not be `None`")  # noqa: E501

        self._os = os

    @property
    def type(self):
        """Gets the type of this ImagesImageDetailsStruct.  # noqa: E501

        Image type  # noqa: E501

        :return: The type of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ImagesImageDetailsStruct.

        Image type  # noqa: E501

        :param type: The type of this ImagesImageDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501
        allowed_values = ["NORMAL", "APPLIANCE"]  # noqa: E501
        if self._configuration.client_side_validation and type not in allowed_values:
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}".format(type, allowed_values)  # noqa: E501
            )

        self._type = type

    @property
    def vm_specifications(self):
        """Gets the vm_specifications of this ImagesImageDetailsStruct.  # noqa: E501


        :return: The vm_specifications of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: ImagesImageDetailsStructVmSpecifications
        """
        return self._vm_specifications

    @vm_specifications.setter
    def vm_specifications(self, vm_specifications):
        """Sets the vm_specifications of this ImagesImageDetailsStruct.


        :param vm_specifications: The vm_specifications of this ImagesImageDetailsStruct.  # noqa: E501
        :type: ImagesImageDetailsStructVmSpecifications
        """
        if self._configuration.client_side_validation and vm_specifications is None:
            raise ValueError("Invalid value for `vm_specifications`, must not be `None`")  # noqa: E501

        self._vm_specifications = vm_specifications

    @property
    def node_access(self):
        """Gets the node_access of this ImagesImageDetailsStruct.  # noqa: E501

        List of nodes that can access this image  # noqa: E501

        :return: The node_access of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: list[ImagesShortNodeStruct]
        """
        return self._node_access

    @node_access.setter
    def node_access(self, node_access):
        """Sets the node_access of this ImagesImageDetailsStruct.

        List of nodes that can access this image  # noqa: E501

        :param node_access: The node_access of this ImagesImageDetailsStruct.  # noqa: E501
        :type: list[ImagesShortNodeStruct]
        """
        if self._configuration.client_side_validation and node_access is None:
            raise ValueError("Invalid value for `node_access`, must not be `None`")  # noqa: E501

        self._node_access = node_access

    @property
    def account_access(self):
        """Gets the account_access of this ImagesImageDetailsStruct.  # noqa: E501


        :return: The account_access of this ImagesImageDetailsStruct.  # noqa: E501
        :rtype: ImagesImageDetailsStructAccountAccess
        """
        return self._account_access

    @account_access.setter
    def account_access(self, account_access):
        """Sets the account_access of this ImagesImageDetailsStruct.


        :param account_access: The account_access of this ImagesImageDetailsStruct.  # noqa: E501
        :type: ImagesImageDetailsStructAccountAccess
        """
        if self._configuration.client_side_validation and account_access is None:
            raise ValueError("Invalid value for `account_access`, must not be `None`")  # noqa: E501

        self._account_access = account_access

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ImagesImageDetailsStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ImagesImageDetailsStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ImagesImageDetailsStruct):
            return True

        return self.to_dict() != other.to_dict()
