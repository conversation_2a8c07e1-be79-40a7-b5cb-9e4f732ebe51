# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ObjectspacesObjectspaceDetailsStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "name": "str",
        "status": "str",
        "creation_time": "int",
        "update_time": "int",
        "account": "ObjectspacesObjectspaceDetailsStructAccount",
        "access_key": "str",
        "secret_key": "str",
        "prefix": "str",
        "audits": "list[ObjectspacesAuditInfo]",
        "buckets": "list[ObjectspacesBucketInfo]",
        "user_access": "list[ObjectspacesACEUserStruct]",
        "group_access": "list[ObjectspacesACEGroupStruct]",
        "connected_cloudspaces": "list[ObjectspacesCloudspaceInfo]",
    }

    attribute_map = {
        "id": "id",
        "name": "name",
        "status": "status",
        "creation_time": "creation_time",
        "update_time": "update_time",
        "account": "account",
        "access_key": "access_key",
        "secret_key": "secret_key",
        "prefix": "prefix",
        "audits": "audits",
        "buckets": "buckets",
        "user_access": "user_access",
        "group_access": "group_access",
        "connected_cloudspaces": "connected_cloudspaces",
    }

    def __init__(
        self,
        id=None,
        name=None,
        status=None,
        creation_time=None,
        update_time=None,
        account=None,
        access_key=None,
        secret_key=None,
        prefix=None,
        audits=None,
        buckets=None,
        user_access=None,
        group_access=None,
        connected_cloudspaces=None,
        _configuration=None,
    ):  # noqa: E501
        """ObjectspacesObjectspaceDetailsStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._status = None
        self._creation_time = None
        self._update_time = None
        self._account = None
        self._access_key = None
        self._secret_key = None
        self._prefix = None
        self._audits = None
        self._buckets = None
        self._user_access = None
        self._group_access = None
        self._connected_cloudspaces = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.status = status
        self.creation_time = creation_time
        self.update_time = update_time
        self.account = account
        self.access_key = access_key
        self.secret_key = secret_key
        self.prefix = prefix
        self.audits = audits
        self.buckets = buckets
        self.user_access = user_access
        self.group_access = group_access
        self.connected_cloudspaces = connected_cloudspaces

    @property
    def id(self):
        """Gets the id of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        id of objectspace  # noqa: E501

        :return: The id of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ObjectspacesObjectspaceDetailsStruct.

        id of objectspace  # noqa: E501

        :param id: The id of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        name of objectspace  # noqa: E501

        :return: The name of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ObjectspacesObjectspaceDetailsStruct.

        name of objectspace  # noqa: E501

        :param name: The name of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def status(self):
        """Gets the status of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        status of objectspace  # noqa: E501

        :return: The status of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ObjectspacesObjectspaceDetailsStruct.

        status of objectspace  # noqa: E501

        :param status: The status of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    @property
    def creation_time(self):
        """Gets the creation_time of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        creation timestamp of the objectspace  # noqa: E501

        :return: The creation_time of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this ObjectspacesObjectspaceDetailsStruct.

        creation timestamp of the objectspace  # noqa: E501

        :param creation_time: The creation_time of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and creation_time is None:
            raise ValueError("Invalid value for `creation_time`, must not be `None`")  # noqa: E501

        self._creation_time = creation_time

    @property
    def update_time(self):
        """Gets the update_time of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        update timestamp of the objectspace  # noqa: E501

        :return: The update_time of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ObjectspacesObjectspaceDetailsStruct.

        update timestamp of the objectspace  # noqa: E501

        :param update_time: The update_time of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and update_time is None:
            raise ValueError("Invalid value for `update_time`, must not be `None`")  # noqa: E501

        self._update_time = update_time

    @property
    def account(self):
        """Gets the account of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501


        :return: The account of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: ObjectspacesObjectspaceDetailsStructAccount
        """
        return self._account

    @account.setter
    def account(self, account):
        """Sets the account of this ObjectspacesObjectspaceDetailsStruct.


        :param account: The account of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: ObjectspacesObjectspaceDetailsStructAccount
        """
        if self._configuration.client_side_validation and account is None:
            raise ValueError("Invalid value for `account`, must not be `None`")  # noqa: E501

        self._account = account

    @property
    def access_key(self):
        """Gets the access_key of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        access key of objectspace  # noqa: E501

        :return: The access_key of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._access_key

    @access_key.setter
    def access_key(self, access_key):
        """Sets the access_key of this ObjectspacesObjectspaceDetailsStruct.

        access key of objectspace  # noqa: E501

        :param access_key: The access_key of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and access_key is None:
            raise ValueError("Invalid value for `access_key`, must not be `None`")  # noqa: E501

        self._access_key = access_key

    @property
    def secret_key(self):
        """Gets the secret_key of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        secret key of objectspace  # noqa: E501

        :return: The secret_key of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._secret_key

    @secret_key.setter
    def secret_key(self, secret_key):
        """Sets the secret_key of this ObjectspacesObjectspaceDetailsStruct.

        secret key of objectspace  # noqa: E501

        :param secret_key: The secret_key of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and secret_key is None:
            raise ValueError("Invalid value for `secret_key`, must not be `None`")  # noqa: E501

        self._secret_key = secret_key

    @property
    def prefix(self):
        """Gets the prefix of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        prefix for the namespaces in objectspace  # noqa: E501

        :return: The prefix of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: str
        """
        return self._prefix

    @prefix.setter
    def prefix(self, prefix):
        """Sets the prefix of this ObjectspacesObjectspaceDetailsStruct.

        prefix for the namespaces in objectspace  # noqa: E501

        :param prefix: The prefix of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and prefix is None:
            raise ValueError("Invalid value for `prefix`, must not be `None`")  # noqa: E501

        self._prefix = prefix

    @property
    def audits(self):
        """Gets the audits of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        Audit list associated with the objectspace  # noqa: E501

        :return: The audits of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: list[ObjectspacesAuditInfo]
        """
        return self._audits

    @audits.setter
    def audits(self, audits):
        """Sets the audits of this ObjectspacesObjectspaceDetailsStruct.

        Audit list associated with the objectspace  # noqa: E501

        :param audits: The audits of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: list[ObjectspacesAuditInfo]
        """
        if self._configuration.client_side_validation and audits is None:
            raise ValueError("Invalid value for `audits`, must not be `None`")  # noqa: E501

        self._audits = audits

    @property
    def buckets(self):
        """Gets the buckets of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        connected buckets to objectspace  # noqa: E501

        :return: The buckets of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: list[ObjectspacesBucketInfo]
        """
        return self._buckets

    @buckets.setter
    def buckets(self, buckets):
        """Sets the buckets of this ObjectspacesObjectspaceDetailsStruct.

        connected buckets to objectspace  # noqa: E501

        :param buckets: The buckets of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: list[ObjectspacesBucketInfo]
        """
        if self._configuration.client_side_validation and buckets is None:
            raise ValueError("Invalid value for `buckets`, must not be `None`")  # noqa: E501

        self._buckets = buckets

    @property
    def user_access(self):
        """Gets the user_access of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        List containing all user accesses  # noqa: E501

        :return: The user_access of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: list[ObjectspacesACEUserStruct]
        """
        return self._user_access

    @user_access.setter
    def user_access(self, user_access):
        """Sets the user_access of this ObjectspacesObjectspaceDetailsStruct.

        List containing all user accesses  # noqa: E501

        :param user_access: The user_access of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: list[ObjectspacesACEUserStruct]
        """
        if self._configuration.client_side_validation and user_access is None:
            raise ValueError("Invalid value for `user_access`, must not be `None`")  # noqa: E501

        self._user_access = user_access

    @property
    def group_access(self):
        """Gets the group_access of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        List containing all group accesses  # noqa: E501

        :return: The group_access of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: list[ObjectspacesACEGroupStruct]
        """
        return self._group_access

    @group_access.setter
    def group_access(self, group_access):
        """Sets the group_access of this ObjectspacesObjectspaceDetailsStruct.

        List containing all group accesses  # noqa: E501

        :param group_access: The group_access of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: list[ObjectspacesACEGroupStruct]
        """
        if self._configuration.client_side_validation and group_access is None:
            raise ValueError("Invalid value for `group_access`, must not be `None`")  # noqa: E501

        self._group_access = group_access

    @property
    def connected_cloudspaces(self):
        """Gets the connected_cloudspaces of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501

        connected cloudspaces to this objectspace  # noqa: E501

        :return: The connected_cloudspaces of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :rtype: list[ObjectspacesCloudspaceInfo]
        """
        return self._connected_cloudspaces

    @connected_cloudspaces.setter
    def connected_cloudspaces(self, connected_cloudspaces):
        """Sets the connected_cloudspaces of this ObjectspacesObjectspaceDetailsStruct.

        connected cloudspaces to this objectspace  # noqa: E501

        :param connected_cloudspaces: The connected_cloudspaces of this ObjectspacesObjectspaceDetailsStruct.  # noqa: E501
        :type: list[ObjectspacesCloudspaceInfo]
        """
        if self._configuration.client_side_validation and connected_cloudspaces is None:
            raise ValueError("Invalid value for `connected_cloudspaces`, must not be `None`")  # noqa: E501

        self._connected_cloudspaces = connected_cloudspaces

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ObjectspacesObjectspaceDetailsStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ObjectspacesObjectspaceDetailsStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ObjectspacesObjectspaceDetailsStruct):
            return True

        return self.to_dict() != other.to_dict()
