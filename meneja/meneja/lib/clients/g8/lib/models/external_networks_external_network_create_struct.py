# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class ExternalNetworksExternalNetworkCreateStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "name": "str",
        "account_id": "int",
        "billable": "bool",
        "dhcp": "bool",
        "gateway": "str",
        "network": "str",
        "subnet_mask": "str",
        "ips": "list[ExternalNetworksExternalNetworkIpStructLIST]",
        "ping_ips": "list[IpShort]",
        "protection": "bool",
        "speed_limit": "int",
        "sriov": "bool",
        "vlan": "int",
        "account_ids": "list[int]",
    }

    attribute_map = {
        "name": "name",
        "account_id": "account_id",
        "billable": "billable",
        "dhcp": "dhcp",
        "gateway": "gateway",
        "network": "network",
        "subnet_mask": "subnet_mask",
        "ips": "ips",
        "ping_ips": "ping_ips",
        "protection": "protection",
        "speed_limit": "speed_limit",
        "sriov": "sriov",
        "vlan": "vlan",
        "account_ids": "account_ids",
    }

    def __init__(
        self,
        name=None,
        account_id=None,
        billable=None,
        dhcp=None,
        gateway=None,
        network=None,
        subnet_mask=None,
        ips=None,
        ping_ips=None,
        protection=None,
        speed_limit=None,
        sriov=None,
        vlan=None,
        account_ids=None,
        _configuration=None,
    ):  # noqa: E501
        """ExternalNetworksExternalNetworkCreateStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._account_id = None
        self._billable = None
        self._dhcp = None
        self._gateway = None
        self._network = None
        self._subnet_mask = None
        self._ips = None
        self._ping_ips = None
        self._protection = None
        self._speed_limit = None
        self._sriov = None
        self._vlan = None
        self._account_ids = None
        self.discriminator = None

        self.name = name
        if account_id is not None:
            self.account_id = account_id
        self.billable = billable
        self.dhcp = dhcp
        self.gateway = gateway
        self.network = network
        self.subnet_mask = subnet_mask
        if ips is not None:
            self.ips = ips
        self.ping_ips = ping_ips
        self.protection = protection
        self.speed_limit = speed_limit
        self.sriov = sriov
        self.vlan = vlan
        if account_ids is not None:
            self.account_ids = account_ids

    @property
    def name(self):
        """Gets the name of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        External network name  # noqa: E501

        :return: The name of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ExternalNetworksExternalNetworkCreateStruct.

        External network name  # noqa: E501

        :param name: The name of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def account_id(self):
        """Gets the account_id of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        account id which can use this network. empty list means it is a public external network. kept for backwards compitablity and to be removed  # noqa: E501

        :return: The account_id of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this ExternalNetworksExternalNetworkCreateStruct.

        account id which can use this network. empty list means it is a public external network. kept for backwards compitablity and to be removed  # noqa: E501

        :param account_id: The account_id of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def billable(self):
        """Gets the billable of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Indicates if billing records need to be gathered for the use  # noqa: E501

        :return: The billable of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: bool
        """
        return self._billable

    @billable.setter
    def billable(self, billable):
        """Sets the billable of this ExternalNetworksExternalNetworkCreateStruct.

        Indicates if billing records need to be gathered for the use  # noqa: E501

        :param billable: The billable of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and billable is None:
            raise ValueError("Invalid value for `billable`, must not be `None`")  # noqa: E501

        self._billable = billable

    @property
    def dhcp(self):
        """Gets the dhcp of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Indicates if a dhcp server automatically hands out ip addresses  # noqa: E501

        :return: The dhcp of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: bool
        """
        return self._dhcp

    @dhcp.setter
    def dhcp(self, dhcp):
        """Sets the dhcp of this ExternalNetworksExternalNetworkCreateStruct.

        Indicates if a dhcp server automatically hands out ip addresses  # noqa: E501

        :param dhcp: The dhcp of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and dhcp is None:
            raise ValueError("Invalid value for `dhcp`, must not be `None`")  # noqa: E501

        self._dhcp = dhcp

    @property
    def gateway(self):
        """Gets the gateway of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Gateway to the upstream network  # noqa: E501

        :return: The gateway of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: str
        """
        return self._gateway

    @gateway.setter
    def gateway(self, gateway):
        """Sets the gateway of this ExternalNetworksExternalNetworkCreateStruct.

        Gateway to the upstream network  # noqa: E501

        :param gateway: The gateway of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and gateway is None:
            raise ValueError("Invalid value for `gateway`, must not be `None`")  # noqa: E501

        self._gateway = gateway

    @property
    def network(self):
        """Gets the network of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Network of the pool  # noqa: E501

        :return: The network of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: str
        """
        return self._network

    @network.setter
    def network(self, network):
        """Sets the network of this ExternalNetworksExternalNetworkCreateStruct.

        Network of the pool  # noqa: E501

        :param network: The network of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and network is None:
            raise ValueError("Invalid value for `network`, must not be `None`")  # noqa: E501

        self._network = network

    @property
    def subnet_mask(self):
        """Gets the subnet_mask of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Subnet mask of the pool  # noqa: E501

        :return: The subnet_mask of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: str
        """
        return self._subnet_mask

    @subnet_mask.setter
    def subnet_mask(self, subnet_mask):
        """Sets the subnet_mask of this ExternalNetworksExternalNetworkCreateStruct.

        Subnet mask of the pool  # noqa: E501

        :param subnet_mask: The subnet_mask of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_mask is None:
            raise ValueError("Invalid value for `subnet_mask`, must not be `None`")  # noqa: E501

        self._subnet_mask = subnet_mask

    @property
    def ips(self):
        """Gets the ips of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501


        :return: The ips of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: list[ExternalNetworksExternalNetworkIpStructLIST]
        """
        return self._ips

    @ips.setter
    def ips(self, ips):
        """Sets the ips of this ExternalNetworksExternalNetworkCreateStruct.


        :param ips: The ips of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: list[ExternalNetworksExternalNetworkIpStructLIST]
        """

        self._ips = ips

    @property
    def ping_ips(self):
        """Gets the ping_ips of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        List of ips to be pinged to check the external network health  # noqa: E501

        :return: The ping_ips of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: list[IpShort]
        """
        return self._ping_ips

    @ping_ips.setter
    def ping_ips(self, ping_ips):
        """Sets the ping_ips of this ExternalNetworksExternalNetworkCreateStruct.

        List of ips to be pinged to check the external network health  # noqa: E501

        :param ping_ips: The ping_ips of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: list[IpShort]
        """
        if self._configuration.client_side_validation and ping_ips is None:
            raise ValueError("Invalid value for `ping_ips`, must not be `None`")  # noqa: E501

        self._ping_ips = ping_ips

    @property
    def protection(self):
        """Gets the protection of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Indicates if this external network has ip-mac protection or not.  # noqa: E501

        :return: The protection of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: bool
        """
        return self._protection

    @protection.setter
    def protection(self, protection):
        """Sets the protection of this ExternalNetworksExternalNetworkCreateStruct.

        Indicates if this external network has ip-mac protection or not.  # noqa: E501

        :param protection: The protection of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and protection is None:
            raise ValueError("Invalid value for `protection`, must not be `None`")  # noqa: E501

        self._protection = protection

    @property
    def speed_limit(self):
        """Gets the speed_limit of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Maximum bandwith speed configured on the network interfaces expressed in Kbit. 0 is the default which gets translated into 1 Gbit  # noqa: E501

        :return: The speed_limit of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: int
        """
        return self._speed_limit

    @speed_limit.setter
    def speed_limit(self, speed_limit):
        """Sets the speed_limit of this ExternalNetworksExternalNetworkCreateStruct.

        Maximum bandwith speed configured on the network interfaces expressed in Kbit. 0 is the default which gets translated into 1 Gbit  # noqa: E501

        :param speed_limit: The speed_limit of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and speed_limit is None:
            raise ValueError("Invalid value for `speed_limit`, must not be `None`")  # noqa: E501

        self._speed_limit = speed_limit

    @property
    def sriov(self):
        """Gets the sriov of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Make use of SR-IOV interfaces for this network  # noqa: E501

        :return: The sriov of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: bool
        """
        return self._sriov

    @sriov.setter
    def sriov(self, sriov):
        """Sets the sriov of this ExternalNetworksExternalNetworkCreateStruct.

        Make use of SR-IOV interfaces for this network  # noqa: E501

        :param sriov: The sriov of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and sriov is None:
            raise ValueError("Invalid value for `sriov`, must not be `None`")  # noqa: E501

        self._sriov = sriov

    @property
    def vlan(self):
        """Gets the vlan of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        Vlan tag used for isolating this external network  # noqa: E501

        :return: The vlan of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: int
        """
        return self._vlan

    @vlan.setter
    def vlan(self, vlan):
        """Sets the vlan of this ExternalNetworksExternalNetworkCreateStruct.

        Vlan tag used for isolating this external network  # noqa: E501

        :param vlan: The vlan of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and vlan is None:
            raise ValueError("Invalid value for `vlan`, must not be `None`")  # noqa: E501

        self._vlan = vlan

    @property
    def account_ids(self):
        """Gets the account_ids of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501

        List of accounts which can use this network. empty list means it is a public external network.  # noqa: E501

        :return: The account_ids of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :rtype: list[int]
        """
        return self._account_ids

    @account_ids.setter
    def account_ids(self, account_ids):
        """Sets the account_ids of this ExternalNetworksExternalNetworkCreateStruct.

        List of accounts which can use this network. empty list means it is a public external network.  # noqa: E501

        :param account_ids: The account_ids of this ExternalNetworksExternalNetworkCreateStruct.  # noqa: E501
        :type: list[int]
        """

        self._account_ids = account_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(ExternalNetworksExternalNetworkCreateStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExternalNetworksExternalNetworkCreateStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExternalNetworksExternalNetworkCreateStruct):
            return True

        return self.to_dict() != other.to_dict()
