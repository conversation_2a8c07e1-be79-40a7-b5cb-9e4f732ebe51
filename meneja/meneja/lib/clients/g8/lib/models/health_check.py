# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class HealthCheck(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "dynaqueue": "bool",
        "id": "str",
        "interval": "int",
        "jobguid": "str",
        "last_checked_timestamp": "int",
        "messages": "list[HealthCheckMessage]",
        "name": "str",
        "node_id": "int",
    }

    attribute_map = {
        "dynaqueue": "dynaqueue",
        "id": "id",
        "interval": "interval",
        "jobguid": "jobguid",
        "last_checked_timestamp": "last_checked_timestamp",
        "messages": "messages",
        "name": "name",
        "node_id": "node_id",
    }

    def __init__(
        self,
        dynaqueue=None,
        id=None,
        interval=None,
        jobguid=None,
        last_checked_timestamp=None,
        messages=None,
        name=None,
        node_id=None,
        _configuration=None,
    ):  # noqa: E501
        """HealthCheck - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dynaqueue = None
        self._id = None
        self._interval = None
        self._jobguid = None
        self._last_checked_timestamp = None
        self._messages = None
        self._name = None
        self._node_id = None
        self.discriminator = None

        if dynaqueue is not None:
            self.dynaqueue = dynaqueue
        if id is not None:
            self.id = id
        if interval is not None:
            self.interval = interval
        if jobguid is not None:
            self.jobguid = jobguid
        if last_checked_timestamp is not None:
            self.last_checked_timestamp = last_checked_timestamp
        if messages is not None:
            self.messages = messages
        if name is not None:
            self.name = name
        if node_id is not None:
            self.node_id = node_id

    @property
    def dynaqueue(self):
        """Gets the dynaqueue of this HealthCheck.  # noqa: E501

        Switch that defines whether the task was executed in jumpscale or dynaqueue.  # noqa: E501

        :return: The dynaqueue of this HealthCheck.  # noqa: E501
        :rtype: bool
        """
        return self._dynaqueue

    @dynaqueue.setter
    def dynaqueue(self, dynaqueue):
        """Sets the dynaqueue of this HealthCheck.

        Switch that defines whether the task was executed in jumpscale or dynaqueue.  # noqa: E501

        :param dynaqueue: The dynaqueue of this HealthCheck.  # noqa: E501
        :type: bool
        """

        self._dynaqueue = dynaqueue

    @property
    def id(self):
        """Gets the id of this HealthCheck.  # noqa: E501

        None  # noqa: E501

        :return: The id of this HealthCheck.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this HealthCheck.

        None  # noqa: E501

        :param id: The id of this HealthCheck.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def interval(self):
        """Gets the interval of this HealthCheck.  # noqa: E501

        Time for how often the health check is run (cron time)  # noqa: E501

        :return: The interval of this HealthCheck.  # noqa: E501
        :rtype: int
        """
        return self._interval

    @interval.setter
    def interval(self, interval):
        """Sets the interval of this HealthCheck.

        Time for how often the health check is run (cron time)  # noqa: E501

        :param interval: The interval of this HealthCheck.  # noqa: E501
        :type: int
        """

        self._interval = interval

    @property
    def jobguid(self):
        """Gets the jobguid of this HealthCheck.  # noqa: E501

        Dynaqueue task id that executed the health check  # noqa: E501

        :return: The jobguid of this HealthCheck.  # noqa: E501
        :rtype: str
        """
        return self._jobguid

    @jobguid.setter
    def jobguid(self, jobguid):
        """Sets the jobguid of this HealthCheck.

        Dynaqueue task id that executed the health check  # noqa: E501

        :param jobguid: The jobguid of this HealthCheck.  # noqa: E501
        :type: str
        """

        self._jobguid = jobguid

    @property
    def last_checked_timestamp(self):
        """Gets the last_checked_timestamp of this HealthCheck.  # noqa: E501

        Epoch of when the last time the health check is executed  # noqa: E501

        :return: The last_checked_timestamp of this HealthCheck.  # noqa: E501
        :rtype: int
        """
        return self._last_checked_timestamp

    @last_checked_timestamp.setter
    def last_checked_timestamp(self, last_checked_timestamp):
        """Sets the last_checked_timestamp of this HealthCheck.

        Epoch of when the last time the health check is executed  # noqa: E501

        :param last_checked_timestamp: The last_checked_timestamp of this HealthCheck.  # noqa: E501
        :type: int
        """

        self._last_checked_timestamp = last_checked_timestamp

    @property
    def messages(self):
        """Gets the messages of this HealthCheck.  # noqa: E501

        A list of health check result  # noqa: E501

        :return: The messages of this HealthCheck.  # noqa: E501
        :rtype: list[HealthCheckMessage]
        """
        return self._messages

    @messages.setter
    def messages(self, messages):
        """Sets the messages of this HealthCheck.

        A list of health check result  # noqa: E501

        :param messages: The messages of this HealthCheck.  # noqa: E501
        :type: list[HealthCheckMessage]
        """

        self._messages = messages

    @property
    def name(self):
        """Gets the name of this HealthCheck.  # noqa: E501

        Unique string for the health check, currently is the module name  # noqa: E501

        :return: The name of this HealthCheck.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this HealthCheck.

        Unique string for the health check, currently is the module name  # noqa: E501

        :param name: The name of this HealthCheck.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_id(self):
        """Gets the node_id of this HealthCheck.  # noqa: E501

        Node id where the health check run  # noqa: E501

        :return: The node_id of this HealthCheck.  # noqa: E501
        :rtype: int
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this HealthCheck.

        Node id where the health check run  # noqa: E501

        :param node_id: The node_id of this HealthCheck.  # noqa: E501
        :type: int
        """

        self._node_id = node_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(HealthCheck, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HealthCheck):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HealthCheck):
            return True

        return self.to_dict() != other.to_dict()
