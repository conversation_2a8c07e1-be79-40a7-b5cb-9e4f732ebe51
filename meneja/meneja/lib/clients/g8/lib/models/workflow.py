# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class Workflow(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "str",
        "title": "str",
        "status": "str",
        "created_on": "float",
        "executed_on": "float",
        "picked_up_on": "float",
    }

    attribute_map = {
        "id": "id",
        "title": "title",
        "status": "status",
        "created_on": "created_on",
        "executed_on": "executed_on",
        "picked_up_on": "picked_up_on",
    }

    def __init__(
        self,
        id=None,
        title=None,
        status=None,
        created_on=None,
        executed_on=None,
        picked_up_on=None,
        _configuration=None,
    ):  # noqa: E501
        """Workflow - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._title = None
        self._status = None
        self._created_on = None
        self._executed_on = None
        self._picked_up_on = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if title is not None:
            self.title = title
        if status is not None:
            self.status = status
        if created_on is not None:
            self.created_on = created_on
        if executed_on is not None:
            self.executed_on = executed_on
        if picked_up_on is not None:
            self.picked_up_on = picked_up_on

    @property
    def id(self):
        """Gets the id of this Workflow.  # noqa: E501

        Globally unique workflow id  # noqa: E501

        :return: The id of this Workflow.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this Workflow.

        Globally unique workflow id  # noqa: E501

        :param id: The id of this Workflow.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def title(self):
        """Gets the title of this Workflow.  # noqa: E501

        Short description of the workflow  # noqa: E501

        :return: The title of this Workflow.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this Workflow.

        Short description of the workflow  # noqa: E501

        :param title: The title of this Workflow.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def status(self):
        """Gets the status of this Workflow.  # noqa: E501

        Current status of the workflow  # noqa: E501

        :return: The status of this Workflow.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this Workflow.

        Current status of the workflow  # noqa: E501

        :param status: The status of this Workflow.  # noqa: E501
        :type: str
        """
        allowed_values = ["QUEUED", "RUNNING", "SUCCEEDED", "FAILED"]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    @property
    def created_on(self):
        """Gets the created_on of this Workflow.  # noqa: E501

        Epoch timestamp when the workflow was submitted  # noqa: E501

        :return: The created_on of this Workflow.  # noqa: E501
        :rtype: float
        """
        return self._created_on

    @created_on.setter
    def created_on(self, created_on):
        """Sets the created_on of this Workflow.

        Epoch timestamp when the workflow was submitted  # noqa: E501

        :param created_on: The created_on of this Workflow.  # noqa: E501
        :type: float
        """

        self._created_on = created_on

    @property
    def executed_on(self):
        """Gets the executed_on of this Workflow.  # noqa: E501

        Epoch timestamp when the workflow finished executing  # noqa: E501

        :return: The executed_on of this Workflow.  # noqa: E501
        :rtype: float
        """
        return self._executed_on

    @executed_on.setter
    def executed_on(self, executed_on):
        """Sets the executed_on of this Workflow.

        Epoch timestamp when the workflow finished executing  # noqa: E501

        :param executed_on: The executed_on of this Workflow.  # noqa: E501
        :type: float
        """

        self._executed_on = executed_on

    @property
    def picked_up_on(self):
        """Gets the picked_up_on of this Workflow.  # noqa: E501

        Epoch timestamp when the workflow was picked up by a worker  # noqa: E501

        :return: The picked_up_on of this Workflow.  # noqa: E501
        :rtype: float
        """
        return self._picked_up_on

    @picked_up_on.setter
    def picked_up_on(self, picked_up_on):
        """Sets the picked_up_on of this Workflow.

        Epoch timestamp when the workflow was picked up by a worker  # noqa: E501

        :param picked_up_on: The picked_up_on of this Workflow.  # noqa: E501
        :type: float
        """

        self._picked_up_on = picked_up_on

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(Workflow, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Workflow):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, Workflow):
            return True

        return self.to_dict() != other.to_dict()
