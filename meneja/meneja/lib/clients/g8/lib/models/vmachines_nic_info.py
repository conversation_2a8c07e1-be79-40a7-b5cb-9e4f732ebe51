# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VmachinesNicInfo(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"name": "str", "mac_address": "str", "type_": "str", "ip_address": "str", "network_id": "int"}

    attribute_map = {
        "name": "name",
        "mac_address": "mac_address",
        "type_": "type_",
        "ip_address": "ip_address",
        "network_id": "network_id",
    }

    def __init__(
        self, name=None, mac_address=None, type_=None, ip_address=None, network_id=None, _configuration=None
    ):  # noqa: E501
        """VmachinesNicInfo - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._mac_address = None
        self._type_ = None
        self._ip_address = None
        self._network_id = None
        self.discriminator = None

        self.name = name
        self.mac_address = mac_address
        self.type_ = type_
        self.ip_address = ip_address
        self.network_id = network_id

    @property
    def name(self):
        """Gets the name of this VmachinesNicInfo.  # noqa: E501

        Nic device name  # noqa: E501

        :return: The name of this VmachinesNicInfo.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VmachinesNicInfo.

        Nic device name  # noqa: E501

        :param name: The name of this VmachinesNicInfo.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def mac_address(self):
        """Gets the mac_address of this VmachinesNicInfo.  # noqa: E501

        Nic mac-address  # noqa: E501

        :return: The mac_address of this VmachinesNicInfo.  # noqa: E501
        :rtype: str
        """
        return self._mac_address

    @mac_address.setter
    def mac_address(self, mac_address):
        """Sets the mac_address of this VmachinesNicInfo.

        Nic mac-address  # noqa: E501

        :param mac_address: The mac_address of this VmachinesNicInfo.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and mac_address is None:
            raise ValueError("Invalid value for `mac_address`, must not be `None`")  # noqa: E501

        self._mac_address = mac_address

    @property
    def type_(self):
        """Gets the type_ of this VmachinesNicInfo.  # noqa: E501

        Nic type  # noqa: E501

        :return: The type_ of this VmachinesNicInfo.  # noqa: E501
        :rtype: str
        """
        return self._type_

    @type_.setter
    def type_(self, type_):
        """Sets the type_ of this VmachinesNicInfo.

        Nic type  # noqa: E501

        :param type_: The type_ of this VmachinesNicInfo.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type_ is None:
            raise ValueError("Invalid value for `type_`, must not be `None`")  # noqa: E501

        self._type_ = type_

    @property
    def ip_address(self):
        """Gets the ip_address of this VmachinesNicInfo.  # noqa: E501

        Nic ip address  # noqa: E501

        :return: The ip_address of this VmachinesNicInfo.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this VmachinesNicInfo.

        Nic ip address  # noqa: E501

        :param ip_address: The ip_address of this VmachinesNicInfo.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_address is None:
            raise ValueError("Invalid value for `ip_address`, must not be `None`")  # noqa: E501

        self._ip_address = ip_address

    @property
    def network_id(self):
        """Gets the network_id of this VmachinesNicInfo.  # noqa: E501

        Nic network id  # noqa: E501

        :return: The network_id of this VmachinesNicInfo.  # noqa: E501
        :rtype: int
        """
        return self._network_id

    @network_id.setter
    def network_id(self, network_id):
        """Sets the network_id of this VmachinesNicInfo.

        Nic network id  # noqa: E501

        :param network_id: The network_id of this VmachinesNicInfo.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and network_id is None:
            raise ValueError("Invalid value for `network_id`, must not be `None`")  # noqa: E501

        self._network_id = network_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VmachinesNicInfo, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VmachinesNicInfo):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VmachinesNicInfo):
            return True

        return self.to_dict() != other.to_dict()
