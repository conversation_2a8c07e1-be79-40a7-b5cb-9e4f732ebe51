# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class WorkflowsWorkflowStep(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "str",
        "title": "str",
        "status": "str",
        "created_on": "float",
        "queued_on": "float",
        "picked_up_on": "float",
        "executed_on": "float",
        "attempts": "int",
        "logs": "object",
        "worker_name": "str",
        "on_success": "object",
        "on_failure": "object",
    }

    attribute_map = {
        "id": "id",
        "title": "title",
        "status": "status",
        "created_on": "created_on",
        "queued_on": "queued_on",
        "picked_up_on": "picked_up_on",
        "executed_on": "executed_on",
        "attempts": "attempts",
        "logs": "logs",
        "worker_name": "worker_name",
        "on_success": "on_success",
        "on_failure": "on_failure",
    }

    def __init__(
        self,
        id=None,
        title=None,
        status=None,
        created_on=None,
        queued_on=None,
        picked_up_on=None,
        executed_on=None,
        attempts=None,
        logs=None,
        worker_name=None,
        on_success=None,
        on_failure=None,
        _configuration=None,
    ):  # noqa: E501
        """WorkflowsWorkflowStep - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._title = None
        self._status = None
        self._created_on = None
        self._queued_on = None
        self._picked_up_on = None
        self._executed_on = None
        self._attempts = None
        self._logs = None
        self._worker_name = None
        self._on_success = None
        self._on_failure = None
        self.discriminator = None

        self.id = id
        self.title = title
        self.status = status
        self.created_on = created_on
        self.queued_on = queued_on
        self.picked_up_on = picked_up_on
        self.executed_on = executed_on
        self.attempts = attempts
        self.logs = logs
        self.worker_name = worker_name
        if on_success is not None:
            self.on_success = on_success
        if on_failure is not None:
            self.on_failure = on_failure

    @property
    def id(self):
        """Gets the id of this WorkflowsWorkflowStep.  # noqa: E501

        Globally unique Workflow id  # noqa: E501

        :return: The id of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this WorkflowsWorkflowStep.

        Globally unique Workflow id  # noqa: E501

        :param id: The id of this WorkflowsWorkflowStep.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def title(self):
        """Gets the title of this WorkflowsWorkflowStep.  # noqa: E501

        Short description of the workflow  # noqa: E501

        :return: The title of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this WorkflowsWorkflowStep.

        Short description of the workflow  # noqa: E501

        :param title: The title of this WorkflowsWorkflowStep.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and title is None:
            raise ValueError("Invalid value for `title`, must not be `None`")  # noqa: E501

        self._title = title

    @property
    def status(self):
        """Gets the status of this WorkflowsWorkflowStep.  # noqa: E501

        Current status of the workflow  # noqa: E501

        :return: The status of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this WorkflowsWorkflowStep.

        Current status of the workflow  # noqa: E501

        :param status: The status of this WorkflowsWorkflowStep.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    @property
    def created_on(self):
        """Gets the created_on of this WorkflowsWorkflowStep.  # noqa: E501

        Epoch timestamp when the workflow was submitted  # noqa: E501

        :return: The created_on of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: float
        """
        return self._created_on

    @created_on.setter
    def created_on(self, created_on):
        """Sets the created_on of this WorkflowsWorkflowStep.

        Epoch timestamp when the workflow was submitted  # noqa: E501

        :param created_on: The created_on of this WorkflowsWorkflowStep.  # noqa: E501
        :type: float
        """
        if self._configuration.client_side_validation and created_on is None:
            raise ValueError("Invalid value for `created_on`, must not be `None`")  # noqa: E501

        self._created_on = created_on

    @property
    def queued_on(self):
        """Gets the queued_on of this WorkflowsWorkflowStep.  # noqa: E501

        Epoch timestamp when the workflow was queued  # noqa: E501

        :return: The queued_on of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: float
        """
        return self._queued_on

    @queued_on.setter
    def queued_on(self, queued_on):
        """Sets the queued_on of this WorkflowsWorkflowStep.

        Epoch timestamp when the workflow was queued  # noqa: E501

        :param queued_on: The queued_on of this WorkflowsWorkflowStep.  # noqa: E501
        :type: float
        """
        if self._configuration.client_side_validation and queued_on is None:
            raise ValueError("Invalid value for `queued_on`, must not be `None`")  # noqa: E501

        self._queued_on = queued_on

    @property
    def picked_up_on(self):
        """Gets the picked_up_on of this WorkflowsWorkflowStep.  # noqa: E501

        Epoch timestamp when the workflow was picked up by a worker  # noqa: E501

        :return: The picked_up_on of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: float
        """
        return self._picked_up_on

    @picked_up_on.setter
    def picked_up_on(self, picked_up_on):
        """Sets the picked_up_on of this WorkflowsWorkflowStep.

        Epoch timestamp when the workflow was picked up by a worker  # noqa: E501

        :param picked_up_on: The picked_up_on of this WorkflowsWorkflowStep.  # noqa: E501
        :type: float
        """
        if self._configuration.client_side_validation and picked_up_on is None:
            raise ValueError("Invalid value for `picked_up_on`, must not be `None`")  # noqa: E501

        self._picked_up_on = picked_up_on

    @property
    def executed_on(self):
        """Gets the executed_on of this WorkflowsWorkflowStep.  # noqa: E501

        Epoch timestamp when the workflow was executed  # noqa: E501

        :return: The executed_on of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: float
        """
        return self._executed_on

    @executed_on.setter
    def executed_on(self, executed_on):
        """Sets the executed_on of this WorkflowsWorkflowStep.

        Epoch timestamp when the workflow was executed  # noqa: E501

        :param executed_on: The executed_on of this WorkflowsWorkflowStep.  # noqa: E501
        :type: float
        """
        if self._configuration.client_side_validation and executed_on is None:
            raise ValueError("Invalid value for `executed_on`, must not be `None`")  # noqa: E501

        self._executed_on = executed_on

    @property
    def attempts(self):
        """Gets the attempts of this WorkflowsWorkflowStep.  # noqa: E501

        Current attempt  # noqa: E501

        :return: The attempts of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: int
        """
        return self._attempts

    @attempts.setter
    def attempts(self, attempts):
        """Sets the attempts of this WorkflowsWorkflowStep.

        Current attempt  # noqa: E501

        :param attempts: The attempts of this WorkflowsWorkflowStep.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and attempts is None:
            raise ValueError("Invalid value for `attempts`, must not be `None`")  # noqa: E501

        self._attempts = attempts

    @property
    def logs(self):
        """Gets the logs of this WorkflowsWorkflowStep.  # noqa: E501

        Logs emitted in the course of executing the task  # noqa: E501

        :return: The logs of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: object
        """
        return self._logs

    @logs.setter
    def logs(self, logs):
        """Sets the logs of this WorkflowsWorkflowStep.

        Logs emitted in the course of executing the task  # noqa: E501

        :param logs: The logs of this WorkflowsWorkflowStep.  # noqa: E501
        :type: object
        """
        if self._configuration.client_side_validation and logs is None:
            raise ValueError("Invalid value for `logs`, must not be `None`")  # noqa: E501

        self._logs = logs

    @property
    def worker_name(self):
        """Gets the worker_name of this WorkflowsWorkflowStep.  # noqa: E501

        Name of worked which executed the task  # noqa: E501

        :return: The worker_name of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: str
        """
        return self._worker_name

    @worker_name.setter
    def worker_name(self, worker_name):
        """Sets the worker_name of this WorkflowsWorkflowStep.

        Name of worked which executed the task  # noqa: E501

        :param worker_name: The worker_name of this WorkflowsWorkflowStep.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and worker_name is None:
            raise ValueError("Invalid value for `worker_name`, must not be `None`")  # noqa: E501

        self._worker_name = worker_name

    @property
    def on_success(self):
        """Gets the on_success of this WorkflowsWorkflowStep.  # noqa: E501

        TaskStatus of task that is executed on the successful completion of this TaskStatus  # noqa: E501

        :return: The on_success of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: object
        """
        return self._on_success

    @on_success.setter
    def on_success(self, on_success):
        """Sets the on_success of this WorkflowsWorkflowStep.

        TaskStatus of task that is executed on the successful completion of this TaskStatus  # noqa: E501

        :param on_success: The on_success of this WorkflowsWorkflowStep.  # noqa: E501
        :type: object
        """

        self._on_success = on_success

    @property
    def on_failure(self):
        """Gets the on_failure of this WorkflowsWorkflowStep.  # noqa: E501

        TaskStatus of task that is executed on the failed completion of this TaskStatus  # noqa: E501

        :return: The on_failure of this WorkflowsWorkflowStep.  # noqa: E501
        :rtype: object
        """
        return self._on_failure

    @on_failure.setter
    def on_failure(self, on_failure):
        """Sets the on_failure of this WorkflowsWorkflowStep.

        TaskStatus of task that is executed on the failed completion of this TaskStatus  # noqa: E501

        :param on_failure: The on_failure of this WorkflowsWorkflowStep.  # noqa: E501
        :type: object
        """

        self._on_failure = on_failure

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(WorkflowsWorkflowStep, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WorkflowsWorkflowStep):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WorkflowsWorkflowStep):
            return True

        return self.to_dict() != other.to_dict()
