# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class NodesNodePowerStatusStruct(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"id": "int", "name": "str", "status": "str", "ping_status": "str"}

    attribute_map = {"id": "id", "name": "name", "status": "status", "ping_status": "ping_status"}

    def __init__(self, id=None, name=None, status=None, ping_status=None, _configuration=None):  # noqa: E501
        """NodesNodePowerStatusStruct - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._status = None
        self._ping_status = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.status = status
        self.ping_status = ping_status

    @property
    def id(self):
        """Gets the id of this NodesNodePowerStatusStruct.  # noqa: E501

        Node id  # noqa: E501

        :return: The id of this NodesNodePowerStatusStruct.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this NodesNodePowerStatusStruct.

        Node id  # noqa: E501

        :param id: The id of this NodesNodePowerStatusStruct.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this NodesNodePowerStatusStruct.  # noqa: E501

        Node name  # noqa: E501

        :return: The name of this NodesNodePowerStatusStruct.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this NodesNodePowerStatusStruct.

        Node name  # noqa: E501

        :param name: The name of this NodesNodePowerStatusStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def status(self):
        """Gets the status of this NodesNodePowerStatusStruct.  # noqa: E501

        Node on/off status  # noqa: E501

        :return: The status of this NodesNodePowerStatusStruct.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this NodesNodePowerStatusStruct.

        Node on/off status  # noqa: E501

        :param status: The status of this NodesNodePowerStatusStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501
        allowed_values = ["ok", "error"]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    @property
    def ping_status(self):
        """Gets the ping_status of this NodesNodePowerStatusStruct.  # noqa: E501

        Node ping status  # noqa: E501

        :return: The ping_status of this NodesNodePowerStatusStruct.  # noqa: E501
        :rtype: str
        """
        return self._ping_status

    @ping_status.setter
    def ping_status(self, ping_status):
        """Sets the ping_status of this NodesNodePowerStatusStruct.

        Node ping status  # noqa: E501

        :param ping_status: The ping_status of this NodesNodePowerStatusStruct.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ping_status is None:
            raise ValueError("Invalid value for `ping_status`, must not be `None`")  # noqa: E501
        allowed_values = ["ok", "error"]  # noqa: E501
        if self._configuration.client_side_validation and ping_status not in allowed_values:
            raise ValueError(
                "Invalid value for `ping_status` ({0}), must be one of {1}".format(  # noqa: E501
                    ping_status, allowed_values
                )
            )

        self._ping_status = ping_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(NodesNodePowerStatusStruct, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodesNodePowerStatusStruct):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodesNodePowerStatusStruct):
            return True

        return self.to_dict() != other.to_dict()
