# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class DisksApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def g8_admin_add_disk(self, payload, **kwargs):  # noqa: E501
        """Create Disk  # noqa: E501

        Add Disk in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_add_disk(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DisksDiskCreateStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: Disk
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_admin_add_disk_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_admin_add_disk_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def g8_admin_add_disk_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Create Disk  # noqa: E501

        Add Disk in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_add_disk_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DisksDiskCreateStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: Disk
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_admin_add_disk" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `payload` when calling `g8_admin_add_disk`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/disks",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="Disk",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_admin_delete_disk(self, disk_id, **kwargs):  # noqa: E501
        """Delete disk  # noqa: E501

        Delete disk  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_delete_disk(disk_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_admin_delete_disk_with_http_info(disk_id, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_admin_delete_disk_with_http_info(disk_id, **kwargs)  # noqa: E501
            return data

    def g8_admin_delete_disk_with_http_info(self, disk_id, **kwargs):  # noqa: E501
        """Delete disk  # noqa: E501

        Delete disk  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_delete_disk_with_http_info(disk_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["disk_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_admin_delete_disk" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'disk_id' is set
        if self.api_client.client_side_validation and (
            "disk_id" not in params or params["disk_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `disk_id` when calling `g8_admin_delete_disk`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "disk_id" in params:
            path_params["disk_id"] = params["disk_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/disks/{disk_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_admin_disk_add_cache(self, disk_id, payload, **kwargs):  # noqa: E501
        """Configure cache on disk  # noqa: E501

        Configure cache on disk  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_disk_add_cache(disk_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param DisksDiskCacheStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_admin_disk_add_cache_with_http_info(disk_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_admin_disk_add_cache_with_http_info(disk_id, payload, **kwargs)  # noqa: E501
            return data

    def g8_admin_disk_add_cache_with_http_info(self, disk_id, payload, **kwargs):  # noqa: E501
        """Configure cache on disk  # noqa: E501

        Configure cache on disk  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_disk_add_cache_with_http_info(disk_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param DisksDiskCacheStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["disk_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_admin_disk_add_cache" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'disk_id' is set
        if self.api_client.client_side_validation and (
            "disk_id" not in params or params["disk_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `disk_id` when calling `g8_admin_disk_add_cache`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `g8_admin_disk_add_cache`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "disk_id" in params:
            path_params["disk_id"] = params["disk_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/disks/{disk_id}/cache",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_admin_disk_disk_backup_blocksize(self, disk_id, payload, **kwargs):  # noqa: E501
        """Set disk backup block size  # noqa: E501

        Set disk backup block size  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_disk_disk_backup_blocksize(disk_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param DisksDiskBackupBlocksizeStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_admin_disk_disk_backup_blocksize_with_http_info(disk_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_admin_disk_disk_backup_blocksize_with_http_info(disk_id, payload, **kwargs)  # noqa: E501
            return data

    def g8_admin_disk_disk_backup_blocksize_with_http_info(self, disk_id, payload, **kwargs):  # noqa: E501
        """Set disk backup block size  # noqa: E501

        Set disk backup block size  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_disk_disk_backup_blocksize_with_http_info(disk_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param DisksDiskBackupBlocksizeStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["disk_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method g8_admin_disk_disk_backup_blocksize" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'disk_id' is set
        if self.api_client.client_side_validation and (
            "disk_id" not in params or params["disk_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `disk_id` when calling `g8_admin_disk_disk_backup_blocksize`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `g8_admin_disk_disk_backup_blocksize`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "disk_id" in params:
            path_params["disk_id"] = params["disk_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/disks/{disk_id}/backup_block_size",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_admin_disk_disk_backup_ratio(self, disk_id, payload, **kwargs):  # noqa: E501
        """Set disk backup snapshot ratio  # noqa: E501

        Set physical disk backup snapshot ratio  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_disk_disk_backup_ratio(disk_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param DisksDiskBackupRatioStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_admin_disk_disk_backup_ratio_with_http_info(disk_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_admin_disk_disk_backup_ratio_with_http_info(disk_id, payload, **kwargs)  # noqa: E501
            return data

    def g8_admin_disk_disk_backup_ratio_with_http_info(self, disk_id, payload, **kwargs):  # noqa: E501
        """Set disk backup snapshot ratio  # noqa: E501

        Set physical disk backup snapshot ratio  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_disk_disk_backup_ratio_with_http_info(disk_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param DisksDiskBackupRatioStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["disk_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method g8_admin_disk_disk_backup_ratio" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'disk_id' is set
        if self.api_client.client_side_validation and (
            "disk_id" not in params or params["disk_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `disk_id` when calling `g8_admin_disk_disk_backup_ratio`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `g8_admin_disk_disk_backup_ratio`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "disk_id" in params:
            path_params["disk_id"] = params["disk_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/disks/{disk_id}/backup_snapshot_ratio",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_admin_disk_set_srub(self, disk_id, payload, **kwargs):  # noqa: E501
        """Configure cache on disk  # noqa: E501

        Configure scrub on disk  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_disk_set_srub(disk_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param DisksDiskScrubStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_admin_disk_set_srub_with_http_info(disk_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_admin_disk_set_srub_with_http_info(disk_id, payload, **kwargs)  # noqa: E501
            return data

    def g8_admin_disk_set_srub_with_http_info(self, disk_id, payload, **kwargs):  # noqa: E501
        """Configure cache on disk  # noqa: E501

        Configure scrub on disk  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_disk_set_srub_with_http_info(disk_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param DisksDiskScrubStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["disk_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_admin_disk_set_srub" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'disk_id' is set
        if self.api_client.client_side_validation and (
            "disk_id" not in params or params["disk_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `disk_id` when calling `g8_admin_disk_set_srub`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `g8_admin_disk_set_srub`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "disk_id" in params:
            path_params["disk_id"] = params["disk_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/disks/{disk_id}/scrub",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_admin_get_disk(self, disk_id, **kwargs):  # noqa: E501
        """Get disks  # noqa: E501

        Get disk  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_get_disk(disk_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param str x_fields: An optional fields mask
        :return: Disk
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_admin_get_disk_with_http_info(disk_id, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_admin_get_disk_with_http_info(disk_id, **kwargs)  # noqa: E501
            return data

    def g8_admin_get_disk_with_http_info(self, disk_id, **kwargs):  # noqa: E501
        """Get disks  # noqa: E501

        Get disk  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_get_disk_with_http_info(disk_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int disk_id: (required)
        :param str x_fields: An optional fields mask
        :return: Disk
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["disk_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_admin_get_disk" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'disk_id' is set
        if self.api_client.client_side_validation and (
            "disk_id" not in params or params["disk_id"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `disk_id` when calling `g8_admin_get_disk`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "disk_id" in params:
            path_params["disk_id"] = params["disk_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/disks/{disk_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="Disk",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_admin_list_disk(self, **kwargs):  # noqa: E501
        """List disks  # noqa: E501

        List disks in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_list_disk(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param int id: Disk id filter
        :param str name: Disk  name filter
        :param str x_fields: An optional fields mask
        :return: DisksPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_admin_list_disk_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.g8_admin_list_disk_with_http_info(**kwargs)  # noqa: E501
            return data

    def g8_admin_list_disk_with_http_info(self, **kwargs):  # noqa: E501
        """List disks  # noqa: E501

        List disks in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_admin_list_disk_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param int id: Disk id filter
        :param str name: Disk  name filter
        :param str x_fields: An optional fields mask
        :return: DisksPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["limit", "start_after", "sort_by", "sort_direction", "id", "name", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_admin_list_disk" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "id" in params:
            query_params.append(("id", params["id"]))  # noqa: E501
        if "name" in params:
            query_params.append(("name", params["name"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/disks",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="DisksPagination",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
