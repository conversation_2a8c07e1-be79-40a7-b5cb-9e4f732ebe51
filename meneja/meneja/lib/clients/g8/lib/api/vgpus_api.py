# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class VgpusApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def g8_create_vgpu(self, payload, **kwargs):  # noqa: E501
        """Create VGPU  # noqa: E501

        Create VGPU  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_create_vgpu(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param VgpusVgpuCreateStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: VirtualGpu
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_create_vgpu_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_create_vgpu_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def g8_create_vgpu_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Create VGPU  # noqa: E501

        Create VGPU  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_create_vgpu_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param VgpusVgpuCreateStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: VirtualGpu
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_create_vgpu" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `payload` when calling `g8_create_vgpu`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/vgpus",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="VirtualGpu",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_list_gpu_non_admin(self, **kwargs):  # noqa: E501
        """List enabled GPUs for non-admin users  # noqa: E501

        List enabled GPUs for non-admin users  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_list_gpu_non_admin(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: GPUShortStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_list_gpu_non_admin_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.g8_list_gpu_non_admin_with_http_info(**kwargs)  # noqa: E501
            return data

    def g8_list_gpu_non_admin_with_http_info(self, **kwargs):  # noqa: E501
        """List enabled GPUs for non-admin users  # noqa: E501

        List enabled GPUs for non-admin users  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_list_gpu_non_admin_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: GPUShortStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_list_gpu_non_admin" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/vgpus/available",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="GPUShortStructs",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_list_vgpu(self, **kwargs):  # noqa: E501
        """List VGPU  # noqa: E501

        List VGPU  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_list_vgpu(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param list[str] status: search status separated by a comma
        :param int account_id: search vgpus by account id
        :param str guid: search vgpus by guid
        :param str x_fields: An optional fields mask
        :return: VirtualGPUPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_list_vgpu_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.g8_list_vgpu_with_http_info(**kwargs)  # noqa: E501
            return data

    def g8_list_vgpu_with_http_info(self, **kwargs):  # noqa: E501
        """List VGPU  # noqa: E501

        List VGPU  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_list_vgpu_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param list[str] status: search status separated by a comma
        :param int account_id: search vgpus by account id
        :param str guid: search vgpus by guid
        :param str x_fields: An optional fields mask
        :return: VirtualGPUPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = [
            "limit",
            "start_after",
            "sort_by",
            "sort_direction",
            "status",
            "account_id",
            "guid",
            "x_fields",
        ]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_list_vgpu" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "status" in params:
            query_params.append(("status", params["status"]))  # noqa: E501
            collection_formats["status"] = "csv"  # noqa: E501
        if "account_id" in params:
            query_params.append(("account_id", params["account_id"]))  # noqa: E501
        if "guid" in params:
            query_params.append(("guid", params["guid"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/vgpus",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="VirtualGPUPagination",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_remove_vgpu(self, vgpu_guid, payload, **kwargs):  # noqa: E501
        """Remove GPU  # noqa: E501

        Remove VGPU  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_remove_vgpu(vgpu_guid, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str vgpu_guid: (required)
        :param DeleteInputStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_remove_vgpu_with_http_info(vgpu_guid, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_remove_vgpu_with_http_info(vgpu_guid, payload, **kwargs)  # noqa: E501
            return data

    def g8_remove_vgpu_with_http_info(self, vgpu_guid, payload, **kwargs):  # noqa: E501
        """Remove GPU  # noqa: E501

        Remove VGPU  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_remove_vgpu_with_http_info(vgpu_guid, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str vgpu_guid: (required)
        :param DeleteInputStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["vgpu_guid", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_remove_vgpu" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'vgpu_guid' is set
        if self.api_client.client_side_validation and (
            "vgpu_guid" not in params or params["vgpu_guid"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `vgpu_guid` when calling `g8_remove_vgpu`")  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `payload` when calling `g8_remove_vgpu`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "vgpu_guid" in params:
            path_params["vgpu_guid"] = params["vgpu_guid"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/vgpus/{vgpu_guid}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_restore_vgpu(self, vgpu_guid, **kwargs):  # noqa: E501
        """Restore VGPU  # noqa: E501

        Restore VGPU  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_restore_vgpu(vgpu_guid, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str vgpu_guid: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_restore_vgpu_with_http_info(vgpu_guid, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_restore_vgpu_with_http_info(vgpu_guid, **kwargs)  # noqa: E501
            return data

    def g8_restore_vgpu_with_http_info(self, vgpu_guid, **kwargs):  # noqa: E501
        """Restore VGPU  # noqa: E501

        Restore VGPU  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_restore_vgpu_with_http_info(vgpu_guid, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str vgpu_guid: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["vgpu_guid"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_restore_vgpu" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'vgpu_guid' is set
        if self.api_client.client_side_validation and (
            "vgpu_guid" not in params or params["vgpu_guid"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `vgpu_guid` when calling `g8_restore_vgpu`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "vgpu_guid" in params:
            path_params["vgpu_guid"] = params["vgpu_guid"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/vgpus/{vgpu_guid}",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
