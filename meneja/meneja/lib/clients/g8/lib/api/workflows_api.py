# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class WorkflowsApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def get_g8_task_logs(self, workflow_id, task_id, **kwargs):  # noqa: E501
        """Lists the logs of a task  # noqa: E501

        Lists the logs of a task  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_task_logs(workflow_id, task_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str workflow_id: (required)
        :param str task_id: (required)
        :param bool include_debug_logs: Flag to whether to include debug log records.
        :param str x_fields: An optional fields mask
        :return: list[LogLine]
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_task_logs_with_http_info(workflow_id, task_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_task_logs_with_http_info(workflow_id, task_id, **kwargs)  # noqa: E501
            return data

    def get_g8_task_logs_with_http_info(self, workflow_id, task_id, **kwargs):  # noqa: E501
        """Lists the logs of a task  # noqa: E501

        Lists the logs of a task  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_task_logs_with_http_info(workflow_id, task_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str workflow_id: (required)
        :param str task_id: (required)
        :param bool include_debug_logs: Flag to whether to include debug log records.
        :param str x_fields: An optional fields mask
        :return: list[LogLine]
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["workflow_id", "task_id", "include_debug_logs", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_g8_task_logs" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'workflow_id' is set
        if self.api_client.client_side_validation and (
            "workflow_id" not in params or params["workflow_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `workflow_id` when calling `get_g8_task_logs`"
            )  # noqa: E501
        # verify the required parameter 'task_id' is set
        if self.api_client.client_side_validation and (
            "task_id" not in params or params["task_id"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `task_id` when calling `get_g8_task_logs`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "workflow_id" in params:
            path_params["workflow_id"] = params["workflow_id"]  # noqa: E501
        if "task_id" in params:
            path_params["task_id"] = params["task_id"]  # noqa: E501

        query_params = []
        if "include_debug_logs" in params:
            query_params.append(("include_debug_logs", params["include_debug_logs"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/workflows/{workflow_id}/task/{task_id}/logs",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="list[LogLine]",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_workflow(self, workflow_id, **kwargs):  # noqa: E501
        """Get workflow by id  # noqa: E501

        Gets details of a specific workflow  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_workflow(workflow_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str workflow_id: (required)
        :param bool include_logs: Flag to whether to include logs in the result set
        :param bool include_debug_logs: Flag to whether to include debug log records. This parameter is only relevant if include_logs is True
        :param str x_fields: An optional fields mask
        :return: WorkflowsWorkflowStep
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_workflow_with_http_info(workflow_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_workflow_with_http_info(workflow_id, **kwargs)  # noqa: E501
            return data

    def get_g8_workflow_with_http_info(self, workflow_id, **kwargs):  # noqa: E501
        """Get workflow by id  # noqa: E501

        Gets details of a specific workflow  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_workflow_with_http_info(workflow_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str workflow_id: (required)
        :param bool include_logs: Flag to whether to include logs in the result set
        :param bool include_debug_logs: Flag to whether to include debug log records. This parameter is only relevant if include_logs is True
        :param str x_fields: An optional fields mask
        :return: WorkflowsWorkflowStep
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["workflow_id", "include_logs", "include_debug_logs", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_g8_workflow" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'workflow_id' is set
        if self.api_client.client_side_validation and (
            "workflow_id" not in params or params["workflow_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `workflow_id` when calling `get_g8_workflow`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "workflow_id" in params:
            path_params["workflow_id"] = params["workflow_id"]  # noqa: E501

        query_params = []
        if "include_logs" in params:
            query_params.append(("include_logs", params["include_logs"]))  # noqa: E501
        if "include_debug_logs" in params:
            query_params.append(("include_debug_logs", params["include_debug_logs"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/workflows/{workflow_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="WorkflowsWorkflowStep",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_workflow_logs(self, workflow_id, **kwargs):  # noqa: E501
        """List the logs of a workflow  # noqa: E501

        Lists the logs of a workflow  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_workflow_logs(workflow_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str workflow_id: (required)
        :param bool include_debug_logs: Flag to whether to include debug log records.
        :param str x_fields: An optional fields mask
        :return: list[LogLine]
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_workflow_logs_with_http_info(workflow_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_workflow_logs_with_http_info(workflow_id, **kwargs)  # noqa: E501
            return data

    def get_g8_workflow_logs_with_http_info(self, workflow_id, **kwargs):  # noqa: E501
        """List the logs of a workflow  # noqa: E501

        Lists the logs of a workflow  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_workflow_logs_with_http_info(workflow_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str workflow_id: (required)
        :param bool include_debug_logs: Flag to whether to include debug log records.
        :param str x_fields: An optional fields mask
        :return: list[LogLine]
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["workflow_id", "include_debug_logs", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_g8_workflow_logs" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'workflow_id' is set
        if self.api_client.client_side_validation and (
            "workflow_id" not in params or params["workflow_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `workflow_id` when calling `get_g8_workflow_logs`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "workflow_id" in params:
            path_params["workflow_id"] = params["workflow_id"]  # noqa: E501

        query_params = []
        if "include_debug_logs" in params:
            query_params.append(("include_debug_logs", params["include_debug_logs"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/workflows/{workflow_id}/logs",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="list[LogLine]",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_g8_workflows(self, **kwargs):  # noqa: E501
        """List workflows  # noqa: E501

        Lists workflows running in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_workflows(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param str start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str title: Workflows title filter
        :param str status: Workflows status filter
        :param str x_fields: An optional fields mask
        :return: WorkflowsPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_g8_workflows_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_g8_workflows_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_g8_workflows_with_http_info(self, **kwargs):  # noqa: E501
        """List workflows  # noqa: E501

        Lists workflows running in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_workflows_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param str start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str title: Workflows title filter
        :param str status: Workflows status filter
        :param str x_fields: An optional fields mask
        :return: WorkflowsPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["limit", "start_after", "sort_by", "sort_direction", "title", "status", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_g8_workflows" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "title" in params:
            query_params.append(("title", params["title"]))  # noqa: E501
        if "status" in params:
            query_params.append(("status", params["status"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/workflows",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="WorkflowsPagination",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
