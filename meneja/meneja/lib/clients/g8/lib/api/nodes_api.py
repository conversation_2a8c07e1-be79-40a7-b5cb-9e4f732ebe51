# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class NodesApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def add_g8_admin_allowed_os_categories_to_node(self, node_id, payload, **kwargs):  # noqa: E501
        """Add allowed os categories to node  # noqa: E501

        Add allowed-os-categories to node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_allowed_os_categories_to_node(node_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param OSCategories payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_g8_admin_allowed_os_categories_to_node_with_http_info(
                node_id, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.add_g8_admin_allowed_os_categories_to_node_with_http_info(
                node_id, payload, **kwargs
            )  # noqa: E501
            return data

    def add_g8_admin_allowed_os_categories_to_node_with_http_info(self, node_id, payload, **kwargs):  # noqa: E501
        """Add allowed os categories to node  # noqa: E501

        Add allowed-os-categories to node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_allowed_os_categories_to_node_with_http_info(node_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param OSCategories payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method add_g8_admin_allowed_os_categories_to_node" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `add_g8_admin_allowed_os_categories_to_node`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_g8_admin_allowed_os_categories_to_node`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/allowed-os-categories",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def add_g8_admin_disallowed_os_categories_to_node(self, node_id, payload, **kwargs):  # noqa: E501
        """Add accounts to externalnetwork  # noqa: E501

        Add disallowed-os-categories to node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_disallowed_os_categories_to_node(node_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param OSCategories payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_g8_admin_disallowed_os_categories_to_node_with_http_info(
                node_id, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.add_g8_admin_disallowed_os_categories_to_node_with_http_info(
                node_id, payload, **kwargs
            )  # noqa: E501
            return data

    def add_g8_admin_disallowed_os_categories_to_node_with_http_info(self, node_id, payload, **kwargs):  # noqa: E501
        """Add accounts to externalnetwork  # noqa: E501

        Add disallowed-os-categories to node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_disallowed_os_categories_to_node_with_http_info(node_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param OSCategories payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method add_g8_admin_disallowed_os_categories_to_node" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `add_g8_admin_disallowed_os_categories_to_node`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_g8_admin_disallowed_os_categories_to_node`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/disallowed-os-categories",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_nodes_localstorage_add_disk(self, payload, **kwargs):  # noqa: E501
        """Add disk to localstorage (lvm)  # noqa: E501

        Add disk to localstorage  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_nodes_localstorage_add_disk(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param NodesLocalstorageDiskCreate payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_nodes_localstorage_add_disk_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_nodes_localstorage_add_disk_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def g8_nodes_localstorage_add_disk_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Add disk to localstorage (lvm)  # noqa: E501

        Add disk to localstorage  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_nodes_localstorage_add_disk_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param NodesLocalstorageDiskCreate payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method g8_nodes_localstorage_add_disk" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `g8_nodes_localstorage_add_disk`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/localstorage/disks/add",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_node_by_id(self, node_id, **kwargs):  # noqa: E501
        """Get node by id  # noqa: E501

        Get details of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_node_by_id(node_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str x_fields: An optional fields mask
        :return: NodeDetails
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_node_by_id_with_http_info(node_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_node_by_id_with_http_info(node_id, **kwargs)  # noqa: E501
            return data

    def get_g8_node_by_id_with_http_info(self, node_id, **kwargs):  # noqa: E501
        """Get node by id  # noqa: E501

        Get details of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_node_by_id_with_http_info(node_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str x_fields: An optional fields mask
        :return: NodeDetails
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_g8_node_by_id" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `node_id` when calling `get_g8_node_by_id`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="NodeDetails",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_node_power_status(self, **kwargs):  # noqa: E501
        """Return on/of status for nodes and switches  # noqa: E501

        Get g8 node ON/OFF status  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_node_power_status(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: NodesPowerStatusStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_node_power_status_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_node_power_status_with_http_info(**kwargs)  # noqa: E501
            return data

    def get_g8_node_power_status_with_http_info(self, **kwargs):  # noqa: E501
        """Return on/of status for nodes and switches  # noqa: E501

        Get g8 node ON/OFF status  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_node_power_status_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: NodesPowerStatusStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_g8_node_power_status" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/status",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="NodesPowerStatusStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_g8_node_health_checks(self, node_id, **kwargs):  # noqa: E501
        """List node health checks  # noqa: E501

        list health checks of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_node_health_checks(node_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str x_fields: An optional fields mask
        :return: HealthCheckPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_g8_node_health_checks_with_http_info(node_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_g8_node_health_checks_with_http_info(node_id, **kwargs)  # noqa: E501
            return data

    def list_g8_node_health_checks_with_http_info(self, node_id, **kwargs):  # noqa: E501
        """List node health checks  # noqa: E501

        list health checks of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_node_health_checks_with_http_info(node_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str x_fields: An optional fields mask
        :return: HealthCheckPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "limit", "start_after", "sort_by", "sort_direction", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_g8_node_health_checks" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `list_g8_node_health_checks`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/health-checks",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="HealthCheckPagination",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_g8_nodes(self, **kwargs):  # noqa: E501
        """List nodes  # noqa: E501

        Lists nodes running in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_nodes(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str search: search id, name, status or roles
        :param str x_fields: An optional fields mask
        :return: NodesPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_g8_nodes_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_g8_nodes_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_g8_nodes_with_http_info(self, **kwargs):  # noqa: E501
        """List nodes  # noqa: E501

        Lists nodes running in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_nodes_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str search: search id, name, status or roles
        :param str x_fields: An optional fields mask
        :return: NodesPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["limit", "start_after", "sort_by", "sort_direction", "search", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_g8_nodes" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "search" in params:
            query_params.append(("search", params["search"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="NodesPagination",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_g8_nodes_health_summary(self, **kwargs):  # noqa: E501
        """List health check summary for all nodes  # noqa: E501

        List health check summary for all nodes  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_nodes_health_summary(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: NodeHealthCheckStatuss
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_g8_nodes_health_summary_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_g8_nodes_health_summary_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_g8_nodes_health_summary_with_http_info(self, **kwargs):  # noqa: E501
        """List health check summary for all nodes  # noqa: E501

        List health check summary for all nodes  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_nodes_health_summary_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: NodeHealthCheckStatuss
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method list_g8_nodes_health_summary" % key
                )
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/health-checks/summary",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="NodeHealthCheckStatuss",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def mute_g8_node_health_check(self, node_id, health_check_id, message_uid, payload, **kwargs):  # noqa: E501
        """Mute/un-mute a specific health check  # noqa: E501

        Mute/ a health check of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.mute_g8_node_health_check(node_id, health_check_id, message_uid, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str health_check_id: (required)
        :param str message_uid: (required)
        :param NodesMuteHealthCheckStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.mute_g8_node_health_check_with_http_info(
                node_id, health_check_id, message_uid, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.mute_g8_node_health_check_with_http_info(
                node_id, health_check_id, message_uid, payload, **kwargs
            )  # noqa: E501
            return data

    def mute_g8_node_health_check_with_http_info(
        self, node_id, health_check_id, message_uid, payload, **kwargs
    ):  # noqa: E501
        """Mute/un-mute a specific health check  # noqa: E501

        Mute/ a health check of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.mute_g8_node_health_check_with_http_info(node_id, health_check_id, message_uid, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str health_check_id: (required)
        :param str message_uid: (required)
        :param NodesMuteHealthCheckStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "health_check_id", "message_uid", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method mute_g8_node_health_check" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `mute_g8_node_health_check`"
            )  # noqa: E501
        # verify the required parameter 'health_check_id' is set
        if self.api_client.client_side_validation and (
            "health_check_id" not in params or params["health_check_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `health_check_id` when calling `mute_g8_node_health_check`"
            )  # noqa: E501
        # verify the required parameter 'message_uid' is set
        if self.api_client.client_side_validation and (
            "message_uid" not in params or params["message_uid"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `message_uid` when calling `mute_g8_node_health_check`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `mute_g8_node_health_check`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501
        if "health_check_id" in params:
            path_params["health_check_id"] = params["health_check_id"]  # noqa: E501
        if "message_uid" in params:
            path_params["message_uid"] = params["message_uid"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/health-checks/{health_check_id}/messages/{message_uid}/mute",
            "PATCH",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def remove_g8_admin_allowed_os_categories_to_node(self, node_id, category, **kwargs):  # noqa: E501
        """Remove allowed os categories from node  # noqa: E501

        Add allowed-os-categories to node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.remove_g8_admin_allowed_os_categories_to_node(node_id, category, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str category: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.remove_g8_admin_allowed_os_categories_to_node_with_http_info(
                node_id, category, **kwargs
            )  # noqa: E501
        else:
            (data) = self.remove_g8_admin_allowed_os_categories_to_node_with_http_info(
                node_id, category, **kwargs
            )  # noqa: E501
            return data

    def remove_g8_admin_allowed_os_categories_to_node_with_http_info(self, node_id, category, **kwargs):  # noqa: E501
        """Remove allowed os categories from node  # noqa: E501

        Add allowed-os-categories to node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.remove_g8_admin_allowed_os_categories_to_node_with_http_info(node_id, category, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str category: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "category"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method remove_g8_admin_allowed_os_categories_to_node" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `remove_g8_admin_allowed_os_categories_to_node`"
            )  # noqa: E501
        # verify the required parameter 'category' is set
        if self.api_client.client_side_validation and (
            "category" not in params or params["category"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `category` when calling `remove_g8_admin_allowed_os_categories_to_node`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501
        if "category" in params:
            path_params["category"] = params["category"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/allowed-os-categories/{category}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def remove_g8_admin_disallowed_os_categories_to_node(self, node_id, category, **kwargs):  # noqa: E501
        """Remove disallowed os categories from node  # noqa: E501

        Add allowed-os-categories to node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.remove_g8_admin_disallowed_os_categories_to_node(node_id, category, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str category: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.remove_g8_admin_disallowed_os_categories_to_node_with_http_info(
                node_id, category, **kwargs
            )  # noqa: E501
        else:
            (data) = self.remove_g8_admin_disallowed_os_categories_to_node_with_http_info(
                node_id, category, **kwargs
            )  # noqa: E501
            return data

    def remove_g8_admin_disallowed_os_categories_to_node_with_http_info(
        self, node_id, category, **kwargs
    ):  # noqa: E501
        """Remove disallowed os categories from node  # noqa: E501

        Add allowed-os-categories to node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.remove_g8_admin_disallowed_os_categories_to_node_with_http_info(node_id, category, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str category: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "category"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method remove_g8_admin_disallowed_os_categories_to_node" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `remove_g8_admin_disallowed_os_categories_to_node`"
            )  # noqa: E501
        # verify the required parameter 'category' is set
        if self.api_client.client_side_validation and (
            "category" not in params or params["category"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `category` when calling `remove_g8_admin_disallowed_os_categories_to_node`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501
        if "category" in params:
            path_params["category"] = params["category"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/disallowed-os-categories/{category}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def reset_health_check_failure(self, **kwargs):  # noqa: E501
        """Reset health check failure  # noqa: E501

        Reset health check failure  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.reset_health_check_failure(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.reset_health_check_failure_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.reset_health_check_failure_with_http_info(**kwargs)  # noqa: E501
            return data

    def reset_health_check_failure_with_http_info(self, **kwargs):  # noqa: E501
        """Reset health check failure  # noqa: E501

        Reset health check failure  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.reset_health_check_failure_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = []  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method reset_health_check_failure" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/health-checks/reset-backup-failures",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def run_all_g8_node_health_checks(self, node_id, **kwargs):  # noqa: E501
        """Re-run all health checks for a specific node  # noqa: E501

        Re-run health checks of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.run_all_g8_node_health_checks(node_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.run_all_g8_node_health_checks_with_http_info(node_id, **kwargs)  # noqa: E501
        else:
            (data) = self.run_all_g8_node_health_checks_with_http_info(node_id, **kwargs)  # noqa: E501
            return data

    def run_all_g8_node_health_checks_with_http_info(self, node_id, **kwargs):  # noqa: E501
        """Re-run all health checks for a specific node  # noqa: E501

        Re-run health checks of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.run_all_g8_node_health_checks_with_http_info(node_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method run_all_g8_node_health_checks" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `run_all_g8_node_health_checks`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/health-checks",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def run_g8_node_health_check(self, node_id, health_check_id, **kwargs):  # noqa: E501
        """Re-run a health checks for a specific node  # noqa: E501

        Re-run a health check of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.run_g8_node_health_check(node_id, health_check_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str health_check_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.run_g8_node_health_check_with_http_info(node_id, health_check_id, **kwargs)  # noqa: E501
        else:
            (data) = self.run_g8_node_health_check_with_http_info(node_id, health_check_id, **kwargs)  # noqa: E501
            return data

    def run_g8_node_health_check_with_http_info(self, node_id, health_check_id, **kwargs):  # noqa: E501
        """Re-run a health checks for a specific node  # noqa: E501

        Re-run a health check of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.run_g8_node_health_check_with_http_info(node_id, health_check_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str health_check_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "health_check_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method run_g8_node_health_check" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `run_g8_node_health_check`"
            )  # noqa: E501
        # verify the required parameter 'health_check_id' is set
        if self.api_client.client_side_validation and (
            "health_check_id" not in params or params["health_check_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `health_check_id` when calling `run_g8_node_health_check`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501
        if "health_check_id" in params:
            path_params["health_check_id"] = params["health_check_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/health-checks/{health_check_id}",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def unmute_g8_node_health_check(self, node_id, health_check_id, message_uid, **kwargs):  # noqa: E501
        """Un-mute a specific health check  # noqa: E501

        Un-mute a health check of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.unmute_g8_node_health_check(node_id, health_check_id, message_uid, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str health_check_id: (required)
        :param str message_uid: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.unmute_g8_node_health_check_with_http_info(
                node_id, health_check_id, message_uid, **kwargs
            )  # noqa: E501
        else:
            (data) = self.unmute_g8_node_health_check_with_http_info(
                node_id, health_check_id, message_uid, **kwargs
            )  # noqa: E501
            return data

    def unmute_g8_node_health_check_with_http_info(self, node_id, health_check_id, message_uid, **kwargs):  # noqa: E501
        """Un-mute a specific health check  # noqa: E501

        Un-mute a health check of a specific node  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.unmute_g8_node_health_check_with_http_info(node_id, health_check_id, message_uid, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int node_id: (required)
        :param str health_check_id: (required)
        :param str message_uid: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["node_id", "health_check_id", "message_uid"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method unmute_g8_node_health_check" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'node_id' is set
        if self.api_client.client_side_validation and (
            "node_id" not in params or params["node_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `node_id` when calling `unmute_g8_node_health_check`"
            )  # noqa: E501
        # verify the required parameter 'health_check_id' is set
        if self.api_client.client_side_validation and (
            "health_check_id" not in params or params["health_check_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `health_check_id` when calling `unmute_g8_node_health_check`"
            )  # noqa: E501
        # verify the required parameter 'message_uid' is set
        if self.api_client.client_side_validation and (
            "message_uid" not in params or params["message_uid"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `message_uid` when calling `unmute_g8_node_health_check`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "node_id" in params:
            path_params["node_id"] = params["node_id"]  # noqa: E501
        if "health_check_id" in params:
            path_params["health_check_id"] = params["health_check_id"]  # noqa: E501
        if "message_uid" in params:
            path_params["message_uid"] = params["message_uid"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/nodes/{node_id}/health-checks/{health_check_id}/messages/{message_uid}/unmute",
            "PATCH",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
