# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class ObjectspacesApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def add_bucket_life_cycle_rule(self, objectspace_id, bucket_name, payload, **kwargs):  # noqa: E501
        """Add bucket lifecycle rule  # noqa: E501

        Add bucket lifecycle rule  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_bucket_life_cycle_rule(objectspace_id, bucket_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param ObjectspacesBucketLifeCycleRule payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_bucket_life_cycle_rule_with_http_info(
                objectspace_id, bucket_name, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.add_bucket_life_cycle_rule_with_http_info(
                objectspace_id, bucket_name, payload, **kwargs
            )  # noqa: E501
            return data

    def add_bucket_life_cycle_rule_with_http_info(self, objectspace_id, bucket_name, payload, **kwargs):  # noqa: E501
        """Add bucket lifecycle rule  # noqa: E501

        Add bucket lifecycle rule  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_bucket_life_cycle_rule_with_http_info(objectspace_id, bucket_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param ObjectspacesBucketLifeCycleRule payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method add_bucket_life_cycle_rule" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `add_bucket_life_cycle_rule`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `add_bucket_life_cycle_rule`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_bucket_life_cycle_rule`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def cancel_bucket_multipart_uploads(self, objectspace_id, bucket_name, key, upload_id, **kwargs):  # noqa: E501
        """Cancel bucket incompelete multipart uploads  # noqa: E501

        Cancel bucket incompelete multipart uploads  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_bucket_multipart_uploads(objectspace_id, bucket_name, key, upload_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str key: (required)
        :param str upload_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.cancel_bucket_multipart_uploads_with_http_info(
                objectspace_id, bucket_name, key, upload_id, **kwargs
            )  # noqa: E501
        else:
            (data) = self.cancel_bucket_multipart_uploads_with_http_info(
                objectspace_id, bucket_name, key, upload_id, **kwargs
            )  # noqa: E501
            return data

    def cancel_bucket_multipart_uploads_with_http_info(
        self, objectspace_id, bucket_name, key, upload_id, **kwargs
    ):  # noqa: E501
        """Cancel bucket incompelete multipart uploads  # noqa: E501

        Cancel bucket incompelete multipart uploads  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_bucket_multipart_uploads_with_http_info(objectspace_id, bucket_name, key, upload_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str key: (required)
        :param str upload_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "key", "upload_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method cancel_bucket_multipart_uploads" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `cancel_bucket_multipart_uploads`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `cancel_bucket_multipart_uploads`"
            )  # noqa: E501
        # verify the required parameter 'key' is set
        if self.api_client.client_side_validation and ("key" not in params or params["key"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `key` when calling `cancel_bucket_multipart_uploads`"
            )  # noqa: E501
        # verify the required parameter 'upload_id' is set
        if self.api_client.client_side_validation and (
            "upload_id" not in params or params["upload_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `upload_id` when calling `cancel_bucket_multipart_uploads`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501
        if "key" in params:
            path_params["key"] = params["key"]  # noqa: E501
        if "upload_id" in params:
            path_params["upload_id"] = params["upload_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/multipart-uploads/{key}/{upload_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def create_bucket_access(self, objectspace_id, bucket_name, payload, **kwargs):  # noqa: E501
        """Create objectspace bucket access  # noqa: E501

        Create objectspace bucket access  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_bucket_access(objectspace_id, bucket_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param ObjectspacesObjectSpaceAccessRequestStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesObjectSpaceAccessStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.create_bucket_access_with_http_info(
                objectspace_id, bucket_name, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.create_bucket_access_with_http_info(
                objectspace_id, bucket_name, payload, **kwargs
            )  # noqa: E501
            return data

    def create_bucket_access_with_http_info(self, objectspace_id, bucket_name, payload, **kwargs):  # noqa: E501
        """Create objectspace bucket access  # noqa: E501

        Create objectspace bucket access  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_bucket_access_with_http_info(objectspace_id, bucket_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param ObjectspacesObjectSpaceAccessRequestStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesObjectSpaceAccessStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method create_bucket_access" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `create_bucket_access`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `create_bucket_access`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `create_bucket_access`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ObjectspacesObjectSpaceAccessStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_bucket_access(self, objectspace_id, bucket_name, name, **kwargs):  # noqa: E501
        """Delete bucket access  # noqa: E501

        Delete bucket access  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_bucket_access(objectspace_id, bucket_name, name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str name: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_bucket_access_with_http_info(objectspace_id, bucket_name, name, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_bucket_access_with_http_info(objectspace_id, bucket_name, name, **kwargs)  # noqa: E501
            return data

    def delete_bucket_access_with_http_info(self, objectspace_id, bucket_name, name, **kwargs):  # noqa: E501
        """Delete bucket access  # noqa: E501

        Delete bucket access  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_bucket_access_with_http_info(objectspace_id, bucket_name, name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str name: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "name"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method delete_bucket_access" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `delete_bucket_access`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `delete_bucket_access`"
            )  # noqa: E501
        # verify the required parameter 'name' is set
        if self.api_client.client_side_validation and ("name" not in params or params["name"] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `name` when calling `delete_bucket_access`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501
        if "name" in params:
            path_params["name"] = params["name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management/{name}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_bucket_life_cycle_rule(self, objectspace_id, bucket_name, rule_id, **kwargs):  # noqa: E501
        """Delete bucket lifecycle rule  # noqa: E501

        Delete bucket lifecycle rule  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_bucket_life_cycle_rule(objectspace_id, bucket_name, rule_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str rule_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_bucket_life_cycle_rule_with_http_info(
                objectspace_id, bucket_name, rule_id, **kwargs
            )  # noqa: E501
        else:
            (data) = self.delete_bucket_life_cycle_rule_with_http_info(
                objectspace_id, bucket_name, rule_id, **kwargs
            )  # noqa: E501
            return data

    def delete_bucket_life_cycle_rule_with_http_info(
        self, objectspace_id, bucket_name, rule_id, **kwargs
    ):  # noqa: E501
        """Delete bucket lifecycle rule  # noqa: E501

        Delete bucket lifecycle rule  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_bucket_life_cycle_rule_with_http_info(objectspace_id, bucket_name, rule_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str rule_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "rule_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method delete_bucket_life_cycle_rule" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `delete_bucket_life_cycle_rule`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `delete_bucket_life_cycle_rule`"
            )  # noqa: E501
        # verify the required parameter 'rule_id' is set
        if self.api_client.client_side_validation and (
            "rule_id" not in params or params["rule_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `rule_id` when calling `delete_bucket_life_cycle_rule`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501
        if "rule_id" in params:
            path_params["rule_id"] = params["rule_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules/{rule_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_objectspace_by_id(self, objectspace_id, **kwargs):  # noqa: E501
        """Get objectspace by id  # noqa: E501

        Get details of a specific objectspace  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_objectspace_by_id(objectspace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesObjectspaceDetailsStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_objectspace_by_id_with_http_info(objectspace_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_objectspace_by_id_with_http_info(objectspace_id, **kwargs)  # noqa: E501
            return data

    def get_g8_objectspace_by_id_with_http_info(self, objectspace_id, **kwargs):  # noqa: E501
        """Get objectspace by id  # noqa: E501

        Get details of a specific objectspace  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_objectspace_by_id_with_http_info(objectspace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesObjectspaceDetailsStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_g8_objectspace_by_id" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `get_g8_objectspace_by_id`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ObjectspacesObjectspaceDetailsStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_bucket_access(self, objectspace_id, bucket_name, **kwargs):  # noqa: E501
        """List objectspace bucket access  # noqa: E501

        List objectspace bucket access  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_bucket_access(objectspace_id, bucket_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesObjectSpaceAccessListStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_bucket_access_with_http_info(objectspace_id, bucket_name, **kwargs)  # noqa: E501
        else:
            (data) = self.list_bucket_access_with_http_info(objectspace_id, bucket_name, **kwargs)  # noqa: E501
            return data

    def list_bucket_access_with_http_info(self, objectspace_id, bucket_name, **kwargs):  # noqa: E501
        """List objectspace bucket access  # noqa: E501

        List objectspace bucket access  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_bucket_access_with_http_info(objectspace_id, bucket_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesObjectSpaceAccessListStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_bucket_access" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `list_bucket_access`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `list_bucket_access`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ObjectspacesObjectSpaceAccessListStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_bucket_life_cycle_rule(self, objectspace_id, bucket_name, **kwargs):  # noqa: E501
        """List bucket lifecycle rules  # noqa: E501

        List bucket lifecycle rules  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_bucket_life_cycle_rule(objectspace_id, bucket_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesBucketLifeCycleRules
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_bucket_life_cycle_rule_with_http_info(objectspace_id, bucket_name, **kwargs)  # noqa: E501
        else:
            (data) = self.list_bucket_life_cycle_rule_with_http_info(
                objectspace_id, bucket_name, **kwargs
            )  # noqa: E501
            return data

    def list_bucket_life_cycle_rule_with_http_info(self, objectspace_id, bucket_name, **kwargs):  # noqa: E501
        """List bucket lifecycle rules  # noqa: E501

        List bucket lifecycle rules  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_bucket_life_cycle_rule_with_http_info(objectspace_id, bucket_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesBucketLifeCycleRules
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method list_bucket_life_cycle_rule" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `list_bucket_life_cycle_rule`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `list_bucket_life_cycle_rule`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ObjectspacesBucketLifeCycleRules",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_bucket_multipart_uploads(self, objectspace_id, bucket_name, **kwargs):  # noqa: E501
        """List bucket incomplete multipart uploads  # noqa: E501

        List bucket incomplete multipart uploads  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_bucket_multipart_uploads(objectspace_id, bucket_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesBucketMultipartUploads
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_bucket_multipart_uploads_with_http_info(
                objectspace_id, bucket_name, **kwargs
            )  # noqa: E501
        else:
            (data) = self.list_bucket_multipart_uploads_with_http_info(
                objectspace_id, bucket_name, **kwargs
            )  # noqa: E501
            return data

    def list_bucket_multipart_uploads_with_http_info(self, objectspace_id, bucket_name, **kwargs):  # noqa: E501
        """List bucket incomplete multipart uploads  # noqa: E501

        List bucket incomplete multipart uploads  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_bucket_multipart_uploads_with_http_info(objectspace_id, bucket_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str x_fields: An optional fields mask
        :return: ObjectspacesBucketMultipartUploads
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method list_bucket_multipart_uploads" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `list_bucket_multipart_uploads`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `list_bucket_multipart_uploads`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/multipart-uploads",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ObjectspacesBucketMultipartUploads",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_g8_objectspaces(self, **kwargs):  # noqa: E501
        """List Objectspaces  # noqa: E501

        Lists Objectspaces in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_objectspaces(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param list[int] ids: search ids
        :param str name: search status
        :param str status: search status
        :param str account: search by account name or id
        :param str x_fields: An optional fields mask
        :return: ObjectspacePagination
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_g8_objectspaces_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_g8_objectspaces_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_g8_objectspaces_with_http_info(self, **kwargs):  # noqa: E501
        """List Objectspaces  # noqa: E501

        Lists Objectspaces in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_objectspaces_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param list[int] ids: search ids
        :param str name: search status
        :param str status: search status
        :param str account: search by account name or id
        :param str x_fields: An optional fields mask
        :return: ObjectspacePagination
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = [
            "limit",
            "start_after",
            "sort_by",
            "sort_direction",
            "ids",
            "name",
            "status",
            "account",
            "x_fields",
        ]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_g8_objectspaces" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "ids" in params:
            query_params.append(("ids", params["ids"]))  # noqa: E501
            collection_formats["ids"] = "csv"  # noqa: E501
        if "name" in params:
            query_params.append(("name", params["name"]))  # noqa: E501
        if "status" in params:
            query_params.append(("status", params["status"]))  # noqa: E501
        if "account" in params:
            query_params.append(("account", params["account"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ObjectspacePagination",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def update_bucket_access(self, objectspace_id, bucket_name, name, payload, **kwargs):  # noqa: E501
        """Update bucket access type  # noqa: E501

        Update bucket access type  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_bucket_access(objectspace_id, bucket_name, name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str name: (required)
        :param ObjectspacesUpdateBucketAccessTypeStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.update_bucket_access_with_http_info(
                objectspace_id, bucket_name, name, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.update_bucket_access_with_http_info(
                objectspace_id, bucket_name, name, payload, **kwargs
            )  # noqa: E501
            return data

    def update_bucket_access_with_http_info(self, objectspace_id, bucket_name, name, payload, **kwargs):  # noqa: E501
        """Update bucket access type  # noqa: E501

        Update bucket access type  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_bucket_access_with_http_info(objectspace_id, bucket_name, name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int objectspace_id: (required)
        :param str bucket_name: (required)
        :param str name: (required)
        :param ObjectspacesUpdateBucketAccessTypeStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["objectspace_id", "bucket_name", "name", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method update_bucket_access" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'objectspace_id' is set
        if self.api_client.client_side_validation and (
            "objectspace_id" not in params or params["objectspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `objectspace_id` when calling `update_bucket_access`"
            )  # noqa: E501
        # verify the required parameter 'bucket_name' is set
        if self.api_client.client_side_validation and (
            "bucket_name" not in params or params["bucket_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `bucket_name` when calling `update_bucket_access`"
            )  # noqa: E501
        # verify the required parameter 'name' is set
        if self.api_client.client_side_validation and ("name" not in params or params["name"] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `name` when calling `update_bucket_access`")  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `update_bucket_access`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "objectspace_id" in params:
            path_params["objectspace_id"] = params["objectspace_id"]  # noqa: E501
        if "bucket_name" in params:
            path_params["bucket_name"] = params["bucket_name"]  # noqa: E501
        if "name" in params:
            path_params["name"] = params["name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management/{name}",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
