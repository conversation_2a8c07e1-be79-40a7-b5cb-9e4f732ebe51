# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class CloudspacesApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def add_g8_cloudspace_dns_record(self, cloudspace_id, payload, **kwargs):  # noqa: E501
        """add cloudspace dns record  # noqa: E501

        add cloudspace dns record  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_cloudspace_dns_record(cloudspace_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param CloudspacesWireGuardPeerStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: CloudspacesDNSRecordStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_g8_cloudspace_dns_record_with_http_info(cloudspace_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.add_g8_cloudspace_dns_record_with_http_info(cloudspace_id, payload, **kwargs)  # noqa: E501
            return data

    def add_g8_cloudspace_dns_record_with_http_info(self, cloudspace_id, payload, **kwargs):  # noqa: E501
        """add cloudspace dns record  # noqa: E501

        add cloudspace dns record  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_cloudspace_dns_record_with_http_info(cloudspace_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param CloudspacesWireGuardPeerStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: CloudspacesDNSRecordStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method add_g8_cloudspace_dns_record" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `add_g8_cloudspace_dns_record`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_g8_cloudspace_dns_record`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/dns_records",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="CloudspacesDNSRecordStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def add_g8_cloudspace_wireguard_interface_config(self, cloudspace_id, payload, **kwargs):  # noqa: E501
        """Add cloudspace wireguard interface configuration  # noqa: E501

        Add cloudspace wireguard interface config  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_cloudspace_wireguard_interface_config(cloudspace_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param CloudspacesWireGuardConfigWriteStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_g8_cloudspace_wireguard_interface_config_with_http_info(
                cloudspace_id, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.add_g8_cloudspace_wireguard_interface_config_with_http_info(
                cloudspace_id, payload, **kwargs
            )  # noqa: E501
            return data

    def add_g8_cloudspace_wireguard_interface_config_with_http_info(
        self, cloudspace_id, payload, **kwargs
    ):  # noqa: E501
        """Add cloudspace wireguard interface configuration  # noqa: E501

        Add cloudspace wireguard interface config  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_cloudspace_wireguard_interface_config_with_http_info(cloudspace_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param CloudspacesWireGuardConfigWriteStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method add_g8_cloudspace_wireguard_interface_config" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `add_g8_cloudspace_wireguard_interface_config`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_g8_cloudspace_wireguard_interface_config`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/wireguard",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def add_g8_cloudspace_wireguard_peer(self, cloudspace_id, interface_name, payload, **kwargs):  # noqa: E501
        """Add cloudspace wireguard peer  # noqa: E501

        Add cloudspace wireguard peer  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :param CloudspacesWireGuardPeerStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_g8_cloudspace_wireguard_peer_with_http_info(
                cloudspace_id, interface_name, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.add_g8_cloudspace_wireguard_peer_with_http_info(
                cloudspace_id, interface_name, payload, **kwargs
            )  # noqa: E501
            return data

    def add_g8_cloudspace_wireguard_peer_with_http_info(
        self, cloudspace_id, interface_name, payload, **kwargs
    ):  # noqa: E501
        """Add cloudspace wireguard peer  # noqa: E501

        Add cloudspace wireguard peer  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_cloudspace_wireguard_peer_with_http_info(cloudspace_id, interface_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :param CloudspacesWireGuardPeerStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "interface_name", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method add_g8_cloudspace_wireguard_peer" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `add_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501
        # verify the required parameter 'interface_name' is set
        if self.api_client.client_side_validation and (
            "interface_name" not in params or params["interface_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `interface_name` when calling `add_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501
        if "interface_name" in params:
            path_params["interface_name"] = params["interface_name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_g8_cloudspace_dns_records(self, cloudspace_id, guid, **kwargs):  # noqa: E501
        """Delete dns records for cloudspace  # noqa: E501

        Delete DNS record for cloudsspace  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_cloudspace_dns_records(cloudspace_id, guid, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str guid: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_g8_cloudspace_dns_records_with_http_info(cloudspace_id, guid, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_g8_cloudspace_dns_records_with_http_info(cloudspace_id, guid, **kwargs)  # noqa: E501
            return data

    def delete_g8_cloudspace_dns_records_with_http_info(self, cloudspace_id, guid, **kwargs):  # noqa: E501
        """Delete dns records for cloudspace  # noqa: E501

        Delete DNS record for cloudsspace  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_cloudspace_dns_records_with_http_info(cloudspace_id, guid, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str guid: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "guid"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method delete_g8_cloudspace_dns_records" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `delete_g8_cloudspace_dns_records`"
            )  # noqa: E501
        # verify the required parameter 'guid' is set
        if self.api_client.client_side_validation and ("guid" not in params or params["guid"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `guid` when calling `delete_g8_cloudspace_dns_records`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501
        if "guid" in params:
            path_params["guid"] = params["guid"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/dns_records/{guid}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_g8_cloudspace_wireguard_interface(self, cloudspace_id, interface_name, **kwargs):  # noqa: E501
        """Delete cloudspace wireguard interface  # noqa: E501

        Delete cloudspace wireguard interface config  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_cloudspace_wireguard_interface(cloudspace_id, interface_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_g8_cloudspace_wireguard_interface_with_http_info(
                cloudspace_id, interface_name, **kwargs
            )  # noqa: E501
        else:
            (data) = self.delete_g8_cloudspace_wireguard_interface_with_http_info(
                cloudspace_id, interface_name, **kwargs
            )  # noqa: E501
            return data

    def delete_g8_cloudspace_wireguard_interface_with_http_info(
        self, cloudspace_id, interface_name, **kwargs
    ):  # noqa: E501
        """Delete cloudspace wireguard interface  # noqa: E501

        Delete cloudspace wireguard interface config  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_cloudspace_wireguard_interface_with_http_info(cloudspace_id, interface_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "interface_name"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_g8_cloudspace_wireguard_interface" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `delete_g8_cloudspace_wireguard_interface`"
            )  # noqa: E501
        # verify the required parameter 'interface_name' is set
        if self.api_client.client_side_validation and (
            "interface_name" not in params or params["interface_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `interface_name` when calling `delete_g8_cloudspace_wireguard_interface`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501
        if "interface_name" in params:
            path_params["interface_name"] = params["interface_name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/wireguard/{interface_name}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_g8_cloudspace_wireguard_peer(self, cloudspace_id, interface_name, peer_name, **kwargs):  # noqa: E501
        """Delete cloudspace wireguard peer  # noqa: E501

        Delete cloudspace wireguard peer  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, peer_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :param str peer_name: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_g8_cloudspace_wireguard_peer_with_http_info(
                cloudspace_id, interface_name, peer_name, **kwargs
            )  # noqa: E501
        else:
            (data) = self.delete_g8_cloudspace_wireguard_peer_with_http_info(
                cloudspace_id, interface_name, peer_name, **kwargs
            )  # noqa: E501
            return data

    def delete_g8_cloudspace_wireguard_peer_with_http_info(
        self, cloudspace_id, interface_name, peer_name, **kwargs
    ):  # noqa: E501
        """Delete cloudspace wireguard peer  # noqa: E501

        Delete cloudspace wireguard peer  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_cloudspace_wireguard_peer_with_http_info(cloudspace_id, interface_name, peer_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :param str peer_name: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "interface_name", "peer_name"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method delete_g8_cloudspace_wireguard_peer" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `delete_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501
        # verify the required parameter 'interface_name' is set
        if self.api_client.client_side_validation and (
            "interface_name" not in params or params["interface_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `interface_name` when calling `delete_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501
        # verify the required parameter 'peer_name' is set
        if self.api_client.client_side_validation and (
            "peer_name" not in params or params["peer_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `peer_name` when calling `delete_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501
        if "interface_name" in params:
            path_params["interface_name"] = params["interface_name"]  # noqa: E501
        if "peer_name" in params:
            path_params["peer_name"] = params["peer_name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers/{peer_name}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_cloudspace_by_id(self, cloudspace_id, **kwargs):  # noqa: E501
        """Get cloudspace by id  # noqa: E501

        Get cloudspace details by id  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_cloudspace_by_id(cloudspace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str x_fields: An optional fields mask
        :return: CloudspacesCloudspaceDetailsStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_cloudspace_by_id_with_http_info(cloudspace_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_cloudspace_by_id_with_http_info(cloudspace_id, **kwargs)  # noqa: E501
            return data

    def get_g8_cloudspace_by_id_with_http_info(self, cloudspace_id, **kwargs):  # noqa: E501
        """Get cloudspace by id  # noqa: E501

        Get cloudspace details by id  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_cloudspace_by_id_with_http_info(cloudspace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str x_fields: An optional fields mask
        :return: CloudspacesCloudspaceDetailsStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_g8_cloudspace_by_id" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `get_g8_cloudspace_by_id`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="CloudspacesCloudspaceDetailsStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_cloudspace_dns_records(self, cloudspace_id, **kwargs):  # noqa: E501
        """Get dns records for cloudspace  # noqa: E501

        Get DNS records for cloudsspace  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_cloudspace_dns_records(cloudspace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str x_fields: An optional fields mask
        :return: CloudspacesDNSRecordsStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_cloudspace_dns_records_with_http_info(cloudspace_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_cloudspace_dns_records_with_http_info(cloudspace_id, **kwargs)  # noqa: E501
            return data

    def get_g8_cloudspace_dns_records_with_http_info(self, cloudspace_id, **kwargs):  # noqa: E501
        """Get dns records for cloudspace  # noqa: E501

        Get DNS records for cloudsspace  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_cloudspace_dns_records_with_http_info(cloudspace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str x_fields: An optional fields mask
        :return: CloudspacesDNSRecordsStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method get_g8_cloudspace_dns_records" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `get_g8_cloudspace_dns_records`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/dns_records",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="CloudspacesDNSRecordsStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_cloudspace_wireguard_config(self, cloudspace_id, **kwargs):  # noqa: E501
        """Get cloudspace wireguard configuration  # noqa: E501

        Get cloudspace wireguard config  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_cloudspace_wireguard_config(cloudspace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param bool include_status: Include peer status of wireguard
        :param str x_fields: An optional fields mask
        :return: list[CloudspacesWireGuardConfigReadStruct]
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_cloudspace_wireguard_config_with_http_info(cloudspace_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_cloudspace_wireguard_config_with_http_info(cloudspace_id, **kwargs)  # noqa: E501
            return data

    def get_g8_cloudspace_wireguard_config_with_http_info(self, cloudspace_id, **kwargs):  # noqa: E501
        """Get cloudspace wireguard configuration  # noqa: E501

        Get cloudspace wireguard config  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_cloudspace_wireguard_config_with_http_info(cloudspace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param bool include_status: Include peer status of wireguard
        :param str x_fields: An optional fields mask
        :return: list[CloudspacesWireGuardConfigReadStruct]
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "include_status", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method get_g8_cloudspace_wireguard_config" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `get_g8_cloudspace_wireguard_config`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501

        query_params = []
        if "include_status" in params:
            query_params.append(("include_status", params["include_status"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/wireguard",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="list[CloudspacesWireGuardConfigReadStruct]",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_g8_cloudspaces(self, **kwargs):  # noqa: E501
        """List cloudspaces  # noqa: E501

        Lists cloudspaces in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_cloudspaces(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param int id: Cloudspaces id filter
        :param str name: Cloudspaces  name filter
        :param str account: Cloudspaces account(id,name) filter
        :param str networks: Cloudspaces networks(id,subnet) filter
        :param str external_ip: Cloudspaces external_ip(ip,subnet,external_network id,external_network name) filter
        :param str status: Cloudspaces status filter
        :param str x_fields: An optional fields mask
        :return: CloudspacePaginationModel
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_g8_cloudspaces_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_g8_cloudspaces_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_g8_cloudspaces_with_http_info(self, **kwargs):  # noqa: E501
        """List cloudspaces  # noqa: E501

        Lists cloudspaces in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_cloudspaces_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param int id: Cloudspaces id filter
        :param str name: Cloudspaces  name filter
        :param str account: Cloudspaces account(id,name) filter
        :param str networks: Cloudspaces networks(id,subnet) filter
        :param str external_ip: Cloudspaces external_ip(ip,subnet,external_network id,external_network name) filter
        :param str status: Cloudspaces status filter
        :param str x_fields: An optional fields mask
        :return: CloudspacePaginationModel
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = [
            "limit",
            "start_after",
            "sort_by",
            "sort_direction",
            "id",
            "name",
            "account",
            "networks",
            "external_ip",
            "status",
            "x_fields",
        ]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_g8_cloudspaces" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "id" in params:
            query_params.append(("id", params["id"]))  # noqa: E501
        if "name" in params:
            query_params.append(("name", params["name"]))  # noqa: E501
        if "account" in params:
            query_params.append(("account", params["account"]))  # noqa: E501
        if "networks" in params:
            query_params.append(("networks", params["networks"]))  # noqa: E501
        if "external_ip" in params:
            query_params.append(("external_ip", params["external_ip"]))  # noqa: E501
        if "status" in params:
            query_params.append(("status", params["status"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="CloudspacePaginationModel",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def update_g8_cloudspace_dns_record(self, cloudspace_id, guid, payload, **kwargs):  # noqa: E501
        """Update cloudspace dns record  # noqa: E501

        Update cloudspace wireguard peer  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_g8_cloudspace_dns_record(cloudspace_id, guid, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str guid: (required)
        :param CloudspacesWireGuardPeerStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.update_g8_cloudspace_dns_record_with_http_info(
                cloudspace_id, guid, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.update_g8_cloudspace_dns_record_with_http_info(
                cloudspace_id, guid, payload, **kwargs
            )  # noqa: E501
            return data

    def update_g8_cloudspace_dns_record_with_http_info(self, cloudspace_id, guid, payload, **kwargs):  # noqa: E501
        """Update cloudspace dns record  # noqa: E501

        Update cloudspace wireguard peer  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_g8_cloudspace_dns_record_with_http_info(cloudspace_id, guid, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str guid: (required)
        :param CloudspacesWireGuardPeerStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "guid", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method update_g8_cloudspace_dns_record" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `update_g8_cloudspace_dns_record`"
            )  # noqa: E501
        # verify the required parameter 'guid' is set
        if self.api_client.client_side_validation and ("guid" not in params or params["guid"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `guid` when calling `update_g8_cloudspace_dns_record`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `update_g8_cloudspace_dns_record`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501
        if "guid" in params:
            path_params["guid"] = params["guid"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/dns_records/{guid}",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def update_g8_cloudspace_wireguard_interface(self, cloudspace_id, interface_name, payload, **kwargs):  # noqa: E501
        """Update cloudspace wireguard interface configuration  # noqa: E501

        Update cloudspace wireguard interface config  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_g8_cloudspace_wireguard_interface(cloudspace_id, interface_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :param CloudspacesWireGuardConfigWriteStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.update_g8_cloudspace_wireguard_interface_with_http_info(
                cloudspace_id, interface_name, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.update_g8_cloudspace_wireguard_interface_with_http_info(
                cloudspace_id, interface_name, payload, **kwargs
            )  # noqa: E501
            return data

    def update_g8_cloudspace_wireguard_interface_with_http_info(
        self, cloudspace_id, interface_name, payload, **kwargs
    ):  # noqa: E501
        """Update cloudspace wireguard interface configuration  # noqa: E501

        Update cloudspace wireguard interface config  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_g8_cloudspace_wireguard_interface_with_http_info(cloudspace_id, interface_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :param CloudspacesWireGuardConfigWriteStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "interface_name", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_g8_cloudspace_wireguard_interface" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `update_g8_cloudspace_wireguard_interface`"
            )  # noqa: E501
        # verify the required parameter 'interface_name' is set
        if self.api_client.client_side_validation and (
            "interface_name" not in params or params["interface_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `interface_name` when calling `update_g8_cloudspace_wireguard_interface`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `update_g8_cloudspace_wireguard_interface`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501
        if "interface_name" in params:
            path_params["interface_name"] = params["interface_name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/wireguard/{interface_name}",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def update_g8_cloudspace_wireguard_peer(
        self, cloudspace_id, interface_name, peer_name, payload, **kwargs
    ):  # noqa: E501
        """Update cloudspace wireguard peer configuration  # noqa: E501

        Update cloudspace wireguard peer  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_g8_cloudspace_wireguard_peer(cloudspace_id, interface_name, peer_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :param str peer_name: (required)
        :param CloudspacesWireGuardPeerStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.update_g8_cloudspace_wireguard_peer_with_http_info(
                cloudspace_id, interface_name, peer_name, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.update_g8_cloudspace_wireguard_peer_with_http_info(
                cloudspace_id, interface_name, peer_name, payload, **kwargs
            )  # noqa: E501
            return data

    def update_g8_cloudspace_wireguard_peer_with_http_info(
        self, cloudspace_id, interface_name, peer_name, payload, **kwargs
    ):  # noqa: E501
        """Update cloudspace wireguard peer configuration  # noqa: E501

        Update cloudspace wireguard peer  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_g8_cloudspace_wireguard_peer_with_http_info(cloudspace_id, interface_name, peer_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int cloudspace_id: (required)
        :param str interface_name: (required)
        :param str peer_name: (required)
        :param CloudspacesWireGuardPeerStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["cloudspace_id", "interface_name", "peer_name", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method update_g8_cloudspace_wireguard_peer" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'cloudspace_id' is set
        if self.api_client.client_side_validation and (
            "cloudspace_id" not in params or params["cloudspace_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `cloudspace_id` when calling `update_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501
        # verify the required parameter 'interface_name' is set
        if self.api_client.client_side_validation and (
            "interface_name" not in params or params["interface_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `interface_name` when calling `update_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501
        # verify the required parameter 'peer_name' is set
        if self.api_client.client_side_validation and (
            "peer_name" not in params or params["peer_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `peer_name` when calling `update_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `update_g8_cloudspace_wireguard_peer`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "cloudspace_id" in params:
            path_params["cloudspace_id"] = params["cloudspace_id"]  # noqa: E501
        if "interface_name" in params:
            path_params["interface_name"] = params["interface_name"]  # noqa: E501
        if "peer_name" in params:
            path_params["peer_name"] = params["peer_name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/cloudspaces/{cloudspace_id}/wireguard/{interface_name}/peers/{peer_name}",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
