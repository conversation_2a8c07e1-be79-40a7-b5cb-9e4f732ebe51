# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class ExternalnetworksApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def add_g8_admin_external_network(self, payload, **kwargs):  # noqa: E501
        """Create ExternalNetwork  # noqa: E501

        Add ExternalNetwork in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_external_network(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ExternalNetworksExternalNetworkCreateStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: ExternalNetworksExternalNetworkStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_g8_admin_external_network_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.add_g8_admin_external_network_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def add_g8_admin_external_network_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Create ExternalNetwork  # noqa: E501

        Add ExternalNetwork in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_external_network_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ExternalNetworksExternalNetworkCreateStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: ExternalNetworksExternalNetworkStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method add_g8_admin_external_network" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_g8_admin_external_network`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ExternalNetworksExternalNetworkStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def add_g8_admin_external_network_accounts(self, externalnetwork_id, payload, **kwargs):  # noqa: E501
        """Add accounts to externalnetwork  # noqa: E501

        Add accounts to ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_external_network_accounts(externalnetwork_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param AccountIds payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_g8_admin_external_network_accounts_with_http_info(
                externalnetwork_id, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.add_g8_admin_external_network_accounts_with_http_info(
                externalnetwork_id, payload, **kwargs
            )  # noqa: E501
            return data

    def add_g8_admin_external_network_accounts_with_http_info(
        self, externalnetwork_id, payload, **kwargs
    ):  # noqa: E501
        """Add accounts to externalnetwork  # noqa: E501

        Add accounts to ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_external_network_accounts_with_http_info(externalnetwork_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param AccountIds payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["externalnetwork_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method add_g8_admin_external_network_accounts" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'externalnetwork_id' is set
        if self.api_client.client_side_validation and (
            "externalnetwork_id" not in params or params["externalnetwork_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `externalnetwork_id` when calling `add_g8_admin_external_network_accounts`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_g8_admin_external_network_accounts`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "externalnetwork_id" in params:
            path_params["externalnetwork_id"] = params["externalnetwork_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks/{externalnetwork_id}/accounts",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def add_g8_admin_external_network_ips(self, externalnetwork_id, payload, **kwargs):  # noqa: E501
        """Add external network ips  # noqa: E501

        Add specific IPs to ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_external_network_ips(externalnetwork_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param IpShorts payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.add_g8_admin_external_network_ips_with_http_info(
                externalnetwork_id, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.add_g8_admin_external_network_ips_with_http_info(
                externalnetwork_id, payload, **kwargs
            )  # noqa: E501
            return data

    def add_g8_admin_external_network_ips_with_http_info(self, externalnetwork_id, payload, **kwargs):  # noqa: E501
        """Add external network ips  # noqa: E501

        Add specific IPs to ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.add_g8_admin_external_network_ips_with_http_info(externalnetwork_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param IpShorts payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["externalnetwork_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method add_g8_admin_external_network_ips" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'externalnetwork_id' is set
        if self.api_client.client_side_validation and (
            "externalnetwork_id" not in params or params["externalnetwork_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `externalnetwork_id` when calling `add_g8_admin_external_network_ips`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `add_g8_admin_external_network_ips`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "externalnetwork_id" in params:
            path_params["externalnetwork_id"] = params["externalnetwork_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks/{externalnetwork_id}/ips",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_g8_admin_external_network_by_id(self, externalnetwork_id, **kwargs):  # noqa: E501
        """Delete external network by id  # noqa: E501

        Delete a specific ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_admin_external_network_by_id(externalnetwork_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_g8_admin_external_network_by_id_with_http_info(
                externalnetwork_id, **kwargs
            )  # noqa: E501
        else:
            (data) = self.delete_g8_admin_external_network_by_id_with_http_info(
                externalnetwork_id, **kwargs
            )  # noqa: E501
            return data

    def delete_g8_admin_external_network_by_id_with_http_info(self, externalnetwork_id, **kwargs):  # noqa: E501
        """Delete external network by id  # noqa: E501

        Delete a specific ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_admin_external_network_by_id_with_http_info(externalnetwork_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["externalnetwork_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method delete_g8_admin_external_network_by_id" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'externalnetwork_id' is set
        if self.api_client.client_side_validation and (
            "externalnetwork_id" not in params or params["externalnetwork_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `externalnetwork_id` when calling `delete_g8_admin_external_network_by_id`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "externalnetwork_id" in params:
            path_params["externalnetwork_id"] = params["externalnetwork_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks/{externalnetwork_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_admin_external_network_by_id(self, externalnetwork_id, **kwargs):  # noqa: E501
        """Get external network by id  # noqa: E501

        Get details of a specific ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_admin_external_network_by_id(externalnetwork_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param str x_fields: An optional fields mask
        :return: ExternalNetworksExternalNetworkStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_admin_external_network_by_id_with_http_info(externalnetwork_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_admin_external_network_by_id_with_http_info(externalnetwork_id, **kwargs)  # noqa: E501
            return data

    def get_g8_admin_external_network_by_id_with_http_info(self, externalnetwork_id, **kwargs):  # noqa: E501
        """Get external network by id  # noqa: E501

        Get details of a specific ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_admin_external_network_by_id_with_http_info(externalnetwork_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param str x_fields: An optional fields mask
        :return: ExternalNetworksExternalNetworkStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["externalnetwork_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method get_g8_admin_external_network_by_id" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'externalnetwork_id' is set
        if self.api_client.client_side_validation and (
            "externalnetwork_id" not in params or params["externalnetwork_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `externalnetwork_id` when calling `get_g8_admin_external_network_by_id`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "externalnetwork_id" in params:
            path_params["externalnetwork_id"] = params["externalnetwork_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks/{externalnetwork_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ExternalNetworksExternalNetworkStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_admin_external_network_ips(self, externalnetwork_id, **kwargs):  # noqa: E501
        """Get external network ips  # noqa: E501

        Get details of a specific ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_admin_external_network_ips(externalnetwork_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param str x_fields: An optional fields mask
        :return: ExternalNetworkIpStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_admin_external_network_ips_with_http_info(externalnetwork_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_admin_external_network_ips_with_http_info(externalnetwork_id, **kwargs)  # noqa: E501
            return data

    def get_g8_admin_external_network_ips_with_http_info(self, externalnetwork_id, **kwargs):  # noqa: E501
        """Get external network ips  # noqa: E501

        Get details of a specific ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_admin_external_network_ips_with_http_info(externalnetwork_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param str x_fields: An optional fields mask
        :return: ExternalNetworkIpStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["externalnetwork_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method get_g8_admin_external_network_ips" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'externalnetwork_id' is set
        if self.api_client.client_side_validation and (
            "externalnetwork_id" not in params or params["externalnetwork_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `externalnetwork_id` when calling `get_g8_admin_external_network_ips`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "externalnetwork_id" in params:
            path_params["externalnetwork_id"] = params["externalnetwork_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks/{externalnetwork_id}/ips",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ExternalNetworkIpStructs",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_g8_admin_external_networks(self, **kwargs):  # noqa: E501
        """List ExternalNetworks  # noqa: E501

        Lists ExternalNetworks in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_admin_external_networks(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param list[int] account_ids: search for externalnetworks accessible by account_ids (to include public add 0)separated by a comma
        :param str name: search on name
        :param str x_fields: An optional fields mask
        :return: ExternalNetworksPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_g8_admin_external_networks_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_g8_admin_external_networks_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_g8_admin_external_networks_with_http_info(self, **kwargs):  # noqa: E501
        """List ExternalNetworks  # noqa: E501

        Lists ExternalNetworks in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_admin_external_networks_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param list[int] account_ids: search for externalnetworks accessible by account_ids (to include public add 0)separated by a comma
        :param str name: search on name
        :param str x_fields: An optional fields mask
        :return: ExternalNetworksPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = [
            "limit",
            "start_after",
            "sort_by",
            "sort_direction",
            "account_ids",
            "name",
            "x_fields",
        ]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method list_g8_admin_external_networks" % key
                )
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "account_ids" in params:
            query_params.append(("account_ids", params["account_ids"]))  # noqa: E501
            collection_formats["account_ids"] = "csv"  # noqa: E501
        if "name" in params:
            query_params.append(("name", params["name"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ExternalNetworksPagination",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def remove_g8_admin_external_network_accounts(self, externalnetwork_id, account_id, **kwargs):  # noqa: E501
        """Add accounts to externalnetwork  # noqa: E501

        Remove accounts from ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.remove_g8_admin_external_network_accounts(externalnetwork_id, account_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param int account_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.remove_g8_admin_external_network_accounts_with_http_info(
                externalnetwork_id, account_id, **kwargs
            )  # noqa: E501
        else:
            (data) = self.remove_g8_admin_external_network_accounts_with_http_info(
                externalnetwork_id, account_id, **kwargs
            )  # noqa: E501
            return data

    def remove_g8_admin_external_network_accounts_with_http_info(
        self, externalnetwork_id, account_id, **kwargs
    ):  # noqa: E501
        """Add accounts to externalnetwork  # noqa: E501

        Remove accounts from ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.remove_g8_admin_external_network_accounts_with_http_info(externalnetwork_id, account_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param int account_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["externalnetwork_id", "account_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method remove_g8_admin_external_network_accounts" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'externalnetwork_id' is set
        if self.api_client.client_side_validation and (
            "externalnetwork_id" not in params or params["externalnetwork_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `externalnetwork_id` when calling `remove_g8_admin_external_network_accounts`"
            )  # noqa: E501
        # verify the required parameter 'account_id' is set
        if self.api_client.client_side_validation and (
            "account_id" not in params or params["account_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `account_id` when calling `remove_g8_admin_external_network_accounts`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "externalnetwork_id" in params:
            path_params["externalnetwork_id"] = params["externalnetwork_id"]  # noqa: E501
        if "account_id" in params:
            path_params["account_id"] = params["account_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks/{externalnetwork_id}/accounts/{account_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def remove_g8_admin_external_network_ip(self, externalnetwork_id, ipaddress, **kwargs):  # noqa: E501
        """Remove specific IP from ExternalNetwork  # noqa: E501

        Remove specific IP from ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.remove_g8_admin_external_network_ip(externalnetwork_id, ipaddress, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param str ipaddress: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.remove_g8_admin_external_network_ip_with_http_info(
                externalnetwork_id, ipaddress, **kwargs
            )  # noqa: E501
        else:
            (data) = self.remove_g8_admin_external_network_ip_with_http_info(
                externalnetwork_id, ipaddress, **kwargs
            )  # noqa: E501
            return data

    def remove_g8_admin_external_network_ip_with_http_info(self, externalnetwork_id, ipaddress, **kwargs):  # noqa: E501
        """Remove specific IP from ExternalNetwork  # noqa: E501

        Remove specific IP from ExternalNetwork  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.remove_g8_admin_external_network_ip_with_http_info(externalnetwork_id, ipaddress, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param str ipaddress: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["externalnetwork_id", "ipaddress"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method remove_g8_admin_external_network_ip" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'externalnetwork_id' is set
        if self.api_client.client_side_validation and (
            "externalnetwork_id" not in params or params["externalnetwork_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `externalnetwork_id` when calling `remove_g8_admin_external_network_ip`"
            )  # noqa: E501
        # verify the required parameter 'ipaddress' is set
        if self.api_client.client_side_validation and (
            "ipaddress" not in params or params["ipaddress"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `ipaddress` when calling `remove_g8_admin_external_network_ip`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "externalnetwork_id" in params:
            path_params["externalnetwork_id"] = params["externalnetwork_id"]  # noqa: E501
        if "ipaddress" in params:
            path_params["ipaddress"] = params["ipaddress"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks/{externalnetwork_id}/ips/{ipaddress}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def update_g8_admin_external_network(self, externalnetwork_id, payload, **kwargs):  # noqa: E501
        """Update ExternalNetwork  # noqa: E501

        Update ExternalNetwork in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_g8_admin_external_network(externalnetwork_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param ExternalNetworksExternalNetworkUpdateStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: ExternalNetworksExternalNetworkStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.update_g8_admin_external_network_with_http_info(
                externalnetwork_id, payload, **kwargs
            )  # noqa: E501
        else:
            (data) = self.update_g8_admin_external_network_with_http_info(
                externalnetwork_id, payload, **kwargs
            )  # noqa: E501
            return data

    def update_g8_admin_external_network_with_http_info(self, externalnetwork_id, payload, **kwargs):  # noqa: E501
        """Update ExternalNetwork  # noqa: E501

        Update ExternalNetwork in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_g8_admin_external_network_with_http_info(externalnetwork_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int externalnetwork_id: (required)
        :param ExternalNetworksExternalNetworkUpdateStruct payload: (required)
        :param str x_fields: An optional fields mask
        :return: ExternalNetworksExternalNetworkStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["externalnetwork_id", "payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method update_g8_admin_external_network" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'externalnetwork_id' is set
        if self.api_client.client_side_validation and (
            "externalnetwork_id" not in params or params["externalnetwork_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `externalnetwork_id` when calling `update_g8_admin_external_network`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `update_g8_admin_external_network`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "externalnetwork_id" in params:
            path_params["externalnetwork_id"] = params["externalnetwork_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/externalnetworks/{externalnetwork_id}",
            "PATCH",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="ExternalNetworksExternalNetworkStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
