# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class VirtualMachinesApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def assign_backup_policy(self, vm_id, payload, **kwargs):  # noqa: E501
        """Assign backup policy  # noqa: E501

        Assign backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.assign_backup_policy(vm_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param VmachinesVMBackupPolicyStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.assign_backup_policy_with_http_info(vm_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.assign_backup_policy_with_http_info(vm_id, payload, **kwargs)  # noqa: E501
            return data

    def assign_backup_policy_with_http_info(self, vm_id, payload, **kwargs):  # noqa: E501
        """Assign backup policy  # noqa: E501

        Assign backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.assign_backup_policy_with_http_info(vm_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param VmachinesVMBackupPolicyStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["vm_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method assign_backup_policy" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'vm_id' is set
        if self.api_client.client_side_validation and ("vm_id" not in params or params["vm_id"] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `vm_id` when calling `assign_backup_policy`")  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `assign_backup_policy`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "vm_id" in params:
            path_params["vm_id"] = params["vm_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines/{vm_id}/backup_policies",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def create_vm_from_backup(self, payload, **kwargs):  # noqa: E501
        """Create vm from backup  # noqa: E501

        Create VM from backup  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_vm_from_backup(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param VmachinesCreateVMFromBackupStructCREATE payload: (required)
        :param str x_fields: An optional fields mask
        :return: VmachinesCreateVMFromBackupResponseStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.create_vm_from_backup_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.create_vm_from_backup_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def create_vm_from_backup_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Create vm from backup  # noqa: E501

        Create VM from backup  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_vm_from_backup_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param VmachinesCreateVMFromBackupStructCREATE payload: (required)
        :param str x_fields: An optional fields mask
        :return: VmachinesCreateVMFromBackupResponseStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method create_vm_from_backup" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `create_vm_from_backup`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines/create_from_backup",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="VmachinesCreateVMFromBackupResponseStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_vm_attach_vgpu(self, vm_id, payload, **kwargs):  # noqa: E501
        """Attach vgpu  # noqa: E501

        Attach VGPU to the vm  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_vm_attach_vgpu(vm_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param VmachinesVmDeviceAttachStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_vm_attach_vgpu_with_http_info(vm_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_vm_attach_vgpu_with_http_info(vm_id, payload, **kwargs)  # noqa: E501
            return data

    def g8_vm_attach_vgpu_with_http_info(self, vm_id, payload, **kwargs):  # noqa: E501
        """Attach vgpu  # noqa: E501

        Attach VGPU to the vm  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_vm_attach_vgpu_with_http_info(vm_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param VmachinesVmDeviceAttachStruct payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["vm_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_vm_attach_vgpu" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'vm_id' is set
        if self.api_client.client_side_validation and ("vm_id" not in params or params["vm_id"] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `vm_id` when calling `g8_vm_attach_vgpu`")  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `payload` when calling `g8_vm_attach_vgpu`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "vm_id" in params:
            path_params["vm_id"] = params["vm_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines/{vm_id}/vgpus",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_vm_detach_vgpu(self, vm_id, vgpu_guid, **kwargs):  # noqa: E501
        """Detach vgpu  # noqa: E501

        Detach VGPU from the vm  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_vm_detach_vgpu(vm_id, vgpu_guid, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param str vgpu_guid: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_vm_detach_vgpu_with_http_info(vm_id, vgpu_guid, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_vm_detach_vgpu_with_http_info(vm_id, vgpu_guid, **kwargs)  # noqa: E501
            return data

    def g8_vm_detach_vgpu_with_http_info(self, vm_id, vgpu_guid, **kwargs):  # noqa: E501
        """Detach vgpu  # noqa: E501

        Detach VGPU from the vm  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_vm_detach_vgpu_with_http_info(vm_id, vgpu_guid, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param str vgpu_guid: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["vm_id", "vgpu_guid"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_vm_detach_vgpu" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'vm_id' is set
        if self.api_client.client_side_validation and ("vm_id" not in params or params["vm_id"] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `vm_id` when calling `g8_vm_detach_vgpu`")  # noqa: E501
        # verify the required parameter 'vgpu_guid' is set
        if self.api_client.client_side_validation and (
            "vgpu_guid" not in params or params["vgpu_guid"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `vgpu_guid` when calling `g8_vm_detach_vgpu`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "vm_id" in params:
            path_params["vm_id"] = params["vm_id"]  # noqa: E501
        if "vgpu_guid" in params:
            path_params["vgpu_guid"] = params["vgpu_guid"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines/{vm_id}/vgpus/{vgpu_guid}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_admin_vm_by_id(self, vm_id, **kwargs):  # noqa: E501
        """Get vm by id  # noqa: E501

        Get details of a specific Vm  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_admin_vm_by_id(vm_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param str x_fields: An optional fields mask
        :return: VmachinesVmDetailsStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_admin_vm_by_id_with_http_info(vm_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_admin_vm_by_id_with_http_info(vm_id, **kwargs)  # noqa: E501
            return data

    def get_g8_admin_vm_by_id_with_http_info(self, vm_id, **kwargs):  # noqa: E501
        """Get vm by id  # noqa: E501

        Get details of a specific Vm  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_admin_vm_by_id_with_http_info(vm_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param str x_fields: An optional fields mask
        :return: VmachinesVmDetailsStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["vm_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_g8_admin_vm_by_id" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'vm_id' is set
        if self.api_client.client_side_validation and ("vm_id" not in params or params["vm_id"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `vm_id` when calling `get_g8_admin_vm_by_id`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "vm_id" in params:
            path_params["vm_id"] = params["vm_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines/{vm_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="VmachinesVmDetailsStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_virtual_machine_placement(self, **kwargs):  # noqa: E501
        """Get virtual machine placement  # noqa: E501

        Produces a complete machine placement overview  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_virtual_machine_placement(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: VirtualMachinePlacementStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_virtual_machine_placement_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.get_virtual_machine_placement_with_http_info(**kwargs)  # noqa: E501
            return data

    def get_virtual_machine_placement_with_http_info(self, **kwargs):  # noqa: E501
        """Get virtual machine placement  # noqa: E501

        Produces a complete machine placement overview  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_virtual_machine_placement_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: VirtualMachinePlacementStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method get_virtual_machine_placement" % key
                )
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines/placement",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="VirtualMachinePlacementStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_vm_restore_progress(self, vm_id, **kwargs):  # noqa: E501
        """Get vm restore progress  # noqa: E501

        Get VM restore progress info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_vm_restore_progress(vm_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param str x_fields: An optional fields mask
        :return: VmachinesGetVMRestoreProgressStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_vm_restore_progress_with_http_info(vm_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_vm_restore_progress_with_http_info(vm_id, **kwargs)  # noqa: E501
            return data

    def get_vm_restore_progress_with_http_info(self, vm_id, **kwargs):  # noqa: E501
        """Get vm restore progress  # noqa: E501

        Get VM restore progress info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_vm_restore_progress_with_http_info(vm_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param str x_fields: An optional fields mask
        :return: VmachinesGetVMRestoreProgressStruct
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["vm_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_vm_restore_progress" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'vm_id' is set
        if self.api_client.client_side_validation and ("vm_id" not in params or params["vm_id"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `vm_id` when calling `get_vm_restore_progress`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "vm_id" in params:
            path_params["vm_id"] = params["vm_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines/{vm_id}/restore_progress",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="VmachinesGetVMRestoreProgressStruct",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_g8_admin_vmachines(self, **kwargs):  # noqa: E501
        """List Vmachines  # noqa: E501

        Lists Vmachines in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_admin_vmachines(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param int id: search id
        :param str name: search name
        :param str status: search status
        :param int cloudspace_id: search with cloudspace id
        :param str cloudspace_name: search with cloudspace name
        :param int account_id: search with account id
        :param str account_name: search with account name
        :param int node_id: search with node id
        :param str node_name: search with node name
        :param str x_fields: An optional fields mask
        :return: VmachinesPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_g8_admin_vmachines_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_g8_admin_vmachines_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_g8_admin_vmachines_with_http_info(self, **kwargs):  # noqa: E501
        """List Vmachines  # noqa: E501

        Lists Vmachines in the G8  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_g8_admin_vmachines_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param int id: search id
        :param str name: search name
        :param str status: search status
        :param int cloudspace_id: search with cloudspace id
        :param str cloudspace_name: search with cloudspace name
        :param int account_id: search with account id
        :param str account_name: search with account name
        :param int node_id: search with node id
        :param str node_name: search with node name
        :param str x_fields: An optional fields mask
        :return: VmachinesPagination
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = [
            "limit",
            "start_after",
            "sort_by",
            "sort_direction",
            "id",
            "name",
            "status",
            "cloudspace_id",
            "cloudspace_name",
            "account_id",
            "account_name",
            "node_id",
            "node_name",
            "x_fields",
        ]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_g8_admin_vmachines" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "id" in params:
            query_params.append(("id", params["id"]))  # noqa: E501
        if "name" in params:
            query_params.append(("name", params["name"]))  # noqa: E501
        if "status" in params:
            query_params.append(("status", params["status"]))  # noqa: E501
        if "cloudspace_id" in params:
            query_params.append(("cloudspace_id", params["cloudspace_id"]))  # noqa: E501
        if "cloudspace_name" in params:
            query_params.append(("cloudspace_name", params["cloudspace_name"]))  # noqa: E501
        if "account_id" in params:
            query_params.append(("account_id", params["account_id"]))  # noqa: E501
        if "account_name" in params:
            query_params.append(("account_name", params["account_name"]))  # noqa: E501
        if "node_id" in params:
            query_params.append(("node_id", params["node_id"]))  # noqa: E501
        if "node_name" in params:
            query_params.append(("node_name", params["node_name"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="VmachinesPagination",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def unassign_backup_policy(self, vm_id, policy_id, **kwargs):  # noqa: E501
        """Unassign backup policy  # noqa: E501

        Unassign backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.unassign_backup_policy(vm_id, policy_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param int policy_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.unassign_backup_policy_with_http_info(vm_id, policy_id, **kwargs)  # noqa: E501
        else:
            (data) = self.unassign_backup_policy_with_http_info(vm_id, policy_id, **kwargs)  # noqa: E501
            return data

    def unassign_backup_policy_with_http_info(self, vm_id, policy_id, **kwargs):  # noqa: E501
        """Unassign backup policy  # noqa: E501

        Unassign backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.unassign_backup_policy_with_http_info(vm_id, policy_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int vm_id: (required)
        :param int policy_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["vm_id", "policy_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method unassign_backup_policy" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'vm_id' is set
        if self.api_client.client_side_validation and ("vm_id" not in params or params["vm_id"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `vm_id` when calling `unassign_backup_policy`"
            )  # noqa: E501
        # verify the required parameter 'policy_id' is set
        if self.api_client.client_side_validation and (
            "policy_id" not in params or params["policy_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `policy_id` when calling `unassign_backup_policy`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "vm_id" in params:
            path_params["vm_id"] = params["vm_id"]  # noqa: E501
        if "policy_id" in params:
            path_params["policy_id"] = params["policy_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/virtual-machines/{vm_id}/backup_policies/{policy_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
