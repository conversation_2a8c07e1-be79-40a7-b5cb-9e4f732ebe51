# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class GpusApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def g8_disable_gpu(self, gpu_id, **kwargs):  # noqa: E501
        """Disable GPU  # noqa: E501

        Disable GPU by deleting the recored  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_disable_gpu(gpu_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str gpu_id: (required)
        :return: bool
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_disable_gpu_with_http_info(gpu_id, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_disable_gpu_with_http_info(gpu_id, **kwargs)  # noqa: E501
            return data

    def g8_disable_gpu_with_http_info(self, gpu_id, **kwargs):  # noqa: E501
        """Disable GPU  # noqa: E501

        Disable GPU by deleting the recored  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_disable_gpu_with_http_info(gpu_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str gpu_id: (required)
        :return: bool
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["gpu_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_disable_gpu" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'gpu_id' is set
        if self.api_client.client_side_validation and (
            "gpu_id" not in params or params["gpu_id"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `gpu_id` when calling `g8_disable_gpu`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "gpu_id" in params:
            path_params["gpu_id"] = params["gpu_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/gpus/{gpu_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="bool",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_enable_gpu(self, payload, **kwargs):  # noqa: E501
        """Enable GPU  # noqa: E501

        Enable GPU by creating a recored  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_enable_gpu(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param GpuCreate payload: (required)
        :param str x_fields: An optional fields mask
        :return: Gpu
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_enable_gpu_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.g8_enable_gpu_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def g8_enable_gpu_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Enable GPU  # noqa: E501

        Enable GPU by creating a recored  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_enable_gpu_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param GpuCreate payload: (required)
        :param str x_fields: An optional fields mask
        :return: Gpu
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_enable_gpu" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `payload` when calling `g8_enable_gpu`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/gpus",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="Gpu",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def g8_list_gpus(self, **kwargs):  # noqa: E501
        """List all GPUs  # noqa: E501

        List all GPUs  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_list_gpus(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: GPUStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.g8_list_gpus_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.g8_list_gpus_with_http_info(**kwargs)  # noqa: E501
            return data

    def g8_list_gpus_with_http_info(self, **kwargs):  # noqa: E501
        """List all GPUs  # noqa: E501

        List all GPUs  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.g8_list_gpus_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str x_fields: An optional fields mask
        :return: GPUStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method g8_list_gpus" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/gpus",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="GPUStructs",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
