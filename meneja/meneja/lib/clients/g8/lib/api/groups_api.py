# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
G8 API

RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

OpenAPI spec version: v4.0

Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class GroupsApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def delete_g8_group_metadata_key_value(self, group_name, key, **kwargs):  # noqa: E501
        """Delete metadata key  # noqa: E501

        Delete a specific key-value pair in group metadata  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_group_metadata_key_value(group_name, key, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str key: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_g8_group_metadata_key_value_with_http_info(group_name, key, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_g8_group_metadata_key_value_with_http_info(group_name, key, **kwargs)  # noqa: E501
            return data

    def delete_g8_group_metadata_key_value_with_http_info(self, group_name, key, **kwargs):  # noqa: E501
        """Delete metadata key  # noqa: E501

        Delete a specific key-value pair in group metadata  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_g8_group_metadata_key_value_with_http_info(group_name, key, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str key: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["group_name", "key"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method delete_g8_group_metadata_key_value" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'group_name' is set
        if self.api_client.client_side_validation and (
            "group_name" not in params or params["group_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `group_name` when calling `delete_g8_group_metadata_key_value`"
            )  # noqa: E501
        # verify the required parameter 'key' is set
        if self.api_client.client_side_validation and ("key" not in params or params["key"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `key` when calling `delete_g8_group_metadata_key_value`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "group_name" in params:
            path_params["group_name"] = params["group_name"]  # noqa: E501
        if "key" in params:
            path_params["key"] = params["key"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/groups/{group_name}/metadata/{key}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_g8_group_metadata_key_value(self, group_name, key, **kwargs):  # noqa: E501
        """Get metadata value  # noqa: E501

        Get a specific key-value pair from group metadata  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_group_metadata_key_value(group_name, key, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str key: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_g8_group_metadata_key_value_with_http_info(group_name, key, **kwargs)  # noqa: E501
        else:
            (data) = self.get_g8_group_metadata_key_value_with_http_info(group_name, key, **kwargs)  # noqa: E501
            return data

    def get_g8_group_metadata_key_value_with_http_info(self, group_name, key, **kwargs):  # noqa: E501
        """Get metadata value  # noqa: E501

        Get a specific key-value pair from group metadata  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_g8_group_metadata_key_value_with_http_info(group_name, key, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str key: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["group_name", "key"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method get_g8_group_metadata_key_value" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'group_name' is set
        if self.api_client.client_side_validation and (
            "group_name" not in params or params["group_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `group_name` when calling `get_g8_group_metadata_key_value`"
            )  # noqa: E501
        # verify the required parameter 'key' is set
        if self.api_client.client_side_validation and ("key" not in params or params["key"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `key` when calling `get_g8_group_metadata_key_value`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "group_name" in params:
            path_params["group_name"] = params["group_name"]  # noqa: E501
        if "key" in params:
            path_params["key"] = params["key"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/groups/{group_name}/metadata/{key}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_group_metadata_settings(self, group_name, **kwargs):  # noqa: E501
        """Get Settings  # noqa: E501

        Get group metadata settings  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_group_metadata_settings(group_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str x_fields: An optional fields mask
        :return: RuntimeSettings
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_group_metadata_settings_with_http_info(group_name, **kwargs)  # noqa: E501
        else:
            (data) = self.get_group_metadata_settings_with_http_info(group_name, **kwargs)  # noqa: E501
            return data

    def get_group_metadata_settings_with_http_info(self, group_name, **kwargs):  # noqa: E501
        """Get Settings  # noqa: E501

        Get group metadata settings  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_group_metadata_settings_with_http_info(group_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str x_fields: An optional fields mask
        :return: RuntimeSettings
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["group_name", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method get_group_metadata_settings" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'group_name' is set
        if self.api_client.client_side_validation and (
            "group_name" not in params or params["group_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `group_name` when calling `get_group_metadata_settings`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "group_name" in params:
            path_params["group_name"] = params["group_name"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/groups/{group_name}/settings",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="RuntimeSettings",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_list_group_metadata(self, group_name, **kwargs):  # noqa: E501
        """List Groups Metadata  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_list_group_metadata(group_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str x_fields: An optional fields mask
        :return: GroupMetadata
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_list_group_metadata_with_http_info(group_name, **kwargs)  # noqa: E501
        else:
            (data) = self.get_list_group_metadata_with_http_info(group_name, **kwargs)  # noqa: E501
            return data

    def get_list_group_metadata_with_http_info(self, group_name, **kwargs):  # noqa: E501
        """List Groups Metadata  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_list_group_metadata_with_http_info(group_name, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str x_fields: An optional fields mask
        :return: GroupMetadata
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["group_name", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_list_group_metadata" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'group_name' is set
        if self.api_client.client_side_validation and (
            "group_name" not in params or params["group_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `group_name` when calling `get_list_group_metadata`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "group_name" in params:
            path_params["group_name"] = params["group_name"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/groups/{group_name}/metadata",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="GroupMetadata",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def set_g8_group_metadata_key_value(self, group_name, key, payload, **kwargs):  # noqa: E501
        """Set metadata value  # noqa: E501

        Set or update a specific key-value pair in group metadata  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_g8_group_metadata_key_value(group_name, key, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str key: (required)
        :param MetadataKeyValue payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.set_g8_group_metadata_key_value_with_http_info(group_name, key, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.set_g8_group_metadata_key_value_with_http_info(
                group_name, key, payload, **kwargs
            )  # noqa: E501
            return data

    def set_g8_group_metadata_key_value_with_http_info(self, group_name, key, payload, **kwargs):  # noqa: E501
        """Set metadata value  # noqa: E501

        Set or update a specific key-value pair in group metadata  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_g8_group_metadata_key_value_with_http_info(group_name, key, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param str key: (required)
        :param MetadataKeyValue payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["group_name", "key", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method set_g8_group_metadata_key_value" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'group_name' is set
        if self.api_client.client_side_validation and (
            "group_name" not in params or params["group_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `group_name` when calling `set_g8_group_metadata_key_value`"
            )  # noqa: E501
        # verify the required parameter 'key' is set
        if self.api_client.client_side_validation and ("key" not in params or params["key"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `key` when calling `set_g8_group_metadata_key_value`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `set_g8_group_metadata_key_value`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "group_name" in params:
            path_params["group_name"] = params["group_name"]  # noqa: E501
        if "key" in params:
            path_params["key"] = params["key"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/groups/{group_name}/metadata/{key}",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def set_group_metadata_settings(self, group_name, payload, **kwargs):  # noqa: E501
        """Set Settings  # noqa: E501

        Set group metadata settings  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_group_metadata_settings(group_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param RuntimeSettings payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.set_group_metadata_settings_with_http_info(group_name, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.set_group_metadata_settings_with_http_info(group_name, payload, **kwargs)  # noqa: E501
            return data

    def set_group_metadata_settings_with_http_info(self, group_name, payload, **kwargs):  # noqa: E501
        """Set Settings  # noqa: E501

        Set group metadata settings  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_group_metadata_settings_with_http_info(group_name, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str group_name: (required)
        :param RuntimeSettings payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["group_name", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'" " to method set_group_metadata_settings" % key
                )
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'group_name' is set
        if self.api_client.client_side_validation and (
            "group_name" not in params or params["group_name"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `group_name` when calling `set_group_metadata_settings`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `set_group_metadata_settings`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "group_name" in params:
            path_params["group_name"] = params["group_name"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/groups/{group_name}/settings",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
