# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115

from flask.helpers import make_response
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse

from meneja.api import api
from meneja.api.common import BinaryStringField
from meneja.api.meneja import SUCCESS
from meneja.api.vco import alpha_ns, get_vco_id, success_model
from meneja.api.vco import utilities_ns as ns
from meneja.business.translations_reporting import add_suggestion
from meneja.business.vco import utilities
from meneja.business.vdi import get_installer_url
from meneja.lib.enumeration import (
    AvailableLanguages,
    SupportedTerraformPlatforms,
    SupportedVCOCliPlatforms,
    VCODistributedUtilities,
)
from meneja.lib.fixes import FixedArgument
from meneja.model.vco import VCO
from meneja.structs.vco.dataclasses.vdi import VDIWindowsAgentURLStruct

cli_download_parser = reqparse.RequestParser(argument_class=FixedArgument)
cli_download_parser.add_argument(
    "platform",
    help="specific platform to download binaries for",
    type=str,
    location="args",
    required=True,
    choices=SupportedVCOCliPlatforms.values(),
)
cli_download_parser.add_argument(
    "version",
    help="specific version to download (internally used by the cli update mechanism. should not be used otherwise)",
    type=str,
    location="args",
    required=False,
)

terraform_download_parser = reqparse.RequestParser(argument_class=FixedArgument)
terraform_download_parser.add_argument(
    "platform",
    help="specific platform to download binaries for",
    type=str,
    location="args",
    required=True,
    choices=SupportedTerraformPlatforms.values(),
)

cli_version_parser = reqparse.RequestParser(argument_class=FixedArgument)
cli_version_parser.add_argument(
    "platform",
    help="platforms available i.e. windows, linux etc.",
    type=str,
    location="args",
    required=True,
    choices=SupportedVCOCliPlatforms.values(),
)

terraform_provider_version_parser = reqparse.RequestParser(argument_class=FixedArgument)
terraform_provider_version_parser.add_argument(
    "platform",
    help="platforms available i.e. windows, linux etc.",
    type=str,
    location="args",
    required=True,
    choices=SupportedTerraformPlatforms.values(),
)

supported_platforms_model = api.model("SupportedPlatforms", {"result": fields.List(fields.String)})
version_model = api.model("VersionInfo", {"version": fields.String, "sha256": fields.String})
available_versions = api.model("AvailableVersion", {"result": fields.List(fields.String)})

add_suggested_translation_parser = reqparse.RequestParser()
add_suggested_translation_parser.add_argument(
    "language", choices=AvailableLanguages.values(), type=str, location="args", required=True, help="language"
)
add_suggested_translation_parser.add_argument(
    "translation_key", type=str, location="args", required=True, help="translation key in VCO"
)
add_suggested_translation_parser.add_argument(
    "suggested_translation", type=str, location="args", required=True, help="suggested translation"
)
add_suggested_translation_parser.add_argument(
    "from_vco",
    type=bool,
    location="args",
    required=False,
    default=False,
    help="true if the translation suggestion come from VCO portal",
)


@ns.route("/cli/platforms")
class VCOClPlatforms(Resource):
    @authenticated
    @ns.doc(shortcut="listCliPlatforms", description="list cli platforms")
    @ns.marshal_with(supported_platforms_model)
    def get(self):
        return {"result": SupportedVCOCliPlatforms.values()}


@ns.route("/cli")
class VCOCli(Resource):
    @ns.doc(shortcut="getCli", description="Get cli", model=BinaryStringField())
    @ns.expect(cli_download_parser)
    @ns.produces(["application/zip"])
    def get(self):
        vco = VCO.get_by_id(get_vco_id(), only=["domain", "utility_name"])
        args = cli_download_parser.parse_args()
        binary = utilities.get_utility_binary(vco, utility=VCODistributedUtilities.CLI, **args)
        return make_response(
            binary,
            200,
            {"Content-Type": "application/zip", "Content-Disposition": f"filename={vco.utility_name}.zip"},
        )


@ns.route("/cli/version")
class VCOCliVersion(Resource):
    @authenticated
    @ns.doc(shortcut="getLatestCliVersionInfo", description="get latest cli version info")
    @ns.expect(cli_version_parser)
    @ns.marshal_with(version_model)
    def get(self):
        vco = VCO.get_by_id(get_vco_id(), only=["domain", "utility_name"])
        args = cli_version_parser.parse_args()
        return utilities.get_utility_latest_version(vco, utility=VCODistributedUtilities.CLI, **args)


@ns.route("/terrafrom-provider/platforms")
class TerraformPlatforms(Resource):
    @authenticated
    @ns.doc(shortcut="listTerraformProviderPlatforms", description="list terraform provider platforms")
    @ns.marshal_with(supported_platforms_model)
    def get(self):
        return {"result": SupportedTerraformPlatforms.values()}


@ns.route("/terraform-provider")
class VCOTerraformProvider(Resource):
    @ns.doc(shortcut="getTerrafomProvider", description="Get terraform provider", model=BinaryStringField())
    @ns.expect(terraform_download_parser)
    @ns.produces(["application/zip"])
    def get(self):
        vco = VCO.get_by_id(get_vco_id(), only=["domain", "utility_name"])
        args = terraform_download_parser.parse_args()
        binary = utilities.get_utility_binary(vco, utility=VCODistributedUtilities.TERRAFORM_PROVIDER, **args)
        return make_response(
            binary,
            200,
            {
                "Content-Type": "application/zip",
                "Content-Disposition": f"filename=terraform-provider-{vco.utility_name}.zip",
            },
        )


@ns.route("/terraform-provider/version")
class VCOTerraformProviderVersion(Resource):
    @authenticated
    @ns.doc(shortcut="downloadTerraformProviderVersion", description="download Terraform provider version")
    @ns.expect(terraform_provider_version_parser)
    @ns.marshal_with(version_model)
    def get(self):
        vco = VCO.get_by_id(get_vco_id(), only=["domain", "utility_name"])
        args = terraform_provider_version_parser.parse_args()
        return utilities.get_utility_latest_version(vco, utility=VCODistributedUtilities.TERRAFORM_PROVIDER, **args)


@ns.route("/csi-driver/examples")
class CSIDriverExamples(Resource):
    @authenticated
    @ns.doc(shortcut="downloadCSIDriverExamples", description="download examples for using the CSI driver")
    @ns.produces(["application/zip"])
    def get(self):
        vco = VCO.get_by_id(get_vco_id(), only=["domain"])
        binary = utilities.get_newest_object_by_prefix(
            vco, utility=VCODistributedUtilities.CSI_DRIVER_EXAMPLES, prefix="examples."
        )
        return make_response(
            binary, 200, {"Content-Type": "application/zip", "Content-Disposition": "filename=csi-driver-examples.zip"}
        )


@ns.route("/examples/kubernetes-files")
class KubernetesTutorialExamples(Resource):
    @authenticated
    @ns.doc(
        shortcut="downloadKubernetesConfigurationFilesExample",
        description="download pre-filled examples for kubernetes configuration files",
    )
    @ns.produces(["application/zip"])
    def get(self):
        vco = VCO.get_by_id(get_vco_id(), only=["domain", "utility_name"])
        file_bytes = utilities.get_kubernetes_examples_zip(vco)
        return make_response(
            file_bytes,
            200,
            {"Content-Type": "application/zip", "Content-Disposition": "filename=kubernetes-example.zip"},
        )


@ns.route("/suggest_translation", doc=False)
class TranslationSuggestions(Resource):
    @authenticated
    @ns.expect(add_suggested_translation_parser)
    @ns.marshal_with(success_model)
    @ns.doc(
        shortcut="addSuggestedTranslation",
        description="Add new suggested translation",
    )
    def post(self):
        current_user = get_current_user_info()
        args = add_suggested_translation_parser.parse_args()
        add_suggestion(
            language=args.get("language"),
            translation_key=args.get("translation_key"),
            suggested_translation=args.get("suggested_translation"),
            vco_id=get_vco_id(),
            account_id=current_user.username,
            email=current_user.email,
            from_vco=args.get("from_vco"),
        )
        return SUCCESS


@alpha_ns.route("/vdi/agent-link/<string:os>")
class VDIAgentInstaller(Resource):
    @authenticated
    @ns.marshal_with(VDIWindowsAgentURLStruct.model(api))
    @ns.doc(shortcut="getAgentInstaller", description="Download VDI agent installer for windows")
    def get(self, os: str):
        return {"url": get_installer_url(user_os=os)}
