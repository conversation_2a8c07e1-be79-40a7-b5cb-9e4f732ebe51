# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115, W0613

import dataclasses

from flask.globals import request
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean
from gig.g8.osimages import OS_LIST

import meneja.business.g8.cloudspace as G8_cloudspace
from meneja.api import api
from meneja.api.common import resource_notes_model
from meneja.api.meneja import audits_list_argparser, audits_pagination_model
from meneja.api.vco import SUCCESS, alpha_ns, changed_model
from meneja.api.vco import customer_ns as ns
from meneja.api.vco import get_vco_domain, get_vco_id, process_cloudspace_id, success_model
from meneja.api.vco.cloudspaces import delete_domain_parser, resources_domain_parser
from meneja.api.vco.management import list_backups_parser
from meneja.business import backups as backup_business
from meneja.business.auth import has_access_to_customer, is_customer_org_admin, requires_custom
from meneja.business.g8.g8_api import G8Client
from meneja.business.vco.customer import cloudspace as cs_business
from meneja.business.vco.customer import vms as vm_business
from meneja.business.vco.customer.roles import list_roles_on_vm
from meneja.business.vco.iaas.virtual_machine import delete_virtual_machine, disable_vm_agent, resize_vm, restore_vm
from meneja.jobs.g8_compliance_report import scan_virtual_machine
from meneja.lib.enumeration import (
    BootType,
    CloudResourceType,
    EnvironmentName,
    NicModels,
    OSType,
    SupportedDNSRecordType,
    VMStatistics,
)
from meneja.lib.fixes import FixedArgument
from meneja.lib.meneja_g8_mapping import gen_arg_parser, generate_api_request_model, generate_api_response_model
from meneja.lib.pagination import get_pagination_model, pagination_handler, pagination_handler_for_audits
from meneja.model.audit import AuditLog
from meneja.model.dns_records import DnsRecord
from meneja.structs.vco.customer.customer_role import CustomerRoleStruct
from meneja.structs.vco.dataclasses.cpu_topology import CPUTopologyStruct
from meneja.structs.vco.dataclasses.notes import NoteInputsStruct
from meneja.structs.vco.dataclasses.resources_dns_record import ResourceDnsRecordStruct
from meneja.structs.vco.dataclasses.vco import (
    BackupStruct,
    BackupWithVMStruct,
    CreateVmFromBackupStruct,
    RestoreVmResponse,
    RestoreVMStruct,
)
from meneja.structs.vco.dataclasses.vm import ExternalNicStruct, VMWriteFileStruct

REQUESTS_ROUTE = "/<customer_id>/cloudspaces/<cloudspace_id>/vms"

create_vm_parser = gen_arg_parser("cloudapi/machines/create")
create_vm_parser.replace_argument("image_id", type=int, default=None, location="args", help="Id of the specific image")
create_vm_parser.replace_argument("disk_size", type=int, default=None, location="args", help="Boot Disk Size in GiB")
create_vm_parser.add_argument("cdrom_id", type=int, default=None, location="args", help="CD-ROM Image ID")
create_vm_parser.add_argument(
    "boot_disk_id",
    type=int,
    default=None,
    location="args",
    help="BOOT Disk ID for creating with snapshot or with existing disk",
)
create_vm_parser.add_argument(
    "os_type", type=str, default=None, location="args", help="OS type used on machine", choices=list(OSType.values())
)
create_vm_parser.add_argument("os_name", type=str, default=None, location="args", help="Image OS name", choices=OS_LIST)
create_vm_parser.add_argument(
    "enable_vm_agent", type=boolean, default=True, location="args", help="whether or not to enable agent communication"
)
create_vm_parser.add_argument("snapshot_id", type=str, default=None, location="args", help="Boot Disk Snapshot ID")
create_vm_parser.add_argument(
    "all_vm_disks",
    type=boolean,
    required=False,
    default=False,
    location="args",
    help="Create clones of all snapshots that were created in the same snapshot",
)
create_vm_parser.add_argument(
    "acronis", type=boolean, required=False, default=False, location="args", help="Create VM from Acronis backup"
)
create_vm_parser.add_argument(
    "veeam", type=boolean, required=False, default=False, location="args", help="Create VM from Veeam backup"
)
create_vm_parser.add_argument(
    "start_vm",
    type=boolean,
    required=False,
    default=True,
    location="args",
    help="whether to start vm right after creation",
)

create_vm_parser.add_argument(
    "boot_type",
    type=str,
    required=False,
    default=BootType.BIOS.value,
    location="args",
    help="Boot type to be used when creating from snapshot or creating empty machine",
    choices=list(BootType.values()),
)

create_vm_parser.add_argument(
    "tpm_secret",
    type=str,
    required=False,
    default=None,
    location="args",
    help="TPM secret",
)

create_vm_extensions_parser = api.model(
    "VMCreateExtentions",
    {
        "userdata": fields.Raw(default={}, help_text="Cloudinit userdata passed in json format"),
        "UserDataParameters": fields.Raw(default={}, help_text="User data parameters  as key value pairs."),
        "gpu_id": fields.String(default="", help_text="GPU profile ID to be attached to the VM", required=False),
        "vgpu_name": fields.String(default="", help_text="VGPU name required if attaching a GPU", required=False),
    },
)

create_vm_model = generate_api_response_model("cloudapi/machines/create", api)

exec_vm_model = api.model(
    "VMExecuteModel",
    {
        "command": fields.String(description="Command to execute", required=True),
        "args": fields.List(fields.String, description="Additional arguments to the command", required=False),
    },
)
exec_vm_response_model = generate_api_response_model("cloudapi/machines/execCommand", api)

change_vm_agent = api.model("changeAgentStatus", {"enabled": fields.Boolean(description="Enable/Disable Agent")})
get_vm_agent = api.model("getAgentStatus", {"status": fields.String(description="agent status")})
write_vm_file_model = VMWriteFileStruct.model(api)
read_vm_file_parser = gen_arg_parser("cloudapi/machines/readFile")
delete_vm_file_parser = gen_arg_parser("cloudapi/machines/deleteFile")
read_vm_file_response = generate_api_response_model("cloudapi/machines/readFile", api)
import_s3_vm_parser = gen_arg_parser("cloudapi/machines/importS3")
import_s3_vm_parser.add_argument(
    "boot_type",
    type=str,
    default=BootType.BIOS.value,
    location="args",
    help="Boot type",
    choices=list(BootType.values()),
)
import_s3_vm_parser.add_argument(
    "os_type",
    type=str,
    default=OSType.LINUX.value,
    location="args",
    help="Image OS type",
    choices=list(OSType.values()),
)
import_s3_vm_parser.add_argument(
    "os_name", type=str, default="Linux - other", required=True, location="args", help="Image OS name", choices=OS_LIST
)
import_s3_vm_parser_with_payload = generate_api_request_model(
    "cloudapi/machines/importS3", api, exclude=["cloudspace_id"]
)
import_s3_vm_model = generate_api_response_model("cloudapi/machines/importS3", api)
export_s3_vm_parser = gen_arg_parser("cloudapi/machines/exportS3")
export_s3_vm_parser_with_payload = generate_api_request_model("cloudapi/machines/exportS3", api, exclude=["vm_id"])
delete_vm_parser = gen_arg_parser("cloudapi/machines/delete")
delete_vm_parser.add_argument(
    "detach_disks",
    type=boolean,
    default=False,
    required=False,
    location="args",
    help="(Optional) Detach disks before deleting",
)
delete_vm_parser.add_argument(
    "detach_gpu",
    type=bool,
    default=False,
    required=False,
    location="args",
    help="(Optional) Detach GPU before deleting",
)
vm_stats_parser = gen_arg_parser("cloudapi/machines/getStats")
vm_stats_model = generate_api_response_model("cloudapi/machines/getStats", api)
rename_vm_parser = gen_arg_parser("cloudapi/machines/update$rename")
redescribe_vm_parser = gen_arg_parser("cloudapi/machines/update$description")
resize_vm_parser = gen_arg_parser("cloudapi/machines/resize")
restore_vm_parser = gen_arg_parser("cloudapi/machines/restore")
start_vm_parser = gen_arg_parser("cloudapi/machines/start")
start_vm_parser.add_argument("tpm_secret", type=str, required=False, default=None, location="args", help="TPM secret")
stop_vm_parser = gen_arg_parser("cloudapi/machines/stop")
move_vm_parser = gen_arg_parser("cloudapi/machines/moveToCloudspace")
move_vm_parser.replace_argument(
    "target_cloudspace_id", type=str, required=True, location="args", help="ID of target Cloudspace"
)
attach_e_network_vm_parser = gen_arg_parser("cloudapi/machines/attachExternalNetwork")
attach_e_network_vm_parser.add_argument(
    "external_cloudspace_id", type=str, required=False, default=None, location="args", help="External cloudspace id"
)
attach_disk_vm_parser = gen_arg_parser("cloudapi/machines/attachDisk")
attach_disk_vm_parser.remove_argument("backup_snapshot_ratio")
attach_cdrom_vm_parser = gen_arg_parser("cloudapi/machines/attachDisk")
attach_cdrom_vm_parser.remove_argument("disk_id")
attach_cdrom_vm_parser.add_argument("cdrom_id", type=int, required=True, location="args", help="ID of the CD-ROM image")
detach_disk_vm_parser = gen_arg_parser("cloudapi/machines/detachDisk")
detach_e_network_vm_parser = gen_arg_parser("cloudapi/machines/detachExternalNetwork")
detach_e_network_vm_parser.add_argument(
    "external_cloudspace_id", default=None, required=False, location="args", help="External cloudspace id"
)
vm_model = generate_api_response_model("cloudapi/machines/get$vm_info", api)
vm_consumption_parser = gen_arg_parser("cloudapi/billing/calculate$vm")
vm_consumption_model = generate_api_response_model("cloudapi/billing/calculate$vm", api)
vm_consumption_ts_parser = gen_arg_parser("cloudapi/billing/calculateTimeseries$vm")
vm_create_template_parser = gen_arg_parser("cloudapi/machines/createTemplate")
vm_consumption_ts_model = generate_api_response_model("cloudapi/billing/calculateTimeseries$vm", api)
external_nics_model = ExternalNicStruct.list_model(api)
external_nics_models_list_model = api.model(
    "ExternalNICModels", {"result": fields.List(fields.String, description="Models of external networks")}
)

create_backup_parser = reqparse.RequestParser(argument_class=FixedArgument)
create_backup_parser.add_argument("policy", type=int, location="args", help="Policy id", required=True)

external_nic_model = ExternalNicStruct.model(api)  # pylint: disable=not-callable
vm_disks_model = api.model(
    "VirtualMachineDisks", {"result": fields.List(fields.Nested(vm_model["disks"].container.model))}
)
vms_model = api.model(
    "VirtualMachines",
    {"result": fields.List(fields.Nested(generate_api_response_model("cloudapi/machines/list", api)))},
)
vms_parser = gen_arg_parser("cloudapi/machines/list")
vm_cdrom_model = api.model(
    "CDROMModel",
    {
        "cdrom_id": fields.Integer(description="CDROM ID"),
        "status": fields.String(description="Status"),
        "name": fields.String(description="CDROM Name"),
        "description": fields.String(description="Description"),
        "disk_size": fields.String(description="Disk Size in GB"),
    },
)
vm_cdrom_list_model = api.model("VirtualMachineCDS", {"result": fields.List(fields.Nested(vm_cdrom_model))})
update_nic_model_parser = gen_arg_parser("cloudapi/machines/updateNicModel")
update_MAC_address_parser = gen_arg_parser("cloudapi/machines/updateMacAddress")
update_IP_address_parser = gen_arg_parser("cloudapi/machines/updateIp")
update_IP_address_parser.add_argument("old_ip_address", type=str, location="args", help="old nic ip address")
update_IP_address_parser.add_argument(
    "is_external", type=boolean, location="args", required=False, default=False, help="is external network"
)
note_input = NoteInputsStruct.model(api)

pin_vm_cpus_parser = gen_arg_parser("cloudapi/machines/pinCPUS")
unpin_vm_cpus_parser = gen_arg_parser("cloudapi/machines/unPinCPUS")
vm_boot_type_parser = gen_arg_parser("cloudapi/machines/setBootType")
boot_disk_parser = reqparse.RequestParser(argument_class=FixedArgument)
boot_disk_parser.add_argument("disk_id", type=int, location="args", help="Disk id")
is_dev_env = EnvironmentName.current() in (EnvironmentName.DEV, EnvironmentName.QAS)
scan_vm_parser = reqparse.RequestParser(argument_class=FixedArgument)
scan_vm_parser.add_argument("force", type=bool, location="args", help="Force scan VM")

list_backups_parser = list_backups_parser.copy()
list_backups_parser.remove_argument("vm_id")
list_backups_parser.remove_argument("location")


@ns.route(REQUESTS_ROUTE)
class CSVirtualMachines(Resource):
    @authenticated
    @ns.expect(vms_parser)
    @ns.doc(shortcut="listCloudspaceVirtualMachines", description="List VMs on this cloudspace")
    @ns.marshal_with(vms_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id):
        jwt = get_current_user_info().jwt
        kwargs = vms_parser.parse_args()
        return {"result": vm_business.list_cloudspace_vms(location, jwt, cloudspace_id, **kwargs)}

    @authenticated
    @ns.doc(shortcut="createVirtualMachine", description="Create VirtualMachine")
    @ns.marshal_with(generate_api_response_model("cloudapi/machines/create", api))
    @ns.expect(
        create_vm_parser,
        create_vm_extensions_parser,
    )
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id):
        uri_args = create_vm_parser.parse_args()
        if uri_args["os_type"] == OSType.WINDOWS.value and not uri_args["enable_vm_agent"]:
            raise ValueError("Cannot create windows virtual machine without enabling vm agent")
        payload_agrs = ns.payload or {}
        for arg in payload_agrs:
            if arg in uri_args:
                raise ValueError(f"{arg} is a url param and unexpected in the payload")
        jwt = get_current_user_info().jwt
        return cs_business.create_vm(
            customer_id,
            location,
            jwt,
            cloudspace_id,
            account_id=account_id,
            vco_id=get_vco_id(),
            domain=get_vco_domain(),
            **uri_args,
            **payload_agrs,
        )


@ns.route(REQUESTS_ROUTE + "/import-s3")
class VirtualMachineFromS3(Resource):
    @authenticated
    @ns.doc(shortcut="createVirtualMachineFromS3", description="import and create virtual machine from s3")
    @ns.expect(import_s3_vm_parser)
    @ns.marshal_with(import_s3_vm_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id):
        jwt = get_current_user_info().jwt
        kwargs = import_s3_vm_parser.parse_args()
        return cs_business.import_vm_from_s3(customer_id, jwt, location, cloudspace_id, **kwargs)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/export-s3")
class VirtualMachineExportS3(Resource):
    @authenticated
    @ns.doc(shortcut="exportVirtualMachineToS3", description="Export virtual machine to s3")
    @ns.expect(export_s3_vm_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        kwargs = export_s3_vm_parser.parse_args()
        return G8Client(location, jwt=jwt).export_vm_s3(vm_id, **kwargs)


@ns.route(REQUESTS_ROUTE + "/import-vm-s3")
class VirtualMachineImportFromS3(Resource):
    @authenticated
    @ns.doc(
        shortcut="createVirtualMachineFromS3WithPayload",
        description="import and create virtual machine from s3 with payload",
    )
    @ns.expect(import_s3_vm_parser_with_payload)
    @ns.marshal_with(import_s3_vm_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id):
        jwt = get_current_user_info().jwt
        kwargs = ns.payload
        return cs_business.import_vm_from_s3(customer_id, jwt, location, cloudspace_id, **kwargs)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/export-vm-s3")
class VirtualMachineExportToS3(Resource):
    @authenticated
    @ns.doc(shortcut="exportVirtualMachineToS3WithPayload", description="Export virtual machine to s3 with payload")
    @ns.expect(export_s3_vm_parser_with_payload)
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).export_vm_s3(vm_id, **ns.payload)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/exec")
class VirtualMachineCommand(Resource):
    @authenticated
    @ns.doc(shortcut="executeCommand", description="Execute command inside virtual machine")
    @ns.expect(exec_vm_model)
    @ns.marshal_with(exec_vm_response_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return vm_business.validate_and_exec_command(location, jwt, vm_id, **ns.payload)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/file")
class VirtualMachineFile(Resource):
    @authenticated
    @ns.doc(shortcut="writeFile", description="Write to file inside virtual machine")
    @ns.expect(write_vm_file_model)
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return vm_business.validate_and_write_vm_file(location, jwt, vm_id, **ns.payload)

    @authenticated
    @ns.doc(shortcut="readFile", description="Read file from virtual machine")
    @ns.expect(read_vm_file_parser)
    @ns.marshal_with(read_vm_file_response)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        args = read_vm_file_parser.parse_args()
        return G8Client(location, jwt=jwt).read_vm_file(vm_id, **args)

    @authenticated
    @ns.doc(shortcut="deleteFile", description="Delete file from virtual machine")
    @ns.expect(delete_vm_file_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def delete(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        args = delete_vm_file_parser.parse_args()
        return G8Client(location, jwt=jwt).delete_vm_file(vm_id, filepath=args["filepath"])


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>")
class VirtualMachine(Resource):
    @authenticated
    @ns.doc(shortcut="getVirtualMachineInfo", description="Get virtual machine info")
    @ns.marshal_with(vm_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return vm_business.get_vm_info(location, jwt, vm_id)

    @authenticated
    @ns.doc(shortcut="deleteVirtualMachine", description="Delete Virtual Machine")
    @ns.expect(delete_vm_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def delete(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return {
            "success": delete_virtual_machine(
                jwt, customer_id, location, cloudspace_id, vm_id, **delete_vm_parser.parse_args()
            )
        }


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/audits")
class VirtualMachineAudits(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc("listVirtualMachineAudits", description="List virtual machine audits logs")
    @ns.marshal_with(audits_pagination_model)
    @ns.expect(audits_list_argparser)
    def get(self, customer_id, cloudspace_id, vm_id):
        args = audits_list_argparser.parse_args()
        args["vco"] = get_vco_id()
        args["resource_type"] = CloudResourceType.VIRTUAL_MACHINE.value
        args["resource_id"] = str(vm_id)
        args["customer_id"] = customer_id
        audits = AuditLog.list(**args)
        return pagination_handler_for_audits(audits, args["limit"])


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/statistics")
class VirtualMachineStats(Resource):
    @authenticated
    @ns.doc(shortcut="getVirtualMachineStats", description="Get Virtual Machine Statistics")
    @ns.expect(vm_stats_parser)
    @ns.marshal_with(vm_stats_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        kwargs = vm_stats_parser.parse_args()
        if kwargs.get("include"):
            try:
                kwargs["include"] = [VMStatistics.get_by_name(i).value for i in kwargs.pop("include", []) if i]
            except KeyError as exp:
                raise KeyError(
                    "include parameters should be one of "
                    "[cpu, network, memory, vm_capacity, vm_latency, vm_iops,"
                    "vm_bandwidth, vdisk_latency, vdisk_capacity, vdisk_bandwidth, vdisk_iops]"
                ) from exp
        return G8Client(location, jwt=jwt).get_vm_stats(vm_id, **kwargs)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/name")
class VirtualMachineRename(Resource):
    @authenticated
    @ns.doc(shortcut="renameVirtualMachine", description="Update virtual machine name")
    @ns.expect(rename_vm_parser)
    @ns.marshal_with(generate_api_response_model("cloudapi/machines/update$rename", api))
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).rename_vm(vm_id, **rename_vm_parser.parse_args())


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/agent")
class VirtualMachineAgent(Resource):
    @authenticated
    @ns.doc(shortcut="changeAgentStatus", description="Enable/Disable virtual machine agent")
    @ns.expect(change_vm_agent)
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        status = ns.payload["enabled"]
        if status:
            return G8Client(location, jwt=jwt).enable_vm_agent(vm_id)
        return disable_vm_agent(location=location, jwt=jwt, vm_id=vm_id)

    @authenticated
    @ns.doc(shortcut="getAgentStatus", description="Get virtual machine agent status")
    @ns.marshal_with(get_vm_agent)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        vm = G8Client(location, jwt=jwt).get_vm_info(vm_id)
        return {"status": vm["agent_status"]}


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/description")
class VirtualMachineUpdateDescription(Resource):
    @authenticated
    @ns.doc(shortcut="updateVirtualMachineDescription", description="Update virtual machine description")
    @ns.expect(redescribe_vm_parser)
    @ns.marshal_with(generate_api_response_model("cloudapi/machines/update$description", api))
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).update_vm_description(vm_id, **redescribe_vm_parser.parse_args())


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/size")
class VirtualMachineResize(Resource):
    @authenticated
    @ns.doc(shortcut="resizeVirtualMachine", description="Update virtual machine sizes of CPUs or memory")
    @ns.expect(resize_vm_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def put(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        resize_vm(customer_id, location, jwt, vm_id, **resize_vm_parser.parse_args())
        return SUCCESS


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/restore")
class VirtualMachineRestore(Resource):
    @authenticated
    @ns.doc(shortcut="restoreVirtualMachine", description="Restore virtual machine from recycle bin")
    @ns.expect(restore_vm_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def put(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        restore_vm(customer_id, location, jwt, cloudspace_id, vm_id, **restore_vm_parser.parse_args())
        return SUCCESS


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/start")
class VirtualMachineStart(Resource):
    @authenticated
    @ns.doc(shortcut="startVirtualMachine", description="Start virtual machine")
    @ns.marshal_with(success_model)
    @ns.expect(start_vm_parser)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        args = start_vm_parser.parse_args()
        tpm_secret = args.pop("tpm_secret", None)
        return vm_business.start_vm(location, jwt, vm_id, tpm_secret)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/stop")
class VirtualMachineStop(Resource):
    @authenticated
    @ns.doc(shortcut="stopVirtualMachine", description="Stop virtual machine")
    @ns.marshal_with(success_model)
    @ns.expect(stop_vm_parser)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).stop_vm(vm_id, **stop_vm_parser.parse_args())


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/resume")
class VirtualMachineResume(Resource):
    @authenticated
    @ns.doc(shortcut="resumeVirtualMachine", description="Resume virtual machine")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).resume_vm(vm_id)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/reboot")
class VirtualMachineReboot(Resource):
    @authenticated
    @ns.doc(shortcut="rebootVirtualMachine", description="Reboot virtual machine")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).reboot_vm(vm_id)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/reset")
class VirtualMachineReset(Resource):
    @authenticated
    @ns.doc(shortcut="resetVirtualMachine", description="Reset virtual machine")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).reset_vm(vm_id)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/compliance-scan")
class VirtualMachineComplianceScan(Resource):
    @authenticated
    @ns.expect(scan_vm_parser if is_dev_env else None)
    @ns.doc(shortcut="complianceScan", description="execute octopus scanner in the virtual machine")
    @ns.marshal_with(success_model)
    def post(self, customer_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        if is_dev_env:
            force = scan_vm_parser.parse_args()["force"]
        else:
            force = False
        scan_virtual_machine(jwt, get_vco_id(), customer_id, cloudspace_id, vm_id, force=force)
        return dict(success=True)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/move")
class VirtualMachineMove(Resource):
    @authenticated
    @ns.doc(shortcut="moveVirtualMachine", description="Move virtual machine")
    @ns.marshal_with(success_model)
    @ns.expect(move_vm_parser)
    @ns.response(400, "Bad Request: Target cloudspace ID not in the same location")
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        kwargs = move_vm_parser.parse_args()
        return cs_business.move_vm(location, vm_id, jwt, **kwargs)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/pause")
class VirtualMachinePause(Resource):
    @authenticated
    @ns.doc(shortcut="pauseVirtualMachine", description="Pause virtual machine")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).pause_vm(vm_id)


@ns.route("/external-network-interfaces/models")
class ExternalNetworkInterfaceModels(Resource):
    @authenticated
    @ns.doc(
        shortcut="listExternalNetworkInterfaceModels", description="List supported external network interface models"
    )
    @ns.marshal_with(external_nics_models_list_model)
    def get(self):
        return {"result": NicModels.values()}


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/external-nics")
class VirtualMachineExternalNetworks(Resource):
    @authenticated
    @ns.doc(
        shortcut="listExternalNetworksVirtualMachine",
        description="List external networks (NIC) that the virtual machine is attached to",
    )
    @ns.marshal_with(external_nics_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return {"result": cs_business.get_vm_external_nics(location, jwt, vm_id)}

    @authenticated
    @ns.doc(
        shortcut="attachExternalNetworksVirtualMachine", description="Attach external network (NIC) to virtual machine"
    )
    @ns.expect(attach_e_network_vm_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        kwargs = attach_e_network_vm_parser.parse_args()
        return vm_business.attach_external_nic_to_vm(customer_id, jwt, location, vm_id, **kwargs)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/external-nics/<external_ip_address>")
class VirtualMachineExternalNetwork(Resource):
    @authenticated
    @ns.doc(
        shortcut="getExternalNetworksVirtualMachine",
        description="Get external network (NIC) that the virtual machine is attached to",
    )
    @ns.marshal_with(external_nic_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id, external_ip_address):
        jwt = get_current_user_info().jwt
        external_ip_address = external_ip_address.split("/")[0]  # Strip mask from IP Addr if passed
        return cs_business.get_vm_external_nic(location, jwt, vm_id, external_ip_address)

    @authenticated
    @ns.doc(
        shortcut="detachExternalNetworksVirtualMachine",
        description="Detach external network (NIC) from virtual machine",
    )
    @ns.expect(detach_e_network_vm_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def delete(self, customer_id, location, account_id, cloudspace_id, vm_id, external_ip_address):
        jwt = get_current_user_info().jwt
        kwargs = detach_e_network_vm_parser.parse_args()
        return cs_business.detach_vm_external_nic(
            customer_id, cloudspace_id, location, jwt, vm_id, external_ip_address, **kwargs
        )


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/console")
class VirtualMachineConsole(Resource):
    @authenticated
    @ns.doc(
        shortcut="getVirtualMachineConsole",
        description="Get virtual machine console",
        model=fields.String(description="Console relative path"),
    )
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return cs_business.get_console_url(jwt, location, vm_id, request.url)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/disks")
class VirtualMachineDisks(Resource):
    @authenticated
    @ns.doc(shortcut="listDisksVirtualMachine", description="List disks on virtual machine")
    @ns.marshal_with(vm_disks_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return {"result": G8_cloudspace.get_vm_info(location, jwt=jwt, vm_id=vm_id)["disks"]}

    @authenticated
    @ns.doc(shortcut="attachDiskVirtualMachine", description="Attach a disk on virtual machine")
    @ns.expect(attach_disk_vm_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        vm_business.attach_disk_to_vm(customer_id, location, jwt, vm_id, **attach_disk_vm_parser.parse_args())
        return SUCCESS


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/disks/<int:disk_id>")
class VirtualMachineDetachDisk(Resource):
    @authenticated
    @ns.doc(shortcut="detachDiskVirtualMachine", description="Detach a disk on virtual machine")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def delete(self, location, account_id, cloudspace_id, vm_id, disk_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).detach_disk_vm(vm_id, disk_id)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/consumption")
class VirtualMachineConsumption(Resource):
    @authenticated
    @ns.doc(shortcut="getVirtualMachineConsumption", description="Get virtual machine resource consumption")
    @ns.expect(vm_consumption_parser)
    @ns.marshal_with(vm_consumption_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        return cs_business.calculate_vm_consumption(
            location, get_current_user_info().jwt, vm_id, **vm_consumption_parser.parse_args()
        )


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/consumption/series")
class VirtualMachineConsumptionSeries(Resource):
    @authenticated
    @ns.doc(
        shortcut="getVirtualMachineConsumptionSeries", description="Get virtual machine resource consumption timeseries"
    )
    @ns.expect(vm_consumption_ts_parser)
    @ns.marshal_with(vm_consumption_ts_model, skip_none=True)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        return cs_business.calculate_vm_consumption_ts(
            location, get_current_user_info().jwt, vm_id, **vm_consumption_ts_parser.parse_args()
        )


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/cdrom-images")
class VirtualMachineCDROMs(Resource):
    @authenticated
    @ns.doc(shortcut="listVirtualMachineCDROMs", description="List CDROM images attached to virtual machine")
    @ns.marshal_with(vm_cdrom_list_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return {"result": G8_cloudspace.get_vm_cdroms(location, jwt=jwt, vm_id=vm_id)}

    @authenticated
    @ns.doc(shortcut="attachVirtualMachineCDROM", description="Attach CDROM image to Virtual Machine")
    @ns.expect(attach_cdrom_vm_parser)
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        args = attach_cdrom_vm_parser.parse_args()
        return vm_business.attach_disk_to_vm(customer_id, location, jwt, vm_id, disk_id=args["cdrom_id"])


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/cdrom-images/<int:cdrom_id>")
class VirtualMachineCDROM(Resource):
    @authenticated
    @ns.doc(shortcut="getVMCDROMInfo", description="Get virtual machine attached CDROM image Info")
    @ns.marshal_with(vm_cdrom_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id, cdrom_id):
        jwt = get_current_user_info().jwt
        return G8_cloudspace.get_vm_cdrom_info(location, jwt=jwt, vm_id=vm_id, cdrom_id=cdrom_id)

    @authenticated
    @ns.doc(shortcut="detachVirtualMachineCDROM", description="Detach CDROM image from virtual machine")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def delete(self, location, account_id, cloudspace_id, vm_id, cdrom_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).detach_disk_vm(vm_id=vm_id, disk_id=cdrom_id)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/template")
class VirtualMachineTemplate(Resource):
    @authenticated
    @ns.doc(shortcut="createTemplateFromVM", description="Create a template from virtual machine")
    @ns.expect(vm_create_template_parser)
    @ns.marshal_with(generate_api_response_model("cloudapi/machines/createTemplate", api))
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        args = vm_create_template_parser.parse_args()
        return vm_business.create_template(
            customer_id, location, jwt=jwt, vm_id=vm_id, template_name=args["template_name"]
        )


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/granted-roles")
class GetCloudspaceRoles(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="listRolesOnVirtualMachine", description="List roles with access to a virtual machine")
    @ns.marshal_with(CustomerRoleStruct.list_model())
    @process_cloudspace_id(preserve_customer_id=True)
    def get(self, customer_id, location, account_id, cloudspace_id, vm_id):
        roles = list_roles_on_vm(
            jwt=get_current_user_info().jwt, customer_id=customer_id, location=location, vm_id=vm_id
        )
        return {"result": [role.to_mongo().to_dict() for role in roles]}


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/network-interfaces/<mac_address>/model")
class UpdateVMNICModel(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(update_nic_model_parser)
    @ns.marshal_with(changed_model)
    @ns.doc(shortcut="updateNetworkInterfaceModel", description="Update virtual machine network interface model")
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id, mac_address):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).update_vm_nic_model(
            vm_id, mac_address, **update_nic_model_parser.parse_args()
        )


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/network-interfaces/<mac_address>")
class UpdateVMMACAddress(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(update_MAC_address_parser)
    @ns.marshal_with(changed_model)
    @ns.doc(shortcut="updateVirtualMachineMacAddress", description="Update virtual machine MAC address")
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id, mac_address):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).update_vm_mac_address(
            vm_id, mac_address, **update_MAC_address_parser.parse_args()
        )


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/notes")
class VMNotes(Resource):
    @authenticated
    @ns.doc(shortcut="getVirtualMachineNotes", description="Get virtual machine notes")
    @ns.marshal_with(resource_notes_model)
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        vm_metadata = vm_business.get_vm_metadata(location, jwt, vm_id)
        return {"result": dataclasses.asdict(vm_metadata)["notes"]}

    @authenticated
    @ns.doc(shortcut="createVirtualMachineNote", description="Create virtual machine note")
    @ns.marshal_with(success_model)
    @ns.expect(note_input)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        payload = NoteInputsStruct(**ns.payload)
        user_info = get_current_user_info()
        vm_business.create_vm_note(location, user_info.jwt, vm_id, user_info.name, payload)
        return SUCCESS


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/notes/<note_id>")
class VMNoteInfo(Resource):
    @authenticated
    @ns.doc(shortcut="updateVirtualMachineNote", description="Update virtual machine note")
    @ns.expect(note_input)
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id, note_id):
        user_info = get_current_user_info()
        payload = NoteInputsStruct(**ns.payload)
        vm_business.update_vm_note(vm_id, location, user_info.jwt, note_id, user_info.name, payload)
        return SUCCESS

    @authenticated
    @ns.doc(shortcut="deleteVirtualMachineNote", description="Delete virtual machine note")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def delete(self, location, account_id, cloudspace_id, vm_id, note_id):
        jwt = get_current_user_info().jwt
        vm_business.delete_vm_note(vm_id, location, jwt, note_id)
        return SUCCESS


@alpha_ns.route(f"/customers{REQUESTS_ROUTE}/<int:vm_id>/external-nics/<external_ip_address>/dns")
class DnsExternalNetwork(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.doc(shortcut="getNicDnsRecord", description="Get external nic dns records")
    @ns.response(404, "DnsRecord matching query does not exist.")
    @process_cloudspace_id(preserve_customer_id=True)
    @ns.marshal_with(ResourceDnsRecordStruct.list_model(api))
    def get(self, customer_id, account_id, location, external_ip_address, vm_id, cloudspace_id):
        return {"result": DnsRecord.list(g8_name=location, vm_id=vm_id, nic_ip=external_ip_address)}

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(resources_domain_parser)
    @ns.doc(shortcut="AddNicDomain", description="Add external network interface domain")
    @process_cloudspace_id(preserve_customer_id=True)
    @ns.marshal_with(success_model)
    def post(self, customer_id, account_id, location, cloudspace_id, vm_id, external_ip_address):
        jwt = get_current_user_info().jwt
        args = resources_domain_parser.parse_args()
        cs_business.add_dns_record(
            customer_id=customer_id,
            location=location,
            cloudspace_id=cloudspace_id,
            jwt=jwt,
            vco_id=get_vco_id(),
            domain_name=args["domain"],
            priority=args["priority"],
            weight=args["weight"],
            port=args["port"],
            service=args["service"],
            protocol=args["protocol"],
            flag=args["flag"],
            tag=args["tag"],
            caa_domain=args["caa_domain"],
            value=args["value"],
            type_=SupportedDNSRecordType.from_string(resources_domain_parser.parse_args()["type"]),
            vm_id=vm_id,
            external_ip_address=external_ip_address,
        )
        return SUCCESS


@alpha_ns.route(f"/customers{REQUESTS_ROUTE}/<int:vm_id>/external-nics/<external_ip_address>/dns/<domain>")
class ExternalNetworkDnsRecord(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(delete_domain_parser)
    @ns.doc(shortcut="DeleteExternalNetworkDnsRecord", description="Delete a dns record from External network")
    @process_cloudspace_id(preserve_customer_id=True)
    @ns.response(404, "DnsRecord matching query does not exist.")
    @ns.marshal_with(success_model)
    def delete(self, domain, cloudspace_id, vm_id, external_ip_address, **kwargs):
        type_ = delete_domain_parser.parse_args()["record_type"]
        cs_business.delete_dns_records(
            DnsRecord.list(
                domain_name=domain,
                cloudspace_id=cloudspace_id,
                vm_id=vm_id,
                nic_ip=external_ip_address,
                record_type=type_,
            )
        )
        return SUCCESS


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/pin-cpus")
class PinCPUS(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(pin_vm_cpus_parser)
    @ns.marshal_with(changed_model)
    @ns.doc(shortcut="PinCPUS", description="Pin vm vcpus to physical cpus")
    @process_cloudspace_id(preserve_customer_id=True)
    def put(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        vm_business.pin_vm_cpus(customer_id, location, jwt, vm_id)


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/unpin-cpus")
class UnpinCPUS(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(unpin_vm_cpus_parser)
    @ns.marshal_with(changed_model)
    @ns.doc(shortcut="UnpinCPUS", description="Unpin vm vcpus from physical cpus")
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).unpin_vm_cpus(vm_id, **unpin_vm_cpus_parser.parse_args())


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/boot-type")
class VMBootType(Resource):
    @authenticated
    @ns.expect(vm_boot_type_parser)
    @ns.marshal_with(changed_model)
    @ns.doc(shortcut="SetBootType", description="Set Virtual Machine's boot type")
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return G8Client(location, jwt=jwt).update_vm_boot_type(vm_id, **vm_boot_type_parser.parse_args())


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/boot-disk")
class SetBootDisk(Resource):
    @authenticated
    @ns.expect(boot_disk_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="SetBootDisk", description="Set Virtual Machine's boot disk")
    @process_cloudspace_id()
    def put(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        G8Client(location, jwt=jwt).set_vm_boot_disk(vm_id, **boot_disk_parser.parse_args())
        return SUCCESS


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/cpu-topology")
class SetCPUTopology(Resource):
    @authenticated
    @ns.expect(CPUTopologyStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="SetCPUTopology", description="Set Virtual Machine's CPU topology")
    @process_cloudspace_id(preserve_customer_id=True)
    def put(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        set_cpu_topology_parser = ns.payload

        return vm_business.set_cpu_topology(
            customer_id,
            location,
            jwt,
            vm_id,
            CPUTopologyStruct(
                cores=int(set_cpu_topology_parser["cores"]),
                threads=int(set_cpu_topology_parser["threads"]),
                sockets=int(set_cpu_topology_parser["sockets"]),
            ),
        )

    @authenticated
    @ns.marshal_with(CPUTopologyStruct.model(api))
    @ns.doc(shortcut="GetCPUTopology", description="Get Virtual Machine's CPU topology")
    @process_cloudspace_id()
    def get(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        cpu_topology = G8Client(location, jwt=jwt).get_vm_info(vm_id)["cpu_topology"]
        return cpu_topology


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/network-interfaces/ip-address")
class UpdateVMIPAddress(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(update_IP_address_parser)
    @ns.marshal_with(changed_model)
    @ns.doc(shortcut="updateVirtualMachineIPAddress", description="Update virtual machine IP address")
    @process_cloudspace_id(preserve_customer_id=True)
    def put(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return vm_business.update_vm_ip_address(
            customer_id, jwt, location, vm_id, **update_IP_address_parser.parse_args()
        )


@ns.route(REQUESTS_ROUTE + "/create-from-backup")
class VirtualMachineFromBackup(Resource):
    @authenticated
    @ns.doc(shortcut="createVirtualMachineFromBackup", description="Create virtual machine from backup")
    @ns.expect(CreateVmFromBackupStruct.model(api))
    @ns.marshal_with(RestoreVmResponse.model(api))
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id):
        jwt = get_current_user_info().jwt

        backup_data = CreateVmFromBackupStruct(**ns.payload)
        return backup_business.create_vm_from_backup(jwt, location, cloudspace_id, backup_data)


@alpha_ns.route(REQUESTS_ROUTE + "/<int:vm_id>/restore-vm-progress")
class RestoreVirtualMachineFromBackup(Resource):
    @authenticated
    @ns.doc(shortcut="restoreVirtualMachineProgress", description="Progress of restoring virtual machine from backup")
    @process_cloudspace_id(preserve_customer_id=True)
    @ns.marshal_with(RestoreVMStruct.model(api))
    def get(self, customer_id, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return backup_business.vm_restore_progress(jwt=jwt, vm_id=vm_id, location=location)


@alpha_ns.route(REQUESTS_ROUTE + "/<int:vm_id>/backup-policies/<int:policy_id>")
class VirtualMachinePolicy(Resource):
    @authenticated
    @ns.doc(shortcut="assignVirtualMachineBackupPolicy", description="Assign virtual machine backup policy")
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, location, account_id, cloudspace_id, vm_id, policy_id):
        jwt = get_current_user_info().jwt
        return backup_business.assign_vm_backup_policy(jwt, location, vm_id, policy_id)

    @authenticated
    @ns.doc(shortcut="unassignVirtualMachineBackupPolicy", description="Unassign virtual machine backup policy")
    @ns.marshal_with(success_model)
    @process_cloudspace_id(preserve_customer_id=True)
    def delete(self, customer_id, location, account_id, cloudspace_id, vm_id, policy_id):
        jwt = get_current_user_info().jwt
        return backup_business.unassign_vm_backup_policy(jwt, location, vm_id, policy_id)


@alpha_ns.route(REQUESTS_ROUTE + "/<int:vm_id>/backups")
class VMBackups(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(list_backups_parser)
    @ns.marshal_with(get_pagination_model("ListBackups", BackupStruct.model(api)))
    @ns.doc(shortcut="listVMBackups", description="List vm backups")
    @process_cloudspace_id()
    def get(self, account_id, location, vm_id, cloudspace_id):
        args = list_backups_parser.parse_args()
        policy = args.get("policy")
        target_id = args.get("target_id")
        status = args.get("status")
        jwt = get_current_user_info().jwt
        limit = args.get("limit")
        start_after = args.get("start_after")

        return pagination_handler(
            backup_business.list_backups,
            limit=limit,
            start_after=start_after,
            jwt=jwt,
            location=location,
            vm_id=vm_id,
            policy=policy,
            target_id=target_id,
            status=status,
            exclude_expired=args.get("exclude_expired"),
            sort_direction=args.get("sort_direction"),
            sort_by=args.get("sort_by"),
        )

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(create_backup_parser)
    @ns.marshal_with(BackupWithVMStruct.model(api))
    @ns.doc(shortcut="createVMBackup", description="Create a new vm backup")
    @process_cloudspace_id(preserve_customer_id=True)
    def post(self, customer_id, account_id, location, cloudspace_id, vm_id):
        args = create_backup_parser.parse_args()
        policy = args.get("policy")
        jwt = get_current_user_info().jwt
        return backup_business.create_backup(
            jwt, get_vco_id(), location, vm_id, policy, by_customer=True, customer_id=customer_id
        )


@alpha_ns.route(REQUESTS_ROUTE + "/<int:vm_id>/backups/<backup_id>")
class VMBackup(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(BackupWithVMStruct.model(api))
    @ns.doc(shortcut="getVMBackup", description="Get vm backup")
    @process_cloudspace_id()
    def get(self, account_id, location, cloudspace_id, vm_id, backup_id):
        jwt = get_current_user_info().jwt
        return backup_business.get_backup(jwt, location, backup_id)

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteVMBackup", description="Delete a backup")
    @process_cloudspace_id()
    def delete(self, account_id, location, cloudspace_id, vm_id, backup_id):
        jwt = get_current_user_info().jwt
        backup_business.delete_backup(jwt, location, backup_id)
        return SUCCESS


@ns.route(REQUESTS_ROUTE + "/<int:vm_id>/tpm")
class VirtualMachineTPM(Resource):
    @authenticated
    @ns.doc(shortcut="addTPM", description="add virtual machine TPM")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def post(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        G8Client(location, jwt=jwt).add_vtpm(vm_id)
        return SUCCESS

    @authenticated
    @ns.doc(shortcut="removeTPM", description="remove virtual machine TPM")
    @ns.marshal_with(success_model)
    @process_cloudspace_id()
    def delete(self, location, account_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        G8Client(location, jwt=jwt).remove_vtpm(vm_id)
        return SUCCESS
