# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115

from dacite import from_dict
from flask import request
from flask.helpers import make_response
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean

from meneja.api import api
from meneja.api.common import BinaryStringField
from meneja.api.meneja import audit_forwarding_status_model, audit_model, audits_list_argparser, audits_pagination_model
from meneja.api.vco import SUCCESS
from meneja.api.vco import admin_ns as ns
from meneja.api.vco import alpha_ns, get_vco_id, success_model
from meneja.business import backups as backup_business
from meneja.business import vco as vco_business
from meneja.business.auth import requires_vco_admin
from meneja.business.db_model_2_rest import generate_restfull_model_spec
from meneja.business.g8.g8 import (
    get_vco_emergency_notifications_contact,
    get_vco_emergency_notifications_subscription,
    update_vco_emergency_notifications_contact_info,
    update_vco_emergency_notifications_subscription,
)
from meneja.business.notifications import (
    create_blog,
    create_notification,
    delete_blog,
    delete_notification,
    get_blog_preview,
    get_notifications,
    get_vco_timezone,
    list_notification_utilities,
    send_test_vco_notification,
    send_vco_notification,
    unpublish_blog,
    update_blog,
    update_notification,
)
from meneja.business.vco.audits import get_audit_log_forwarding_status
from meneja.business.vco.customer import add_demo_user
from meneja.business.vco.dns import get_ds_key
from meneja.business.vco.oidc_providers import (
    activate_oidc_provider,
    create_oidc_provider,
    deactivate_oidc_provider,
    delete_oidc_provider,
    disable_password_login,
    enable_password_login,
    get_oidc_provider,
    get_password_login_status,
    list_oidc_providers,
    update_oidc_provider,
)
from meneja.lib.enumeration import BackupStatuses, NotificationIssuer, NotificationType
from meneja.lib.fixes import FixedArgument
from meneja.lib.pagination import get_pagination_model, pagination_handler, pagination_handler_for_audits
from meneja.model.audit import AuditLog
from meneja.model.backups import Policies, Targets
from meneja.model.notifications import NotificationBlog, Notifications
from meneja.model.prerelease import PreReleaseFeatures
from meneja.model.vco import VCO, ColorScheme
from meneja.model.vco.software_licenses import VCOLicenses
from meneja.structs.meneja.dataclasses.dns import DnsDSRecord
from meneja.structs.notification_subscription.dataclasses import (
    NotificationsForwardingStruct,
    NotificationUtilitiesStruct,
    TestEmailsStruct,
    TimezoneStruct,
)
from meneja.structs.notification_subscription.notification_config import NotificationConfigStruct
from meneja.structs.software_licenses.vco_software_licenses import VcoSoftwareLicensesStruct
from meneja.structs.vco.dataclasses.alert_notification_contact_info import AlertNotificationContactStruct
from meneja.structs.vco.dataclasses.demo_user import DemoUserStruct
from meneja.structs.vco.dataclasses.domain_ownership_code import DomainOwnershipCodeStruct
from meneja.structs.vco.dataclasses.oidc_provider import (
    OIDCProviderCreateStruct,
    OIDCProviderIdStruct,
    OIDCProviderStruct,
)
from meneja.structs.vco.dataclasses.password_login import PasswordLoginStatusStruct
from meneja.structs.vco.dataclasses.prerelease_feature import PreReleaseFeatureStruct
from meneja.structs.vco.dataclasses.resource_domain import TopLevelDomainStruct
from meneja.structs.vco.dataclasses.show_prices import ShowPricesStruct
from meneja.structs.vco.dataclasses.vco import (
    AuditLogForwardingConfig,
    BackupCreateTargetStruct,
    BackupFullStruct,
    BackupPoliciesStruct,
    BackupPolicyListStruct,
    BackupPolicyStruct,
    BackupStruct,
    BackupTargetsStruct,
    BackupTargetStruct,
    BackupWithVMStruct,
    CustomerSupportTextStruct,
    IdModelStruct,
    LicenseIncompliancyForwardingStruct,
    VmFromBackupStruct,
)
from meneja.structs.vco.dataclasses.vco_spla_settings import VcoSplaSettingsPostStruct, VcoSplaSettingsStruct
from meneja.structs.vco_location_subscription.vco_location import VcoLocationStruct

vco_website_parser = reqparse.RequestParser(argument_class=FixedArgument)
vco_website_parser.add_argument("vco_website", help="Website", type=str, location="args", required=True)

vco_color_scheme_model = api.model("ColorScheme", generate_restfull_model_spec(ColorScheme))

vco_color_scheme_parser = reqparse.RequestParser(argument_class=FixedArgument)
vco_color_scheme_parser.add_argument("primary", help="Primary color", type=str, location="args", default="#1976D2")
vco_color_scheme_parser.add_argument("secondary", help="Secondary color", type=str, location="args", default="#424242")
vco_color_scheme_parser.add_argument("accent", help="Accent color", type=str, location="args", default="#82B1FF")
vco_color_scheme_parser.add_argument("error", help="Error color", type=str, location="args", default="#FF5252")
vco_color_scheme_parser.add_argument("info", help="Info color", type=str, location="args", default="#2196F3")
vco_color_scheme_parser.add_argument("success", help="Success color", type=str, location="args", default="#4CAF50")
vco_color_scheme_parser.add_argument("warning", help="Warning color", type=str, location="args", default="#FFC107")

vco_description_parser = reqparse.RequestParser(argument_class=FixedArgument)
vco_description_parser.add_argument(
    "description", required=True, help="Intro text in the portal landing page", location="args"
)

logo_parser = reqparse.RequestParser(argument_class=FixedArgument)
logo_parser.add_argument("logo", type=str, location="form", required=True, help="Base64 of the branding logo")

vco_naming_parser = reqparse.RequestParser(argument_class=FixedArgument)
vco_naming_parser.add_argument("name", type=str, required=False, location="args", help="name")
vco_naming_parser.add_argument("utility_name", type=str, required=False, location="args", help="utility name")

vco_naming_model = api.model("NamingInfo", {"name": fields.String(), "utility_name": fields.String()})
top_level_domain_parser = reqparse.RequestParser(argument_class=FixedArgument)
top_level_domain_parser.add_argument("domain", type=str, location="args", required=True, help="Top level domain")
domain_ownership_verification_model = DomainOwnershipCodeStruct.model(api)
demo_user_model = DemoUserStruct.model(api)

email_notification_forwarding = NotificationsForwardingStruct.model(api)
license_incompliancy_forwarding = LicenseIncompliancyForwardingStruct.model(api)

edit_notification_forwarding_parser = reqparse.RequestParser(argument_class=FixedArgument)
edit_notification_forwarding_parser.add_argument(
    "enabled", type=boolean, required=True, location="args", help="Notifications forwarding status"
)

edit_license_incompliancy_parser = reqparse.RequestParser(argument_class=FixedArgument)
edit_license_incompliancy_parser.add_argument(
    "enabled", type=boolean, required=True, location="args", help="Notifications forwarding status"
)

email_notification_get_parser = reqparse.RequestParser(argument_class=FixedArgument)
email_notification_get_parser.add_argument(
    "include_past", type=boolean, default=False, location="args", help="include past maintenance notifications"
)
email_notification_get_parser.add_argument(
    "notification_type", type=str, choices=NotificationType.values(), location="args", help="notification type"
)
upload_url_model = AuditLogForwardingConfig.model(api)

email_notification_model = NotificationConfigStruct.model()

get_blog_fields = generate_restfull_model_spec(NotificationBlog, api, True)
get_blog_model = api.model("Rollout", get_blog_fields)
blog_pagination_model = get_pagination_model("blogPagination", get_blog_model)

blog_list_parser = reqparse.RequestParser(argument_class=FixedArgument)
blog_list_parser.add_argument(
    "limit", type=int, default=15, location="args", help="Flag to limit the amount of results. 0 means no limit"
)
blog_list_parser.add_argument(
    "start_after", type=int, location="args", default=0, help="Start returning records after index"
)

spla_edit_parser = reqparse.RequestParser(argument_class=FixedArgument)
spla_edit_parser.add_argument("name", help="Name of user configured SPLA", type=str, location="args", default="")
spla_edit_parser.add_argument("email", help="Name of user configured SPLA", type=str, location="args", default="")
spla_edit_parser.add_argument("spla_number", help="SPLA number", type=str, location="args", default="")
show_prices_parser = reqparse.RequestParser(argument_class=FixedArgument)
show_prices_parser.add_argument("enabled", type=boolean, required=True, help="Enable show prices", location="args")


unsubscribe_parser = reqparse.RequestParser(argument_class=FixedArgument)
unsubscribe_parser.add_argument("location", type=str, location="args", help="Target location", required=True)
# subscribe_policy_parser = reqparse.RequestParser(argument_class=FixedArgument)
# subscribe_policy_parser.add_argument("target_id", type=str, location="args", help="Target id", required=True)

backup_parser = reqparse.RequestParser(argument_class=FixedArgument)
backup_parser.add_argument("location", type=str, location="args", help="Target location", required=True)

backup_location_parser = reqparse.RequestParser(argument_class=FixedArgument)
backup_location_parser.add_argument("location", type=str, location="args", help="Target location", required=True)
backup_location_parser.add_argument("cloudspace_id", type=str, location="args", help="Cloudspace", required=True)

sync_backup_parser = reqparse.RequestParser(argument_class=FixedArgument)
sync_backup_parser.add_argument("location", type=str, location="args", help="Target location", required=True)


list_backups_parser = reqparse.RequestParser(argument_class=FixedArgument)
list_backups_parser.add_argument("location", type=str, location="args", help="Target location", required=True)
list_backups_parser.add_argument("vm_id", type=int, location="args", help="Vm id")
list_backups_parser.add_argument("target_id", type=str, location="args", help="Target id")
list_backups_parser.add_argument(
    "status", type=str, location="args", help="Backup status", choices=BackupStatuses.values()
)
list_backups_parser.add_argument(
    "limit",
    type=int,
    default=25,
    location="args",
    help="limit",
)
list_backups_parser.add_argument(
    "start_after", type=int, default=0, location="args", help="Index to start listing after"
)

list_backups_parser.add_argument(
    "exclude_expired", type=bool, default=False, location="args", help="Exclude expired backups"
)
list_backups_parser.add_argument(
    "sort_direction",
    type=int,
    default=-1,
    location="args",
    help="Sort direction 1 for ascending and -1 for descending order",
    choices=[-1, 1],
)
list_backups_parser.add_argument(
    "sort_by", type=str, default="creation_timestamp", location="args", help="Sort by field"
)
list_backups_parser.add_argument("g8_target_id", type=int, location="args", help="G8 target ID")


@ns.route("/branding/vco-website")
class VCOWebsite(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(vco_website_parser)
    @ns.doc(shortcut="setWebsite", description="Set Website")
    def put(self):
        vco_business.set_vco_website(get_vco_id(), **vco_website_parser.parse_args())

    @authenticated
    @ns.doc(shortcut="getVWebsite", description="Get Website", model=fields.String(description="Website"))
    def get(self):
        return VCO.get_by_id(get_vco_id(), only=["vco_website"]).vco_website


@ns.route("/branding/color-scheme")
class VCOColorScheme(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="setColorScheme", description="Set color scheme")
    @ns.expect(vco_color_scheme_parser)
    def post(self):
        vco_business.set_vco_branding_colors(get_vco_id(), **vco_color_scheme_parser.parse_args())

    @ns.doc(shortcut="getColorScheme", description="Get the color scheme")
    @ns.marshal_with(vco_color_scheme_model)
    def get(self):
        return VCO.get_color_scheme(get_vco_id())


@alpha_ns.route("/admin/dns/validate-domain-ownership")
class VcoTopLevelDomainValidationTxt(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(top_level_domain_parser)
    @ns.marshal_with(success_model)
    @ns.doc(
        shortcut="validateVcoDomainOwnership",
        description="Validate vco domain ownership",
    )
    def get(self):
        vco_business.validate_domain_ownership(get_vco_id(), **top_level_domain_parser.parse_args())
        return SUCCESS


@alpha_ns.route("/dns/validate-ns-records")
class TopLevelDomainValidationNs(Resource):
    @authenticated
    @ns.expect(top_level_domain_parser)
    @ns.marshal_with(success_model)
    @ns.doc(
        shortcut="validateDomainNs",
        description="Validate domain NS configuration",
    )
    def get(self):
        vco_business.validate_domain_ns_config(**top_level_domain_parser.parse_args())
        return SUCCESS


@ns.route("/branding/description")
class VCODescription(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getDescription", description="Set landing portal description")
    def get(self):
        return VCO.get_description(get_vco_id())

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="setDescription", description="Set landing portal description")
    @ns.expect(vco_description_parser)
    def post(self):
        args = vco_description_parser.parse_args()
        vco_business.set_vco_branding_description(get_vco_id(), args["description"])


@alpha_ns.route("/admin/dns")
class VCODomainOwnershipVerification(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(domain_ownership_verification_model)
    @ns.response(200, "Domain Verification code")
    @ns.doc(
        shortcut="getVCODomainOwnershipCode",
        description="Get the code required to be added by the VCO to txt record to provide ownership of a domain",
    )
    def get(self):
        return vco_business.get_domain_ownership_code(get_vco_id())

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(domain_ownership_verification_model)
    @ns.response(200, "Domain Verification code")
    @ns.doc(
        shortcut="newVCODomainOwnershipCode",
        description="Generate and returns a new ownership of a domain code for the VCO",
    )
    def put(self):
        return vco_business.generate_new_ownership_code(get_vco_id())


@alpha_ns.route("/admin/dns/top-level-domain")
class VCOTopLevelDomain(Resource):
    @authenticated
    @ns.marshal_with(TopLevelDomainStruct.model(api))
    @ns.response(200, "Top level domain")
    @ns.response(404, "Top level domain is not set yet")
    @ns.doc(shortcut="getVCOTopLevelDomain", description="Get top level domain of a VCO")
    def get(self):
        return vco_business.get_vco_domain(get_vco_id())

    @authenticated
    @ns.doc(shortcut="addVCOTopLevelDomain", description="Add top level domain to VCO")
    @ns.response(200, "Top level domain")
    @ns.marshal_with(success_model)
    @requires_vco_admin
    @ns.expect(top_level_domain_parser)
    def put(self):
        vco_business.add_vco_top_level_domain(get_vco_id(), **top_level_domain_parser.parse_args())
        return SUCCESS

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteVCOTopLevelDomain", description="Delete top level domain of a VCO")
    def delete(self):
        vco_business.delete_vco_domain(get_vco_id())
        return SUCCESS


@ns.route("/branding/logo")
class VCOLogo(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="setLogo", description="Set logo")
    @ns.expect(logo_parser)
    def post(self):
        jwt = get_current_user_info().jwt
        vco_business.set_vco_branding_logo(get_vco_id(), logo_parser.parse_args().get("logo"), jwt)

    @ns.doc(shortcut="getLogo", description="Get logo")
    @ns.produces(["image/png"])
    @ns.response(404, "Logo is not set yet.")
    @ns.response(200, "Logo Image", model=BinaryStringField())
    def get(self):
        logo = VCO.get_logo(get_vco_id())
        if logo:
            return make_response((logo, 200, {"Content-Type": "image/png"}))
        else:
            raise KeyError("Logo is not set yet.")


@ns.route("/naming")
class VCONaming(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="updateNaming", description="Update Naming")
    @ns.expect(vco_naming_parser)
    def put(self):
        vco_business.update_vco_naming(get_vco_id(), **vco_naming_parser.parse_args())

    @authenticated
    @ns.doc(shortcut="getNaming", description="Get Naming")
    @ns.marshal_with(vco_naming_model)
    def get(self):
        vco = VCO.get_by_id(get_vco_id(), only=["name", "utility_name"])
        return vco


@ns.route("/licenses")
class VCOSoftwareLicenses(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getVCOSoftwareLicenses", description="Get VCO Software Licenses")
    @ns.marshal_with(VcoSoftwareLicensesStruct.model())
    def get(self):
        return VCOLicenses.get_by_vco_id(get_vco_id())


chat_parser = reqparse.RequestParser(argument_class=FixedArgument)
chat_parser.add_argument("enabled", type=boolean, required=True, help="Enable chat", location="args")

chat_model = api.model("ChatConfig", {"enabled": fields.Boolean(description="Chat enabled")})


@ns.route("/chat")
class ChatConfig(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(chat_parser)
    @ns.response(204, "Updated")
    @ns.doc(shortcut="setChatConfig", description="Set Chat Configuration")
    def put(self):
        vco_business.set_chat_config(get_vco_id(), **chat_parser.parse_args())

    @authenticated
    @ns.marshal_with(chat_model)
    @ns.doc(shortcut="getChatConfig", description="Get Chat Configuration")
    def get(self):
        return vco_business.get_chat_config(get_vco_id())


@ns.route("/demo-user")
class DemoUser(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(demo_user_model)
    @ns.doc(shortcut="setDemoUser", description="Set Demo User info")
    def post(self):
        add_demo_user(**ns.payload, vco_id=get_vco_id())

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(demo_user_model)
    @ns.doc(shortcut="getDemoUser", description="Get Demo User info")
    def get(self):
        return VCO.get_demo_user(vco_id=get_vco_id())

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="unsetDemoUser", description="Unset Demo user")
    def delete(self):
        VCO.unset_demo_user(vco_id=get_vco_id())


@ns.route("/email-notification-forwarding")
class EmailNotificationForwarding(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getNotificationForwarding", description="Gets email notification forwarding")
    @ns.marshal_with(email_notification_forwarding)
    def get(self):
        return dict(enabled=VCO.get_by_id(get_vco_id()).settings.email_notification_forwarding)

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="editNotificationForwarding", description="Edit email notification forwarding")
    @ns.expect(edit_notification_forwarding_parser)
    def put(self):
        args = edit_notification_forwarding_parser.parse_args()
        vco = VCO.get_by_id(get_vco_id())
        vco.update(settings__email_notification_forwarding=args.get("enabled"))


@ns.route("/email-notifications")
class EmailNotifications(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="listVCONotifications", description="get email notifications")
    @ns.expect(email_notification_get_parser)
    @ns.marshal_list_with(email_notification_model)
    def get(self):
        args = email_notification_get_parser.parse_args()
        issuer_id = f"{NotificationIssuer.VCO}:{get_vco_id()}"
        return get_notifications(issuer_id, **args)

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="createVCONotification", description="Create a new email notification")
    @ns.expect(email_notification_model)
    def post(self):
        issuer_id = f"{NotificationIssuer.VCO}:{get_vco_id()}"
        return create_notification(NotificationConfigStruct(ns.payload).to_mongoengine(), issuer_id)


@ns.route("/email-notification/<notification_id>")
class EmailNotification(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getVCONotification", description="Get email notification")
    @ns.marshal_with(email_notification_model)
    def get(self, notification_id):
        issuer_id = f"{NotificationIssuer.VCO}:{get_vco_id()}"
        return Notifications.get_by_id(notification_id, issuer_id)

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="updateVCONotification", description="Update email notification")
    @ns.expect(email_notification_model)
    def put(self, notification_id):
        issuer_id = f"{NotificationIssuer.VCO}:{get_vco_id()}"
        return update_notification(issuer_id, notification_id, NotificationConfigStruct(ns.payload).to_mongoengine())

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="deleteVCONotification", description="Delete email notification")
    def delete(self, notification_id):
        issuer_id = f"{NotificationIssuer.VCO}:{get_vco_id()}"
        return delete_notification(issuer_id, notification_id)


@ns.route("/email-notification/<notification_id>/preview")
class EmailNotificationPreview(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getVCONotificationPreview", description="get email notification preview")
    @ns.marshal_with(email_notification_model)
    def get(self, notification_id):
        vco_id = get_vco_id()
        issuer_id = f"{NotificationIssuer.VCO}:{vco_id}"
        return get_blog_preview(notification_id, issuer_id, vco_id)


@ns.route("/email-notification/<notification_id>/send")
class SendEmailNotification(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="sendVCONotification", description="Send email notification")
    def put(self, notification_id):
        return send_vco_notification(notification_id, get_vco_id())


@ns.route("/email-notification/<notification_id>/send-test")
class SendTestEmailNotification(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(TestEmailsStruct.model(api))
    @ns.doc(shortcut="sendVCOTestNotification", description="Send test email notification")
    def put(self, notification_id):
        emails = ns.payload["emails"]
        return send_test_vco_notification(notification_id, emails, get_vco_id())


@ns.route("/email-notification/<notification_id>/blog")
class CreateNotificationBlog(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="createNotificationBlog", description="Create notification blog")
    def put(self, notification_id):
        return create_blog(notification_id, get_vco_id())

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="unpublishVCONotification", description="Unpublish email notification")
    def delete(self, notification_id):
        issuer_id = f"{NotificationIssuer.VCO}:{get_vco_id()}"
        return unpublish_blog(notification_id, issuer_id)


@ns.route("/email-notification/utilities")
class EmailNotificationUtilities(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(NotificationUtilitiesStruct.model(api))
    @ns.doc(shortcut="listNotificationUtilities", description="List notification utilities")
    def get(self):
        return list_notification_utilities()


@ns.route("/timezone")
class VCOTimezone(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(TimezoneStruct.model(api))
    @ns.doc(shortcut="getVCOTimezone", description="Get VCO timezone")
    def get(self):
        return dict(timezone=get_vco_timezone(vco_id=get_vco_id()))


@ns.route("/audits")
class VCOAuditsLog(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc("listVCOAuditLogs", description="List VCO audit logs")
    @ns.marshal_with(audits_pagination_model)
    @ns.expect(audits_list_argparser)
    def get(self):
        args = audits_list_argparser.parse_args()
        args["vco"] = get_vco_id()
        return pagination_handler_for_audits(AuditLog.list(**args), args["limit"])


@ns.route("/audits/forwarding/status")
class VCOAuditForwardingStatus(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc("getAuditForwardingStatus", description="Get audit forwarding status")
    @ns.marshal_with(audit_forwarding_status_model)
    def get(self):
        return get_audit_log_forwarding_status()


@ns.route("/audits/<audit_id>")
class VCOAudit(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc("getAudit", description="Get audit details")
    @ns.marshal_with(audit_model)
    def get(self, audit_id):
        return AuditLog.get_by_id(audit_id)


@ns.route("/audits/forwarding-config")
class VCOAuditsUploadURL(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc("getAuditForwardingConfig", description="Get audits forwarding to an external system configuration")
    @ns.marshal_with(upload_url_model)
    def get(self):
        return VCO.get_audit_log_forwarding_config(vco_id=get_vco_id())

    @authenticated
    @requires_vco_admin
    @ns.doc("setAuditForwardingConfig", description="Set configuration to upload audits to an external system")
    @ns.expect(upload_url_model, validate=True)
    @ns.marshal_with(success_model)
    def post(self):
        args = ns.payload
        upload_url = args.get("url")
        upload_headers = args.get("headers")
        return vco_business.add_audits_upload_url(
            vco_id=get_vco_id(), upload_url=upload_url, upload_headers=upload_headers
        )

    @authenticated
    @requires_vco_admin
    @ns.doc("deleteAuditForwardingConfig", description="Delete upload audits to an external system configuration")
    @ns.marshal_with(success_model)
    def delete(self):
        return VCO.unset_audit_log_forwarding_config(vco_id=get_vco_id())


@ns.route("/support-text")
class VCOSupportText(Resource):
    @authenticated
    @ns.marshal_with(CustomerSupportTextStruct.model(api))
    @ns.doc(shortcut="getVCOSupportText", description="Get VCO support text")
    def get(self):
        return dict(customer_support_text=VCO.get_support_text(vco_id=get_vco_id()))

    @authenticated
    @requires_vco_admin
    @ns.expect(CustomerSupportTextStruct.model(api))
    @ns.doc(shortcut="setVCOSupportText", description="set VCO support text")
    def put(self):
        support_text = ns.payload["customer_support_text"]
        vco_business.update_vco_support_text(vco_id=get_vco_id(), support_text=support_text)


@ns.route("/vco-root-organization")
class VCORootOrg(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getVcoRootOrg", description="Get vco root organization")
    def get(self):
        return VCO.get_by_id(get_vco_id(), only=["iam_root_organization"]).iam_root_organization


@ns.route("/notifications-blog")
class NotificationsBlogs(Resource):
    @authenticated
    @ns.marshal_with(blog_pagination_model)
    @ns.expect(blog_list_parser)
    @ns.doc(shortcut="listBlogPosts", description="list G8s blog posts")
    def get(self):
        args = blog_list_parser.parse_args()
        limit = args.get("limit")
        start_after = args.get("start_after")
        issuer_id = f"{NotificationIssuer.VCO}:{get_vco_id()}"
        return pagination_handler(NotificationBlog.list, limit=limit, start_after=start_after, issuer_id=issuer_id)


@ns.route("/notifications-blog/<blog_id>")
class NotificationsBlog(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getNotificationBlog", description="Get email notification blog")
    @ns.marshal_with(email_notification_model)
    def get(self, blog_id):
        return NotificationBlog.get_by_id(blog_id)

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="updateNotificationBlog", description="Update email notification blog")
    @ns.expect(email_notification_model)
    def put(self, blog_id):
        return update_blog(blog_id, NotificationConfigStruct(ns.payload).to_mongoengine())

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="deleteNotificationBlog", description="Delete email notification blog")
    def delete(self, blog_id):
        return delete_blog(blog_id)


@ns.route("/license-incompliancy-notifications")
class LicenseIncompliancyForwarding(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getLicenseIncompliancyForwarding", description="Gets license incompliancy forwarding")
    @ns.marshal_with(license_incompliancy_forwarding)
    def get(self):
        return dict(enabled=VCO.get_by_id(get_vco_id()).settings.license_incompliancy_forwarding)

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="editLicenseIncompliancyForwarding", description="Edit license incompliancy forwarding")
    @ns.expect(edit_license_incompliancy_parser)
    def put(self):
        args = edit_license_incompliancy_parser.parse_args()
        vco = VCO.get_by_id(get_vco_id())
        vco.update(settings__license_incompliancy_forwarding=args.get("enabled"))


@ns.route("/emergency-notifications")
class EmergencyNotifications(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(VcoLocationStruct.list_model(field="locations"))
    @ns.doc(
        shortcut="updateVCOEmergencyNotificationSubscription",
        description="Update vco emergency notification subscription",
    )
    def put(self):
        jwt = get_current_user_info().jwt
        update_vco_emergency_notifications_subscription(get_vco_id(), jwt, ns.payload)

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(VcoLocationStruct.model())
    @ns.doc(
        shortcut="getVCOEmergencyNotificationSubscription", description="get vco emergency notification subscription"
    )
    def get(self):
        return get_vco_emergency_notifications_subscription(get_vco_id())


@ns.route("/emergency-contact-info")
class EmergencyNotificationsContactInfo(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(AlertNotificationContactStruct.model(api))
    @ns.doc(
        shortcut="updateVCOEmergencyNotificationContact", description="Update vco emergency notification contact info"
    )
    def put(self):
        jwt = get_current_user_info().jwt
        update_vco_emergency_notifications_contact_info(get_vco_id(), jwt, ns.payload)

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(AlertNotificationContactStruct.model(api))
    @ns.doc(shortcut="getVCOEmergencyNotificationContact", description="get vco emergency notification contact info")
    def get(self):
        return get_vco_emergency_notifications_contact(get_vco_id())


@ns.route("/spla")
class SplaSettings(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(VcoSplaSettingsPostStruct.model(api))
    @ns.doc(shortcut="setSplaSettings", description="Set SPLA settings")
    @ns.marshal_with(success_model)
    def post(self):
        vco_business.set_spla_settings(
            **ns.payload,
            user_id=get_current_user_info().username,
            source_ip_address=request.remote_addr,
            vco_id=get_vco_id(),
        )
        return SUCCESS

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(VcoSplaSettingsStruct.model(api))
    @ns.doc(shortcut="getSplaSettings", description="Get SPLA info")
    def get(self):
        return VCO.get_spla_settings(vco_id=get_vco_id())

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="unsetSPLA", description="Unset SPLA")
    @ns.marshal_with(success_model)
    def delete(self):
        vco_business.unset_spla_settings(get_vco_id())
        return SUCCESS

    @authenticated
    @requires_vco_admin
    @ns.expect(spla_edit_parser)
    @ns.doc(shortcut="editSplaSettings", description="Edit SPLA settings")
    @ns.marshal_with(success_model)
    def put(self):
        args = spla_edit_parser.parse_args()

        vco_business.edit_spla_settings(
            user_id=get_current_user_info().username,
            vco_id=get_vco_id(),
            source_ip_address=request.remote_addr,
            **args,
        )
        return SUCCESS


@ns.route("/show-prices")
class ShowPrices(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(show_prices_parser)
    @ns.doc(shortcut="updateVcoCustomersShowPrices", description="Update VCO customers show prices")
    @ns.marshal_with(success_model)
    def put(self):
        VCO.update_show_prices(get_vco_id(), show_prices_parser.parse_args()["enabled"])
        return SUCCESS

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(ShowPricesStruct.model(api))
    @ns.doc(shortcut="getVcoCustomersShowPrices", description="Get customers show prices")
    def get(self):
        return ShowPricesStruct(enabled=VCO.get_show_prices(vco_id=get_vco_id()))


@ns.route("/oidc-providers")
class OIDCProviders(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="listOIDCProviders", description="List OIDC providers")
    @ns.marshal_with(OIDCProviderStruct.list_model(api))
    def get(self):
        providers = list_oidc_providers(get_vco_id())
        return {"result": providers}

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="createOIDCProvider", description="Create a new OIDC provider")
    @ns.expect(OIDCProviderCreateStruct.model(api))
    @ns.marshal_with(OIDCProviderIdStruct.model(api))
    def post(self):
        provider_struct = OIDCProviderCreateStruct(**ns.payload)
        return create_oidc_provider(get_vco_id(), provider_struct)


@ns.route("/oidc-providers/<provider_id>")
class OIDCProvider(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getOIDCProvider", description="Get an OIDC provider by ID")
    @ns.marshal_with(OIDCProviderStruct.model(api))
    def get(self, provider_id):
        provider = get_oidc_provider(get_vco_id(), provider_id)
        return provider

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="updateOIDCProvider", description="Update an OIDC provider")
    @ns.expect(OIDCProviderCreateStruct.model(api))
    @ns.marshal_with(OIDCProviderStruct.model(api))
    def put(self, provider_id):
        provider_struct = OIDCProviderCreateStruct(**ns.payload)
        return update_oidc_provider(get_vco_id(), provider_id, provider_struct)

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="deleteOIDCProvider", description="Delete an OIDC provider")
    @ns.marshal_with(success_model)
    def delete(self, provider_id):
        delete_oidc_provider(get_vco_id(), provider_id)
        return SUCCESS


@ns.route("/oidc-providers/<provider_id>/activate")
class ActivateOIDCProvider(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="activateOIDCProvider", description="Activate an OIDC provider")
    @ns.marshal_with(success_model)
    def post(self, provider_id):
        activate_oidc_provider(get_vco_id(), provider_id)
        return SUCCESS


@ns.route("/oidc-providers/<provider_id>/deactivate")
class DeactivateOIDCProvider(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="deactivateOIDCProvider", description="Deactivate an OIDC provider")
    @ns.marshal_with(success_model)
    def post(self, provider_id):
        deactivate_oidc_provider(get_vco_id(), provider_id)
        return SUCCESS


@ns.route("/password-login/status")
class PasswordLoginStatus(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getPasswordLoginStatus", description="Get password login status")
    @ns.marshal_with(PasswordLoginStatusStruct.model(api))
    def get(self):
        return get_password_login_status(get_vco_id())


@ns.route("/password-login/enable")
class EnablePasswordLogin(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="enablePasswordLogin", description="Enable password login")
    @ns.marshal_with(success_model)
    def post(self):
        enable_password_login(get_vco_id())
        return SUCCESS


@ns.route("/password-login/disable")
class DisablePasswordLogin(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="disablePasswordLogin", description="Disable password login")
    @ns.marshal_with(success_model)
    def post(self):
        disable_password_login(get_vco_id())
        return SUCCESS


@alpha_ns.route("/admin/dns/top-level-domain/ds-record")
class VCODSRecord(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(DnsDSRecord.model(api))
    @ns.response(200, "DS record")
    @ns.response(404, "Top level domain is not set yet")
    @ns.doc(shortcut="getVCOTopLevelDomainDSRecord", description="Get DS record of top level domain of a VCO ")
    def get(self):
        return get_ds_key(vco_business.get_vco_domain(get_vco_id()).domain)


@alpha_ns.route("/admin/backup-targets")
class BackupTargets(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(BackupTargetsStruct.list_model(api))
    @ns.doc(shortcut="listBackupTargets", description="List backup targets")
    def get(self):
        return {"result": Targets.list(vco_id=get_vco_id())}

    @authenticated
    @requires_vco_admin
    @ns.expect(BackupCreateTargetStruct.model(api))
    @ns.marshal_with(IdModelStruct.model(api))
    @ns.doc(shortcut="createBackupTarget", description="Create a new backup target")
    def post(self):
        target_data = from_dict(BackupCreateTargetStruct, ns.payload)
        return {"id": backup_business.create_target(vco_id=get_vco_id(), data=target_data)}


@alpha_ns.route("/admin/backup-targets/<target_id>")
class BackupTarget(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(BackupTargetStruct.model(api))
    @ns.doc(shortcut="getBackupTarget", description="Get a backup target")
    def get(self, target_id):
        return Targets.get_by_id(_id=target_id)

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteBackupTarget", description="Delete a backup target")
    def delete(self, target_id):
        backup_business.delete_target(target_id)
        return SUCCESS

    @authenticated
    @requires_vco_admin
    @ns.expect(BackupCreateTargetStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="updateBackupTarget", description="Update a backup target")
    def put(self, target_id):
        target_data = from_dict(BackupCreateTargetStruct, ns.payload)
        backup_business.update_target(target_id=target_id, target_data=target_data, vco_id=get_vco_id())
        return SUCCESS


@alpha_ns.route("/admin/backup-targets/<target_id>/repository-overview")
class RepositoryOverview(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(VmFromBackupStruct.list_model(api))
    @ns.doc(shortcut="repositoryOverview", description="Repository overview")
    def get(self, target_id):
        jwt = get_current_user_info().jwt
        return backup_business.list_repository_vms(jwt, target_id)


@alpha_ns.route("/admin/backup-targets/<target_id>/vms/<vm_id>")
class TargetVMs(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteVMBackups", description="Target vm details")
    def delete(self, target_id, vm_id):
        jwt = get_current_user_info().jwt
        return backup_business.delete_vm_backups(jwt, target_id, vm_id=vm_id)


@alpha_ns.route("/admin/backup-targets/<target_id>/vms/<vm_id>/backups")
class TargetVmBackups(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(BackupFullStruct.list_model(api))
    @ns.doc(shortcut="listTargetVMBackups", description="List VM backups")
    def get(self, target_id, vm_id):
        jwt = get_current_user_info().jwt
        return backup_business.list_vm_backups(jwt, target_id, vm_id)


@alpha_ns.route("/admin/backup-targets/<target_id>/subscribe")
class BackupSubscribeTarget(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(backup_location_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="subscribeBackupTarget", description="Subscribe backup target")
    def post(self, target_id):
        args = backup_location_parser.parse_args()
        location = args.get("location")
        cloudspace_id = args.get("cloudspace_id")
        jwt = get_current_user_info().jwt
        backup_business.subscribe_backup_target(jwt, target_id, location, cloudspace_id, None, get_vco_id())
        return SUCCESS


@alpha_ns.route("/admin/backup-targets/<target_id>/unsubscribe")
class BackupUnsubscribeTarget(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(unsubscribe_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="unsubscribeBackupTarget", description="Unsubscribe backup target")
    def delete(self, target_id):
        args = unsubscribe_parser.parse_args()
        location = args.get("location")
        jwt = get_current_user_info().jwt
        backup_business.unsubscribe_backup_target(jwt, target_id, location)
        return SUCCESS


@alpha_ns.route("/admin/backup-targets/<target_id>/sync")
class BackupSyncTarget(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(sync_backup_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="syncBackupTarget", description="Sync backup target")
    def post(self, target_id):
        args = sync_backup_parser.parse_args()
        location = args.get("location")
        jwt = get_current_user_info().jwt
        backup_business.sync_backup_target(jwt, location, target_id)
        return SUCCESS


@alpha_ns.route("/admin/backup-policies")
class BackupPolicies(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(BackupPolicyListStruct.list_model(api))
    @ns.doc(shortcut="listBackupPolicies", description="List backup policies")
    def get(self):
        return {"result": Policies.list(vco_id=get_vco_id())}

    @authenticated
    @requires_vco_admin
    @ns.expect(BackupPolicyStruct.model(api))
    @ns.marshal_with(IdModelStruct.model(api))
    @ns.doc(shortcut="createBackupPolicy", description="Create a new backup Policy")
    def post(self):
        policy_data = from_dict(BackupPolicyStruct, ns.payload)
        jwt = get_current_user_info().jwt
        return {"id": backup_business.create_policy(vco_id=get_vco_id(), policy_data=policy_data, jwt=jwt)}


@alpha_ns.route("/admin/backup-policies/<policy_id>")
class BackupPolicy(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(BackupPoliciesStruct.model(api))
    @ns.doc(shortcut="getBackupPolicy", description="Get a backup policy")
    def get(self, policy_id):
        return Policies.get_by_id(_id=policy_id)

    @authenticated
    @requires_vco_admin
    @ns.expect(BackupPolicyStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="updateBackupPolicy", description="Update a backup policy")
    def put(self, policy_id):
        policy_data = from_dict(BackupPolicyStruct, ns.payload)
        jwt = get_current_user_info().jwt
        backup_business.update_policy(jwt, get_vco_id(), policy_id, policy_data)
        return SUCCESS

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteBackupPolicy", description="Delete a backup policy")
    def delete(self, policy_id):
        backup_business.delete_policy(policy_id)
        return SUCCESS


@alpha_ns.route("/admin/backups")
class Backups(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(list_backups_parser)
    @ns.marshal_with(get_pagination_model("ListBackups", BackupStruct.model(api)))
    @ns.doc(shortcut="listBackups", description="List backups")
    def get(self):
        args = list_backups_parser.parse_args()
        location = args.get("location")
        vm_id = args.get("vm_id")
        policy = args.get("policy")
        cloudspace_id = args.get("cloudspace_id")
        target_id = args.get("target_id")
        status = args.get("status")
        jwt = get_current_user_info().jwt
        return pagination_handler(
            backup_business.list_backups,
            limit=args.get("limit"),
            start_after=args.get("start_after"),
            jwt=jwt,
            location=location,
            vm_id=vm_id,
            policy=policy,
            cloudspace_id=cloudspace_id,
            target_id=target_id,
            status=status,
            exclude_expired=args.get("exclude_expired"),
            sort_direction=args.get("sort_direction"),
            sort_by=args.get("sort_by"),
        )


@alpha_ns.route("/admin/backups/<backup_id>")
class Backup(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(backup_parser)
    @ns.marshal_with(BackupWithVMStruct.model(api))
    @ns.doc(shortcut="getBackup", description="Get a backup")
    def get(self, backup_id):
        args = backup_parser.parse_args()
        location = args.get("location")
        jwt = get_current_user_info().jwt
        return backup_business.get_backup(jwt, location, backup_id)

    @authenticated
    @requires_vco_admin
    @ns.expect(backup_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteBackup", description="Delete a backup")
    def delete(self, backup_id):
        args = backup_parser.parse_args()
        location = args.get("location")
        jwt = get_current_user_info().jwt
        backup_business.delete_backup(jwt, location, backup_id)
        return SUCCESS


@ns.route("/pre-release-features")
class PreReleaseFeatureList(Resource):
    @authenticated
    @ns.doc(shortcut="listPreReleaseFeatures", description="List all pre-release features")
    @ns.marshal_with(PreReleaseFeatureStruct.list_model(api))
    def get(self):
        return {"result": PreReleaseFeatures.list()}
