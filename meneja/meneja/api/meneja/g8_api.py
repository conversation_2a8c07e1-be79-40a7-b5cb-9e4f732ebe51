# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import random
import re

import requests
from flask import Response, json
from flask.globals import request
from flask_itsyouonline import get_current_user_info
from mongoengine.errors import DoesNotExist
from requests_toolbelt.adapters import host_header_ssl

from meneja.business.auth import get_current_scopes, get_organizations, has_scope, is_admin, is_specific_g8_owner
from meneja.business.g8 import get_g8_jwt_from_db
from meneja.lib.enumeration import EnvironmentName
from meneja.lib.itsyouonline import ItsyouOnlineClient
from meneja.model.g8 import G8HardwareSpec, G8Info

_PATH_MATCHER = re.compile(r"/api/1/g8-admin-api/([a-z0-9\-]+){1}(/.*){1}")

_session = requests.Session()
_session.mount("https://", host_header_ssl.HostHeaderSSLAdapter())


def _get_request_options(g8_host, jwt) -> dict:
    request_options = dict(headers={"Authorization": f"bearer {jwt}", "Host": g8_host})
    if g8_host.endswith(".local"):
        request_options["verify"] = False
    return request_options


def _copy_headers(source, target):
    for header in ("Content-Type", "Transfer-Encoding"):
        if header in source:
            target[header] = source[header]


def _do_post_put_patch(method, g8_scheme, g8_host, g8_ip, path, query_string, jwt):
    options = _get_request_options(g8_host, jwt)
    _copy_headers(request.headers, options["headers"])
    g8_response = method(f"{g8_scheme}://{g8_ip}{path}?{query_string}", data=request.data, **options)
    return Response(g8_response.content, status=g8_response.status_code, headers=dict(g8_response.headers))


def _do_get_delete(method, g8_scheme, g8_host, g8_ip, path, query_string, jwt):  # pylint: disable=too-many-arguments
    g8_response = method(f"{g8_scheme}://{g8_ip}{path}?{query_string}", **_get_request_options(g8_host, jwt))
    return Response(g8_response.content, status=g8_response.status_code, headers=dict(g8_response.headers))


def _get(g8_scheme, g8_host, g8_ip, path, query_string, jwt):
    return _do_get_delete(_session.get, g8_scheme, g8_host, g8_ip, path, query_string, jwt)


def _post(g8_scheme, g8_host, g8_ip, path, query_string, jwt):
    return _do_post_put_patch(_session.post, g8_scheme, g8_host, g8_ip, path, query_string, jwt)


def _put(g8_scheme, g8_host, g8_ip, path, query_string, jwt):
    return _do_post_put_patch(_session.put, g8_scheme, g8_host, g8_ip, path, query_string, jwt)


def _delete(g8_scheme, g8_host, g8_ip, path, query_string, jwt):
    return _do_get_delete(_session.delete, g8_scheme, g8_host, g8_ip, path, query_string, jwt)


def _patch(g8_scheme, g8_host, g8_ip, path, query_string, jwt):
    return _do_post_put_patch(_session.patch, g8_scheme, g8_host, g8_ip, path, query_string, jwt)


_METHODS = dict(get=_get, post=_post, put=_put, delete=_delete, patch=_patch)

CALLS_ALLOWED_FOR_CE = {
    r"/api/1/externalnetworks$": ["GET", "POST"],
    r"/api/1/externalnetworks/\d+$": ["GET", "POST", "DELETE"],
    r"/api/1/externalnetworks/\d+/ips$": ["GET", "POST", "DELETE"],
    r"/api/1/externalnetworks/\d+/ips/.+$": ["DELETE"],
    r"/api/1/nodes/status$": ["GET"],
    r"/api/1/gpus$": ["GET", "POST"],
    r"/api/1/gpus/\w+$": ["DELETE"],
    r"/api/1/nodes/available-gpus$": ["GET"],
}


def _is_ce_accessible_api_call(path: str, method: str):
    for pattern, methods in CALLS_ALLOWED_FOR_CE.items():
        if re.match(pattern, path):
            if method in methods:
                return True
            else:
                return False
    return False


def reverse_proxy():
    """G8 API reverse proxy"""
    path: str = request.path
    match = _PATH_MATCHER.match(path)
    if match is None:
        return 404, "Api request not matched!"
    g8_name, path = match.groups()
    g8 = G8Info.get_by_name(g8_name)  # Ensure G8 exists, raises DoesNotExist if not
    if EnvironmentName.current() != EnvironmentName.DEV:
        scope = f"user:memberof:{g8.itsyou_online.app_id}.admin"
    else:
        scope = f"user:memberof:greenitglobe.environments.{g8_name}.admin"
    if not has_scope(scope):
        if _is_ce_accessible_api_call(path, request.method):
            ce_id = G8Info.get_by_name(g8_name, only=["g8_owner_id"]).g8_owner_id
            if is_admin() or is_specific_g8_owner(ce_id, organizations=get_organizations()):
                jwt = get_g8_jwt_from_db(g8_name)
                client = ItsyouOnlineClient(jwt)
                client.refresh_jwt()
                jwt = client.jwt
            else:
                return Response(
                    json.dumps({"message": f"You need to be an admin of cloudenabler {ce_id}"}),
                    status=400,
                    content_type="application/json",
                )
        else:
            content = {
                "message": "No admin scope",
                "type": "AccessDeniedWithoutScope",
                "current_scopes": get_current_scopes(),
                "need_scopes": [scope],
            }
            return Response(json.dumps(content), status=403, content_type="application/json")
    else:
        jwt = get_current_user_info().jwt

    try:
        g8_hw_spec = G8HardwareSpec.get_by_name_cached(g8_name)
        g8_ips = g8_hw_spec.get_meneja_ips_cached(g8_name)
        random.shuffle(g8_ips)
        g8_scheme, g8_host = G8Info.get_url_scheme_and_host(g8_name)
    except (KeyError, DoesNotExist):
        return 404, "G8 not found"
    method: str = _METHODS.get(request.method.lower())
    if method is None:
        return 405, "Method is not allowed"
    query_string: str = request.query_string.decode("utf-8")
    connection_error = None
    for g8_ip in g8_ips:
        try:
            return method(g8_scheme, g8_host, g8_ip, path, query_string, jwt=jwt)
        except (requests.exceptions.ConnectTimeout, requests.exceptions.ConnectionError) as error:
            connection_error = error
    raise connection_error
