# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import re
from dataclasses import dataclass
from functools import partial
from typing import Iterable, List

from requests.exceptions import HTTPError
from werkzeug import exceptions

from meneja.business import get_vco_iam_client
from meneja.business.g8.g8_api import G8Client
from meneja.business.vco.customer import cloudspace
from meneja.lib.enumeration import CloudResourceType
from meneja.lib.itsyouonline import (
    AccessDeniedWithoutScope,
    ItsyouOnlineClient,
    OrganizationAlreadyMember,
    OrganizationNotFound,
)
from meneja.lib.utils import create_new_identifier, decode_validate_base64_id, encode_base64_id, run_for_all_locations
from meneja.model.vco import VCO
from meneja.model.vco.customer import Customer, Location, Role, RolePermissions
from meneja.structs.vco.customer.customer_role_create import CustomerRoleCreateStruct

# TODO:
# - Apply location grant for customer wide roles when location is added to a customer

SUPERADMIN_ROLE_ID = "superadmin_1"
get_superadmin = partial(
    Role,
    name="Superadmin",
    role_id=SUPERADMIN_ROLE_ID,
    description="""Superadmin role grants full set of permissions to all customer resources.
                   Owners of the role's IAM organization have access to the role management.""",
    permissions=RolePermissions(read=True, create=True, update=True, delete=True, execute=True),
)


def list_roles(customer_id: str) -> Iterable:
    """Return list of customer roles including superadmin role

    Args:
        customer_id (str): Customer ID

    Returns:
        Iterable: List of roles
    """
    customer = Customer.get_by_id(customer_id)
    roles = customer.roles
    superadmin = get_superadmin(iam_organization=customer.organization)
    roles.append(superadmin)
    return roles


def get_role(customer_id: str, role_id: str) -> Role:
    """Get role info

    Args:
        customer_id (str): Customer ID
        role_id (str): Role ID

    Returns:
        Role: Role info
    """
    if role_id == SUPERADMIN_ROLE_ID:
        organization = Customer.get_organization(customer_id)
        return get_superadmin(iam_organization=organization)
    return Customer.get_role(customer_id, role_id)


def create_role(jwt: str, customer_id: str, role: CustomerRoleCreateStruct) -> Role:
    """Create customer role

    Args:
        jwt (str): Customer admin JWT Token
        customer_id (str): customer_id
        role (CustomerRoleCreateStruct): Role details

    Returns:
        Role
    """
    # validate input
    if not customer_id:
        raise ValueError("Customer id is a required value!")
    customer = Customer.get_by_id(customer_id=customer_id)
    if not role.name:
        raise ValueError("Role name is a required value!")
    if role.permissions is None:
        raise ValueError("The permissions are required values!")
    permission_values = {
        role.permissions.create,
        role.permissions.delete,
        role.permissions.execute,
        role.permissions.read,
    }
    if not permission_values.issubset({True, False}):
        raise ValueError("All permissions need to be explicitly set!")
    customer_roles = list_roles(customer_id)
    for role_item in customer_roles:
        if role_item.name == role.name.strip():
            raise ValueError(f"A role with name {role.name} already exists!")

    # create an organization in iam
    iam_user_client = ItsyouOnlineClient(jwt=jwt)
    iam_vco_client = get_vco_iam_client(customer.vco)

    # create organization inventory
    tech_tree = iam_vco_client.get_tree(f"{customer.technical_organization}")
    user_tree = iam_user_client.get_tree(f"{customer.organization}")

    def org_exists(tree: dict, org: str):
        globalid = tree["globalid"]
        if org == globalid:
            return True
        if not org.startswith(globalid):
            return False
        for child in tree["children"]:
            if org_exists(child, org):
                return True
        return False

    # Create roles sub organization if necessary
    if not org_exists(user_tree, f"{customer.organization}.roles"):
        iam_user_client.create_suborganization(f"{customer.organization}.roles")
    if not org_exists(tech_tree, f"{customer.technical_organization}.roles"):
        iam_vco_client.create_suborganization(f"{customer.technical_organization}.roles")

    # Create a new unique role_id
    def roleid_exists(role_id):
        if org_exists(tech_tree, f"{customer.technical_organization}.roles.{role_id}"):
            return True
        if org_exists(user_tree, f"{customer.organization}.roles.{role_id}"):
            return True
        return False

    role_id = create_new_identifier(role.name, roleid_exists)

    # Create the role in iam
    iam_vco_client.create_suborganization(f"{customer.technical_organization}.roles.{role_id}")
    iam_user_client.create_suborganization(f"{customer.organization}.roles.{role_id}")
    iam_vco_client.add_orgmember(
        f"{customer.technical_organization}.roles.{role_id}", f"{customer.organization}.roles.{role_id}"
    )

    # Create the role in the customer object
    dbrole = role.to_mongoengine()
    dbrole.iam_organization = f"{customer.organization}.roles.{role_id}"
    dbrole.tech_iam_organization = f"{customer.technical_organization}.roles.{role_id}"
    dbrole.role_id = role_id
    customer.roles.append(dbrole)
    customer.save()

    return dbrole


def grant_revoke_role(
    jwt: str,
    customer_id: str,
    role_id: str,
    grant_action: bool,
    resource_type: CloudResourceType = None,
    resource_id: str = None,
) -> None:
    """Grants or revokes role access to a cloud resource role

    Args:
        customer_id (str): Customer id
        role_id (str): Role Id
        resource_type (str): cloud resource type
        resource_id (str): cloud resource id
        grant (bool): Grants access if True else Revokes access
    """

    def get_location(resource_id):
        for location in customer.locations:
            if location.location == resource_id:
                break
        else:
            raise KeyError(f"Location {resource_id} was not found!")
        return location

    if role_id == SUPERADMIN_ROLE_ID:
        raise ValueError("Superadmin role already grants access to all customer resources")
    if not resource_type:
        raise ValueError("Resource type is a required value!")
    if not customer_id:
        raise ValueError("Customer id is a required value!")
    if resource_type != CloudResourceType.CUSTOMER and not resource_id:
        raise ValueError(f"Resource ID is a required value for resource type {resource_type}!")
    customer = Customer.get_by_id(customer_id=customer_id)
    role: Role = None
    for role in customer.roles:
        if role.role_id == role_id:
            break
    else:
        raise KeyError(f"Role {role_id} was not found!")
    location: Location = None
    if resource_type == CloudResourceType.CUSTOMER:
        if resource_id:
            raise ValueError("Should not pass resource_id when granting a customer wide role!")
    elif resource_type == CloudResourceType.LOCATION:
        location = get_location(resource_id)
    elif resource_type == CloudResourceType.CLOUDSPACE:
        location_name, cloudspace_id = cloudspace.decode_validate_cloudspace_id(resource_id)
        location = get_location(location_name)
    elif resource_type == CloudResourceType.OBJECTSPACE:
        location_name, objectspace_id = decode_validate_base64_id(resource_id)
        location = get_location(location_name)
    elif resource_type == CloudResourceType.DISK:
        try:
            location_name, disk_id = str(resource_id).split(":", 1)
        except ValueError as exc:
            raise ValueError(f"Invalid disk resource_id passed: {resource_id}!") from exc
        try:
            disk_id = int(disk_id)
        except ValueError as exc:
            raise ValueError("Expected integer value for disk_id") from exc
        location = get_location(location_name)
    elif resource_type == CloudResourceType.VIRTUAL_MACHINE:
        try:
            cloudspace_id, virtual_machine_id = str(resource_id).split(":", 1)
        except ValueError as exc:
            raise ValueError(f"Invalid virtual machine resource_id passed: {resource_id}!") from exc
        try:
            virtual_machine_id = int(virtual_machine_id)
        except ValueError as exc:
            raise ValueError("Expected integer value for machine_id") from exc
        location_name, _ = cloudspace.decode_validate_cloudspace_id(cloudspace_id)
        location = get_location(location_name)
    elif resource_type in (CloudResourceType.OBJECTSPACE, CloudResourceType.DISK):
        raise NotImplementedError(
            f"""Resource type {resource_type} is not supported.
            Supported resource types: {CloudResourceType.values()}"""
        )

    # Apply grant
    if grant_action:
        if resource_type == CloudResourceType.CUSTOMER:
            _grant_role_to_all_customer_resources(jwt, customer, role)
        elif resource_type == CloudResourceType.LOCATION:
            _grant_role_to_customer_location(jwt, role, location)
        elif resource_type == CloudResourceType.CLOUDSPACE:
            _grant_role_to_customer_cloudspace(jwt, role, location, cloudspace_id)
        elif resource_type == CloudResourceType.OBJECTSPACE:
            _grant_role_to_customer_objectspace(jwt, role, location, objectspace_id)
        elif resource_type == CloudResourceType.VIRTUAL_MACHINE:
            _grant_role_to_customer_virtual_machine(jwt, role, location, virtual_machine_id)
        elif resource_type == CloudResourceType.DISK:
            _grant_role_to_customer_disk(jwt, role, location, disk_id)
        else:
            raise NotImplementedError(
                f"""Resource type {resource_type} is not supported.
                Supported resource types: {CloudResourceType.values()}"""
            )
    else:
        if resource_type == CloudResourceType.CUSTOMER:
            _revoke_role_to_all_customer_resources(jwt, customer, role)
        elif resource_type == CloudResourceType.LOCATION:
            _revoke_role_to_customer_location(jwt, role, location)
        elif resource_type == CloudResourceType.CLOUDSPACE:
            _revoke_role_to_customer_cloudspace(jwt, role, location, cloudspace_id)
        elif resource_type == CloudResourceType.VIRTUAL_MACHINE:
            _revoke_role_to_customer_virtual_machine(jwt, role, location, virtual_machine_id)
        elif resource_type == CloudResourceType.DISK:
            _revoke_role_to_customer_disk(jwt, role, location, disk_id)
        elif resource_type == CloudResourceType.OBJECTSPACE:
            _revoke_role_to_customer_objectspace(jwt, role, location, objectspace_id)
        else:
            raise NotImplementedError(
                f"""Resource type {resource_type} is not supported.
                    Supported resource types: {CloudResourceType.values()}"""
            )


def _grant_role_to_all_customer_resources(jwt: str, customer: Customer, role: Role):
    for location in customer.locations:
        try:
            _grant_role_to_customer_location(jwt, role, location)
        except Exception as exc:
            # pylint: disable-next=no-member
            if exc.response.status_code == 400 and str(exc.response.content).find(
                "Group already has access rights to this account"
            ):
                continue
            raise
    role.customer_wide = True
    customer.save()


def _revoke_role_to_all_customer_resources(jwt: str, customer: Customer, role: Role):
    for location in customer.locations:
        _revoke_role_to_customer_location(jwt, role, location)
    role.customer_wide = False
    customer.save()


def _grant_role_to_customer_location(jwt: str, role: Role, location: Location):
    g8_client = G8Client(location.location, jwt)
    g8_client.grant_role_to_account(
        location.g8_account_id, role.permissions.to_g8_accesstype(), role.tech_iam_organization
    )


def _revoke_role_to_customer_location(jwt: str, role: Role, location: Location):
    g8_client = G8Client(location.location, jwt)
    g8_client.revoke_role_from_account(location.g8_account_id, role.tech_iam_organization)


def _grant_role_to_customer_cloudspace(jwt: str, role: Role, location: Location, cloudspace_id: int):
    g8_client = G8Client(location.location, jwt)
    g8_client.grant_role_to_cloudspace(cloudspace_id, role.permissions.to_g8_accesstype(), role.tech_iam_organization)


def _revoke_role_to_customer_cloudspace(jwt: str, role: Role, location: Location, cloudspace_id: int):
    g8_client = G8Client(location.location, jwt)
    g8_client.revoke_role_from_cloudspace(cloudspace_id, role.tech_iam_organization)


def _grant_role_to_customer_virtual_machine(jwt: str, role: Role, location: Location, virtual_machine_id):
    g8_client = G8Client(location.location, jwt)
    g8_client.grant_role_to_virtual_machine(
        virtual_machine_id, role.permissions.to_g8_accesstype(), role.tech_iam_organization
    )


def _revoke_role_to_customer_virtual_machine(jwt: str, role: Role, location: Location, virtual_machine_id):
    g8_client = G8Client(location.location, jwt)
    g8_client.revoke_role_from_machine(virtual_machine_id, role.tech_iam_organization)


def _grant_role_to_customer_objectspace(jwt: str, role: Role, location: Location, objectspace_id: int):
    g8_client = G8Client(location.location, jwt)
    g8_client.grant_role_to_objectspace(objectspace_id, role.permissions.to_g8_accesstype(), role.tech_iam_organization)


def _revoke_role_to_customer_objectspace(jwt: str, role: Role, location: Location, objectspace_id: int):
    g8_client = G8Client(location.location, jwt)
    g8_client.revoke_role_from_objectspace(objectspace_id, role.tech_iam_organization)


def _grant_role_to_customer_disk(jwt: str, role: Role, location: Location, disk_id: int):
    g8_client = G8Client(location.location, jwt)
    g8_client.grant_role_to_disk(disk_id, role.permissions.to_g8_accesstype(), role.tech_iam_organization)


def _revoke_role_to_customer_disk(jwt: str, role: Role, location: Location, disk_id: int):
    g8_client = G8Client(location.location, jwt)
    g8_client.revoke_role_from_disk(disk_id, role.tech_iam_organization)


# def _grant_role_to_customer_vdi_profile(role: Role, profile_id: str):
#     vdi_profile = VDI.get_profile_by_id(profile_id=profile_id)
#     vdi_profile.roles.append(role.permissions, role.tech_iam_organization)


def _revoke_role_to_customer_vdi_profile(jwt: str, role: Role, location: Location, virtual_machine_id):
    g8_client = G8Client(location.location, jwt)
    g8_client.revoke_role_from_machine(virtual_machine_id, role.tech_iam_organization)


@dataclass
class Resource:
    """Represents a resource that has been granted access to a role.

    Attributes:
        resource_type (str): The type of resource, e.g. "cloudspace", "vm", etc.
        resource_id (str): The unique ID of the resource.
        resource_name (str): A human-friendly name for the resource.
    """

    resource_type: str
    resource_id: str
    resource_name: str


def get_role_grants(jwt: str, customer_id: str, role_id: str) -> List[Resource]:
    """Gets role grants to resources

    Args:
        jwt (str): User JWT
        customer_id (str): Customer ID
        role_id (str): Role ID

    Returns:
        List[Resource]
    """
    # Validate input
    if not customer_id:
        raise ValueError("Customer id is a required value!")
    if role_id == SUPERADMIN_ROLE_ID:
        return list(), list()
    customer = Customer.get_by_id(customer_id=customer_id)
    role: Role = None
    for role in customer.roles:
        if role.role_id == role_id:
            break
    else:
        raise KeyError(f"Role {role_id} was not found!")

    # Query all G8's & build result
    result = list()
    if role.customer_wide:
        resource = Resource(CloudResourceType.CUSTOMER.value, None, None)
        result.append(resource)

    @run_for_all_locations
    # pylint: disable-next=unused-argument
    def _get_location_grants(location, **kwargs):
        grants = []
        g8c = G8Client(location.location, jwt)
        location_resources = g8c.list_role_grants(role.tech_iam_organization)
        for g8_resource in location_resources:
            if g8_resource["resource_type"] == "account" and role.customer_wide:
                continue
            elif g8_resource["resource_type"] == "account":
                resource_id = location.location
                resource_type = CloudResourceType.LOCATION.value
                resource_name = location.location
            elif g8_resource["resource_type"] == "cloudspace":
                resource_id = encode_base64_id(g8_resource["resource_id"], location.location)
                resource_type = CloudResourceType.CLOUDSPACE.value
                resource_name = g8_resource["resource_name"]
            elif g8_resource["resource_type"] == "objectspace":
                resource_id = encode_base64_id(g8_resource["resource_id"], location.location)
                resource_type = CloudResourceType.OBJECTSPACE.value
                resource_name = g8_resource["resource_name"]
            elif g8_resource["resource_type"] == "vmachine":
                vm_info = g8c.get_vm_info(g8_resource["resource_id"])
                cloudspace_id = encode_base64_id(vm_info["cloudspace_id"], location.location)
                resource_id = f"{cloudspace_id}:{g8_resource['resource_id']}"
                resource_type = CloudResourceType.VIRTUAL_MACHINE.value
                resource_name = g8_resource["resource_name"]
            elif g8_resource["resource_type"] == "disk":
                resource_id = f"{location.location}:{g8_resource['resource_id']}"
                resource_type = CloudResourceType.DISK.value
                resource_name = g8_resource["resource_name"]
            else:
                raise NotImplementedError()
            resource = Resource(resource_type, resource_id, resource_name)
            grants.append(resource)
        return grants

    # pylint: disable-next=no-value-for-parameter, unbalanced-tuple-unpacking
    location_statuses, grants = _get_location_grants(customer_id=customer_id)
    result.extend(grants)
    return location_statuses, result


def delete_role(jwt: str, customer_id: str, role_id: str) -> None:
    """Deletes a role

    Args:
        jwt (str): JWT
        customer_id (str): Customer ID
        role_id (str): Role ID
    """
    # Validate input
    if not customer_id:
        raise ValueError("Customer id is a required value!")
    if role_id == SUPERADMIN_ROLE_ID:
        raise ValueError("You cannot delete superadmin role")

    # Check if any service accounts are assigned to this role
    from meneja.business.vco.customer.service_accounts import get_service_accounts_by_role

    assigned_service_accounts = get_service_accounts_by_role(customer_id, role_id)
    if assigned_service_accounts:
        service_account_names = [sa.name for sa in assigned_service_accounts]
        raise exceptions.BadRequest(
            f"Cannot delete role '{role_id}'. It is assigned to {len(assigned_service_accounts)} "
            f"service account(s): {', '.join(service_account_names)}. "
            f"Please remove the role from all service accounts before deleting it."
        )

    customer = Customer.get_by_id(customer_id=customer_id)
    role: Role = None
    for role in customer.roles:
        if role.role_id == role_id:
            break
    else:
        raise KeyError(f"Role {role_id} was not found!")

    # Revoke access on the g8s
    location: Location = None
    for location in customer.locations:
        g8c = G8Client(location.location, jwt)
        try:
            g8c.revoke_role_from_account(location.g8_account_id, role.tech_iam_organization)
        except HTTPError as exc:
            if exc.response.status_code != 404:
                raise

    # Delete the role from the database
    customer.roles.remove(role)
    customer.save()

    # Delete the role's technical organization
    iam_vco_client = get_vco_iam_client(customer.vco)
    iam_vco_client.delete_organization(name=role.tech_iam_organization)


def update_role(jwt: str, customer_id: str, role_id: str, role: CustomerRoleCreateStruct) -> None:
    """Updates the role

    Args:
        jwt (str): JWT
        customer_id (str): Customer ID
        role_id (str): Role ID
        role (CustomerRoleCreateStruct): Role settings
    """
    # Validate input
    if not customer_id:
        raise ValueError("Customer id is a required value!")
    if role_id == SUPERADMIN_ROLE_ID:
        raise ValueError("You cannot change superadmin role")
    customer = Customer.get_by_id(customer_id=customer_id)
    dbrole: Role = None
    for dbrole in customer.roles:
        if dbrole.role_id == role_id:
            break
    else:
        raise KeyError(f"Role {role_id} was not found!")
    rpm = role.permissions
    dbrpm: RolePermissions = dbrole.permissions

    # Update in mongo-db
    dbrole.name = role.name
    dbrole.description = role.description
    dbrpm.create = rpm.create
    dbrpm.update = rpm.update
    dbrpm.delete = rpm.delete
    dbrpm.execute = rpm.execute
    dbrpm.read = rpm.read
    customer.save()

    # Update in the g8s
    location: Location = None
    for location in customer.locations:
        g8c = G8Client(location.location, jwt)
        location_resources = g8c.list_role_grants(dbrole.tech_iam_organization)
        accounts, css, vms, objectspaces, disks = [], [], [], [], []
        for g8_resource in location_resources:
            if g8_resource["resource_type"] == "account":
                accounts.append(g8_resource["resource_id"])
            elif g8_resource["resource_type"] == "cloudspace":
                css.append(g8_resource["resource_id"])
            elif g8_resource["resource_type"] == "vmachine":
                vms.append(g8_resource["resource_id"])
            elif g8_resource["resource_type"] == "objectspace":
                objectspaces.append(g8_resource["resource_id"])
            elif g8_resource["resource_type"] == "disk":
                disks.append(g8_resource["resource_id"])
            else:
                raise NotImplementedError(f"Resource {g8_resource['resource_type']} is not supported")

        g8_access_type = dbrpm.to_g8_accesstype()
        for account in accounts:
            g8c.update_role_grant_on_account(account, g8_access_type, dbrole.tech_iam_organization)
        for cs in css:
            g8c.update_role_grant_on_cloudspace(cs, g8_access_type, dbrole.tech_iam_organization)
        for vm in vms:
            g8c.update_role_grant_on_virtual_machine(vm, g8_access_type, dbrole.tech_iam_organization)
        for objectspace in objectspaces:
            g8c.update_role_grant_on_objectspace(objectspace, g8_access_type, dbrole.tech_iam_organization)
        for disk in disks:
            g8c.update_role_grant_on_disk(disk, g8_access_type, dbrole.tech_iam_organization)


def list_roles_on_customer(customer_id: str) -> Iterable[str]:
    """List roles with access to this customer

    Args:
        location (str): Location code
        account_id (str): Account ID

    Returns:
        Iterable[str]: List of role IDs
    """
    customer = Customer.get_by_id(customer_id=customer_id)
    return [role for role in customer.roles if role.customer_wide]


def list_roles_on_location(customer_id: str, location: str, account_id: str, jwt: str) -> Iterable[str]:
    """List roles with access to this accout

    Args:
        location (str): Location code
        account_id (str): Account ID
        jwt (str): User's JWT

    Returns:
        Iterable[str]: List of role IDs
    """
    users = G8Client(location, jwt=jwt).list_account_users(account_id=account_id)
    return _get_role_list_from_acl(customer_id, users["acl"])


def list_roles_on_cloudspace(customer_id: str, location: str, cloudspace_id: str, jwt: str) -> Iterable[str]:
    """List roles with access to this CS

    Args:
        location (str): Location code
        cloudspace_id (str): CS ID
        jwt (str): User's JWT

    Returns:
        Iterable[str]: List of role IDs
    """
    users = G8Client(location, jwt=jwt).list_cloudspace_users(cloudspace_id=cloudspace_id)
    return _get_role_list_from_acl(customer_id, users["cloudspace_acl"])


def list_roles_on_objectspace(customer_id: str, objectspace_id: str, jwt: str) -> Iterable[str]:
    """List roles with access to this OS

    Args:
        location (str): Location code
        objectspace_id (str): Objectspace ID
        jwt (str): User's JWT

    Returns:
        Iterable[str]: List of role IDs
    """
    location, objectspace_id_decoded = decode_validate_base64_id(objectspace_id)
    users = G8Client(location, jwt=jwt).list_objectspace_users(objectspace_id=objectspace_id_decoded)
    return _get_role_list_from_acl(customer_id, users["objectspace_acl"])


def list_roles_on_vm(customer_id: str, location: str, vm_id: str, jwt: str) -> Iterable[str]:
    """List roles with access to this VM

    Args:
        location (str): Location code
        vm_id (str): VM ID
        jwt (str): User's JWT

    Returns:
        Iterable[str]: List of role IDs
    """
    users = G8Client(location, jwt=jwt).list_vm_users(vm_id=vm_id)
    return _get_role_list_from_acl(customer_id, users["vm_acl"])


def list_roles_on_disk(customer_id: str, location: str, disk_id: str, jwt: str) -> Iterable[str]:
    """List roles with access to this CS

    Args:
        location (str): Location code
        disk_id (str): Disk ID
        jwt (str): User's JWT

    Returns:
        Iterable[str]: List of role IDs
    """
    users = G8Client(location, jwt=jwt).list_disk_users(disk_id=disk_id)
    return _get_role_list_from_acl(customer_id, users["disk_acl"])


def _get_role_list_from_acl(customer_id: str, access_list: list):
    customer = Customer.get_by_id(customer_id=customer_id)
    roles = []
    pattern = re.compile(f"{customer.technical_organization}.roles.*$")
    for user in access_list:
        if user["explicit"]:
            role_org = re.findall(pattern, user["username"])
            if role_org:
                role = customer.get_role_by_tech_organization(role_org[0])
                roles.append(role)
    return roles


def check_support_status(customer_id: str):
    """Check if support organization is a member of customer organization

    Args:
        customer_id (str): customer id

    Returns:
        bool: is support organization a member of customer organization
    """
    customer = Customer.get_by_id(customer_id=customer_id)
    return customer.support_access


def add_support_organization(jwt: str, customer_id: str):
    """add support organization as a member of customer organization

    Args:
        jwt (str):  Customer admin JWT Token
        customer_id (str): customer id

    """
    customer = Customer.get_by_id(customer_id=customer_id)

    if customer.support_access:
        raise exceptions.Conflict(f"support organization already a member of {customer.organization} organization")

    iam_user_client = ItsyouOnlineClient(jwt=jwt)
    vco = VCO.get_by_id(customer.vco)

    org_name = f"{vco.iam_root_organization}.staff.support"

    try:
        iam_user_client.add_orgmember(customer.organization, org_name, accept_invite=True)
    except HTTPError as err:
        if err.response.status_code == 403:
            raise AccessDeniedWithoutScope(
                f"The user:ownerof:organization scope for organization {customer.organization} is needed"
                "to continue the operation!",
                f"user:ownerof:organization:{customer.organization}",
            ) from err
        raise
    except OrganizationAlreadyMember:
        pass

    customer.support_access = True
    customer.save()


def delete_support_organization(jwt: str, customer_id: str):
    """delete support organization from customer organization

    Args:
        jwt (str): Customer admin JWT Token
        customer_id (str): customer id

    """
    customer = Customer.get_by_id(customer_id=customer_id)

    if not customer.support_access:
        raise exceptions.Conflict(f"support organization is not a member of {customer.organization} organization")

    iam_user_client = ItsyouOnlineClient(jwt=jwt)
    vco = VCO.get_by_id(customer.vco)

    org_name = f"{vco.iam_root_organization}.staff.support"
    try:
        iam_user_client.delete_orgmember(customer.organization, org_name)
    except OrganizationNotFound:
        pass

    customer.support_access = False
    customer.save()
