# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import base64
import ipaddress
import logging
import os
import re
import time
import uuid
from dataclasses import asdict
from typing import List, Optional

import gevent
from dynaqueue.client.client import Client
from dynaqueue.models.task import Task
from dynaqueue.scheduler import schedule
from gevent.pool import Pool
from jinja2 import Template

# from dynaqueue.scheduler import schedule
from pymongo import ReturnDocument
from werkzeug.exceptions import BadRequest, NotFound

from meneja.business import backups as backup_business
from meneja.business.g8.g8 import get_g8_jwt_from_db
from meneja.business.g8.g8_api import G8Client
from meneja.business.service_auth.decorators import with_service_account_jwt
from meneja.business.vco.customer import cloudspace as cs_business
from meneja.business.vco.customer import vms as vm_business
from meneja.business.vco.customer.cloudspace import (
    add_wireguard_interface,
    add_wireguard_peer,
    decode_validate_cloudspace_id,
    delete_remote_wireguard_connection,
    get_remote_wireguard_interface,
)
from meneja.business.vco.customer.vms import execute_command, get_vm_info
from meneja.business.vco.iaas.virtual_machine import delete_virtual_machine
from meneja.jobs import job
from meneja.lib.connection import DynaqueueConnection, MinioConnection, MinioQaDevConnection
from meneja.lib.enumeration import (
    AgentOS,
    AvailabilityEnum,
    EnvironmentName,
    InstanceBehaviorEnum,
    RDPStatus,
    ServiceAccountResourceType,
    VDIPofileStatus,
)
from meneja.model.vco.customer import Customer
from meneja.model.vco.vdi import (
    SessionWireguardConfig,
    SessionWireguardPeer,
    VDIProfile,
    VDISession,
    VDIStandbyVM,
    VDIUser,
    VMConfig,
)
from meneja.structs.vco.dataclasses.vdi import VDIProfileCreateStruct, VDIProfileUpdateStruct

logger = logging.getLogger()
TEMPLATES_PATH = os.path.join(os.path.dirname(__file__), "templates")

STATUS_REPORTER_CONFIG_PATH = os.path.join(TEMPLATES_PATH, "status_reporter_config.ps1.j2")


def get_g8_account_id(customer_id: str, location: str) -> str:
    """Get G8 Account ID

    Args:
        customer_id (str): Customer ID
        location (str): Location

    Returns:
        str: G8 Account ID
    """
    customer: Customer = Customer.get_by_id(customer_id)
    for customer_location in customer.locations:
        if customer_location.location == location:
            return customer_location.g8_account_id
    raise NotFound(f"Customer {customer_id} does not have access to location {location}")


def _normalize_profile_name(name: str) -> str:
    """Normalize profile name to be used in profile_id"""
    name = name.lower().strip()
    name = re.sub(r"[^\w\s-]", "", name)
    name = re.sub(r"[\s\-]+", "_", name)
    return name


def _claim_one_standby_vm_atomic(customer_id: str, profile_id: str):
    """
    Returns the claimed standby VM subdoc as a dict (vm_id, vm_name, rdp_host, status, created_at, ...),
    or None if no standby is available.
    Atomically sets that VM's status to "in-use".
    """
    coll = VDIProfile._get_collection()  # pylint: disable=protected-access  # raw PyMongo collection

    # We use ReturnDocument.BEFORE so the projection still matches `status: "standby"`
    # and returns the subdocument we claimed, even though the update flips it to "in-use".
    doc_before = coll.find_one_and_update(
        {
            "customer_id": customer_id,
            "profile_id": profile_id,
            "vms.status": "standby",
        },
        {
            "$set": {"vms.$.status": "in-use"},
        },
        projection={
            "_id": 0,
            # Return ONLY the matched standby element to avoid fetching a big array
            "vms": {"$elemMatch": {"status": "standby"}},
        },
        return_document=ReturnDocument.BEFORE,
    )

    if not doc_before or not doc_before.get("vms"):
        return None

    # The element we flipped is the one the projection returned
    return doc_before["vms"][0]


def _create_vm(
    profile_id: str,
    customer_id: str,
    location: str,
    cloudspace_id: str,
    jwt: str,
    vco_id: str,
) -> dict:
    """Create a standby VM and persist it in the profile."""
    profile = VDIProfile.get_by_id(profile_id=profile_id, customer_id=customer_id)
    vm_name = f"{_normalize_profile_name(profile.name)}-{uuid.uuid4().hex[:8]}"
    logger.info(
        "Creating VM for customer: %s profile %s: name=%s at VCO: %s", customer_id, profile_id, vm_name, profile.vco
    )
    vm = cs_business.create_vm(
        name=vm_name,
        account_id=get_g8_account_id(customer_id, location),
        location=location,
        cloudspace_id=cloudspace_id,
        customer_id=customer_id,
        memory=profile.vm_config.memory,
        vcpus=profile.vm_config.vcpus,
        disk_size=profile.vm_config.bootdisk,
        user_data=profile.vm_config.cloud_init_template,
        description="A virtual desktop instance",
        image_id=profile.vm_config.image_id,
        gpu_id=profile.vm_config.vgpu_profile,
        start_vm=False,
        vco_id=vco_id,
        jwt=jwt,
    )
    if "vm_id" not in vm:
        raise RuntimeError("create_vm did not return vm_id")
    logger.info("Created VM vm_id=%s name=%s", vm["vm_id"], vm_name)
    logger.info("Checking for assigned policies...")
    if profile.backup_policy_id:
        logger.info(
            "Backup policy found in the profile. Applying backup policy %s to VM vm_id=%s name=%s",
            profile.backup_policy_id,
            vm["vm_id"],
            vm_name,
        )
        backup_business.assign_vm_backup_policy(
            location=location,
            jwt=jwt,
            vm_id=vm["vm_id"],
            policy_id=profile.backup_policy_id,
        )
        logger.info("Applied backup policy %s to VM vm_id=%s name=%s", profile.backup_policy_id, vm["vm_id"], vm_name)
    else:
        logger.info("No backup policy found in the profile.")

    logger.info("Waiting for VM vm_id=%s to start", vm["vm_id"])
    vm_business.start_vm(location=location, jwt=jwt, vm_id=vm["vm_id"])
    logger.info("VM vm_id=%s started successfully!", vm["vm_id"])
    # Persist the VM into the profile now (avoid cross-task result passing)
    logger.info("Assigning VM vm_id=%s to profile info", vm["vm_id"])
    _fetch_vm_info_and_persist(
        profile_id=profile_id,
        customer_id=customer_id,
        location=location,
        jwt=jwt,
        vm_id=vm["vm_id"],
    )
    logger.info("Iniitiating deploy status report config at vm_id=%s", vm["vm_id"])
    deploy_status_reporter_config(customer_id, vm["vm_id"], profile_id)


def _log_init_pool_build(profile_id: str, customer_id: str, to_create: int) -> None:
    logger.info(
        "Starting standby pool build for profile=%s, customer=%s, to_create=%s",
        profile_id,
        customer_id,
        to_create,
    )


def _fetch_vm_info_and_persist(profile_id: str, customer_id: str, location: str, jwt: str, vm_id: int) -> None:
    """Fetch VM info and persist minimal data into the VDI profile vms."""
    vm_info = get_vm_info(location=location, jwt=jwt, vm_id=vm_id)

    rdp_host = ""
    try:
        nics = vm_info.get("network_interfaces") or []
        if isinstance(nics, list) and nics:
            first = nics[0] or {}
            rdp_host = first.get("ip_address") or ""
    except (KeyError, IndexError, TypeError, AttributeError, ValueError):
        rdp_host = ""

    entry = VDIStandbyVM(vm_id=int(vm_id), vm_name=str(vm_info["name"]), rdp_host=str(rdp_host), status="standby")

    prof = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    if not any(v.vm_id == int(vm_id) for v in (prof.vms or [])):
        prof.vms.append(entry)
        prof.updated_at = int(time.time())
        prof.save()

    logger.info(
        "Persisted VM to profile=%s: vm_id=%s, name=%s, rdp_host=%s",
        profile_id,
        vm_id,
        vm_info.get("name"),
        rdp_host,
    )


def enqueue_build_standby_pool(
    profile_id: str,
    customer_id: str,
    location: str,
    cloudspace_id: str,
    jwt: str,
    vco_id: str,
) -> str:
    """Enqueue tasks to build the VDI standby pool."""
    profile: VDIProfile = VDIProfile.get_by_id(profile_id=profile_id, customer_id=customer_id)
    target = int(profile.standby_pool_size or 0)
    existing = len(profile.vms or [])
    to_create = max(0, target - existing)

    pool: Pool = Pool(size=to_create)
    for _ in range(to_create):
        pool.spawn(_create_vm, profile_id, customer_id, location, cloudspace_id, jwt, vco_id)
    pool.join(raise_error=True)


def _validate_vdi_profile(payload: VDIProfileCreateStruct):
    """validate VDI profile inputs"""
    if payload.standby_pool_size < 1:
        raise ValueError("standby pool size must be at least 1.")

    if (payload.recycle_time_seconds < 300 or payload.recycle_time_seconds >= 86400 * 7) and (
        payload.instance_behavior == InstanceBehaviorEnum.SINGLE_USE
        or payload.availability == AvailabilityEnum.SHUTDOWN
    ):
        raise ValueError("recycle time must be between 300 (5 mins) and 604800 (7 days).")


def create_vdi_profile(customer_id: str, payload: VDIProfileCreateStruct, vco_id: str) -> dict:
    """Create VDI profile and enqueue standby pool creation.

    Args:
        customer_id (str): Customer ID
        payload (VDIProfileCreateStruct): Payload
        {
            "name": "string",
            "vm_config": {
                "memory": int,  # Memory in MB
                "vcpus": int,  # Number of virtual CPUs
                "bootdisk": int,  # Boot disk size in GB
                "vgpu_profile": "string",  # Virtual GPU profile name (e.g., "nvidia-t4")
                "cloud_init_template": "user data template name",
                "image_id": "string"
            },
            wireguard_config: {
                "name": "string",
                "address": "string",  # WireGuard interface address
                "port": int,  # WireGuard port
                "public_key": "string",  # Public key
                "private_key": "string",  # Private key
                }
            "cloudspace_id": "string",
            "standby_pool_size": 2,
            "instance_behavior": "single-use",  // or "dedicated"
            "recycle_time_seconds": 300,
            "availability": "shutdown",        // or "stand_by"
            "backup_policy_id": 123            // optional
        }
    """

    _validate_vdi_profile(payload=payload)

    # Validate service account exists and is accessible
    try:
        Customer.get_service_account(customer_id, payload.service_account_id)
        logger.info("Using service account %s for VDI profile creation", payload.service_account_id)
    except Exception as e:
        logger.error("Invalid service account %s: %s", payload.service_account_id, e)
        raise BadRequest(f"Service account '{payload.service_account_id}' not found or not accessible") from e

    normalized_name = _normalize_profile_name(payload.name)
    base_id = f"{customer_id}_{normalized_name}"
    counter = VDIProfile.count(customer_id=customer_id, name=normalized_name)
    profile_id = f"{base_id}_{counter + 1}"

    location, cloudspace_id = decode_validate_cloudspace_id(payload.cloudspace_id)
    vm_config_doc = VMConfig(**asdict(payload.vm_config))

    from meneja.business.service_auth.service_accounts import get_service_account_jwt

    service_account_jwt = get_service_account_jwt(vco_id, customer_id, payload.service_account_id)

    # Optional WireGuard creation (unchanged)
    wireguard_id = None
    if payload.wireguard_config:
        payload.wireguard_config.peers = []
        payload.wireguard_config.name = f"profile_{profile_id}_vdi_wireguard"
        if not payload.wireguard_config.mtu:
            payload.wireguard_config.mtu = 1420
        wireguard_id = add_wireguard_interface(
            location=location,
            cloudspace_id=cloudspace_id,
            jwt=service_account_jwt,
            payload=payload.wireguard_config,
        )

    profile_doc = VDIProfile(
        customer_id=customer_id,
        profile_id=profile_id,
        name=payload.name,
        vco=vco_id,
        vm_config=vm_config_doc,
        cloudspace_id=payload.cloudspace_id,
        standby_pool_size=payload.standby_pool_size,
        instance_behavior=payload.instance_behavior,
        recycle_time_seconds=payload.recycle_time_seconds,
        availability=payload.availability,
        backup_policy_id=payload.backup_policy_id,
        wireguard_id=wireguard_id["interface_id"] if wireguard_id else None,
        service_account_id=payload.service_account_id,
        created_at=time.time(),
        updated_at=time.time(),
        vms=[],  # start empty
        status=VDIPofileStatus.ACTIVE.value,
    )
    profile_doc.save()

    # Attach the service account to the VDI resource
    from meneja.business.vco.customer.service_accounts import attach_service_account_to_resource

    attach_service_account_to_resource(
        customer_id=customer_id,
        resource_type=ServiceAccountResourceType.VDI.value,
        resource_id=profile_id,
        service_account_id=payload.service_account_id,
    )

    logger.info("Successfully attached service account %s to VDI profile %s", payload.service_account_id, profile_id)

    # Remove the extra reconcile spawn to avoid duplicate VM creation and races
    # gevent.spawn(_reconcile_vdi_standby_pool, jwt, customer_id, profile_id, vco_id)

    # Enqueue the async pool creation (non-blocking)
    init_status_task = Task("Init VDI profile creation", timeout=21600)
    init_status_task.set_workload(_log_init_pool_build, profile_id, customer_id, payload.standby_pool_size)
    create_vms_async = Task("Create VDI profile vms async")
    create_vms_async.set_workload(
        enqueue_build_standby_pool, profile_id, customer_id, location, cloudspace_id, service_account_jwt, vco_id
    )
    init_status_task.on_success = create_vms_async
    client: Client = DynaqueueConnection.get_client()
    task = client.submit_task_async(init_status_task)

    # Return immediately; VMs will arrive in the profile as they’re created
    return {"id": profile_id, "standby_job_id": task.task_id}


def update_vdi_profile(customer_id: str, profile_id: str, payload: VDIProfileUpdateStruct) -> dict:
    """Update an existing VDI profile

    Args:
        customer_id (str): The ID of the customer
        profile_id (str): The profile identifier
        payload (VDIProfileUpdateStruct): Incoming updated data

    Returns:
        dict: Success response
    """

    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    if not profile:
        raise NotFound(f"VDI Profile '{profile_id}' not found for customer '{customer_id}'")

    if payload.standby_pool_size < 1:
        raise ValueError("standby_pool_size must be at least 1")
    if payload.recycle_time_seconds < 300 or payload.recycle_time_seconds >= 86400 * 7:
        raise ValueError("recycle_time_seconds must be between 300 and 604800 (7 days)")

    profile.standby_pool_size = payload.standby_pool_size
    profile.recycle_time_seconds = payload.recycle_time_seconds
    profile.availability = payload.availability
    profile.backup_policy_id = payload.backup_policy_id
    profile.vm_config = VMConfig(**asdict(payload.vm_config))
    profile.updated_at = time.time()
    profile.save()

    return {"success": True}


def update_vdi_profile_service_account(customer_id: str, profile_id: str, new_service_account_id: str) -> dict:
    """
    Update the service account for a VDI profile

    Args:
        customer_id (str): Customer ID
        profile_id (str): VDI profile ID
        new_service_account_id (str): New service account ID

    Returns:
        dict: Success response
    """
    from meneja.business.vco.customer.service_accounts import update_service_account_resources

    try:
        # Get the VDI profile
        profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
        if not profile:
            raise NotFound(f"VDI Profile '{profile_id}' not found for customer '{customer_id}'")

        # Get current service account ID
        old_service_account_id = profile.service_account_id
        if not old_service_account_id:
            raise BadRequest(f"VDI Profile '{profile_id}' does not have a current service account")

        if old_service_account_id == new_service_account_id:
            raise BadRequest("New service account ID is the same as current service account ID")

        # Validate new service account exists
        try:
            Customer.get_service_account(customer_id, new_service_account_id)
        except Exception as e:
            logger.error("Invalid service account %s: %s", new_service_account_id, e)
            raise BadRequest(f"Service account '{new_service_account_id}' not found or not accessible") from e

        # Update VDI profile with new service account ID
        profile.service_account_id = new_service_account_id
        profile.updated_at = time.time()
        profile.save()

        # Update service account resources (move resource from old to new service account)
        update_service_account_resources(
            customer_id=customer_id,
            resource_type=ServiceAccountResourceType.VDI.value,
            resource_id=profile_id,
            old_service_account_id=old_service_account_id,
            new_service_account_id=new_service_account_id,
        )

        logger.info(
            "Successfully updated VDI profile %s service account from %s to %s",
            profile_id,
            old_service_account_id,
            new_service_account_id,
        )

        return {"success": True}

    except Exception as e:
        logger.error("Error updating VDI profile service account: %s", e)
        raise


@job(
    "VDI - Cleanup vdi profile resources {profile_id}",
    block=False,
    timeout=3600,
    object_type="vdi_profile",
    object_id="{profile_id}",
    is_single_queued=True,
)
def _purge_vdi_profile_and_standby_vms(
    jwt: str,
    customer_id: str,
    profile_id: str,
    *,
    wait_for_destroy: bool = True,
    wait_timeout_sec: int = 600,
    poll_interval_sec: int = 5,
    only_standby: bool = False,  # delete only entries marked "standby"
) -> None:
    """
    Deletes all VMs referenced in VDIProfile.vms, pops them from the profile,
    and deletes the VDIProfile when none remain.

    Idempotent: safe to call multiple times. If a VM is already gone, it will be pruned from the list.

    Params:
      - wait_for_destroy: if True, poll until the VM is confirmed deleted (or timeout).
      - only_standby:     if True, skip any entries not marked "standby".
      - force:            forwarded to underlying delete call if supported.
    """
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    if not profile:
        logger.info("VDIProfile %s/%s not found; nothing to purge.", customer_id, profile_id)
        return

    # Resolve infra coordinates once (used by delete calls)
    location, cs_id = decode_validate_cloudspace_id(profile.cloudspace_id)
    logging.info("Deleting wireguard interface %s for profile %s/%s", profile.wireguard_id, customer_id, profile_id)
    if profile.wireguard_id:
        delete_remote_wireguard_connection(profile.wireguard_id, location, cs_id, jwt)
        profile.wireguard_id = ""
        profile.save()
    logger.info("Deleted wireguard interface %s for profile %s/%s", profile.wireguard_id, customer_id, profile_id)

    def vm_exists(vm_id: str) -> bool:
        try:
            info = get_vm_info(location=location, jwt=jwt, vm_id=vm_id)
            # if API returns successfully, consider it existing unless it reports a terminal deleted state
            return bool(info) and str(info.get("status", "")).lower() not in {"deleted", "destroyed", "terminated"}
        except Exception:  # pylint: disable=broad-except
            # Treat "not found" as not existing; any hard infra error will bubble on delete
            return False

    def delete_vm(vm_id: str) -> None:
        """
        Replace this with your actual destroy API.
        Prefer a business-layer call if you have one (like cs_business.delete_vm).
        """
        # Example call (adjust args to your API):
        g8_client = G8Client(location, jwt=jwt)
        g8_client.delete_vm(vm_id, permanently=True)

    # Work on a copy to avoid mutating while iterating
    entries = list(profile.vms or [])
    if not entries:
        logger.info("VDIProfile %s/%s has no VMs; deleting profile.", customer_id, profile_id)
        profile.delete()
        return

    remaining = []
    for e in entries:
        if only_standby and getattr(e, "status", None) != "standby":
            # Keep non-standby (e.g., dedicated/attached) if caller asked to only remove standby
            remaining.append(e)
            continue

        vm_id = getattr(e, "vm_id", None)
        if not vm_id:
            # Bad entry: drop it
            logger.warning("Profile %s/%s contains a VM entry without vm_id. Pruning.", customer_id, profile_id)
            continue

        # If already gone, just prune it from profile
        if not vm_exists(vm_id):
            logger.info("VM %s already absent; pruning from profile %s/%s.", vm_id, customer_id, profile_id)
        else:
            try:
                delete_vm(vm_id)
                logger.info("Delete requested for VM %s.", vm_id)
            except Exception as exc:  # pylint: disable=broad-except
                # If delete call fails, keep the entry so we don't lose track.
                logger.exception("Failed to request deletion for VM %s; keeping it on profile. Err: %s", vm_id, exc)
                remaining.append(e)
                continue

            if wait_for_destroy:
                deadline = time.time() + wait_timeout_sec
                while time.time() < deadline:
                    if not vm_exists(vm_id):
                        break
                    time.sleep(poll_interval_sec)
                else:
                    # Timed out; keep entry so we can retry on the next run
                    logger.warning("Timed out waiting for VM %s to be destroyed; keeping it on profile.", vm_id)
                    remaining.append(e)
                    continue

        # VM successfully deleted or already gone → do NOT append to remaining (i.e., prune)

    # Persist pruned list
    try:
        profile.vms = remaining
        profile.save()
    except Exception:  # pylint: disable=broad-except
        logger.exception("Failed to save pruned VM list on profile %s/%s.", customer_id, profile_id)
        # Best effort; if we can’t save, don’t attempt profile delete
        return

    # If none remain, delete the profile itself
    if not remaining:
        try:
            logger.info("Deleted empty VDIProfile %s/%s.", customer_id, profile_id)
            VDIProfile.set_profile_status(customer_id, profile_id, VDIPofileStatus.DELETED.value)
        except Exception:  # pylint: disable=broad-except
            logger.exception("Failed to delete VDIProfile %s/%s after purging VMs.", customer_id, profile_id)
            VDIProfile.set_profile_status(customer_id, profile_id, VDIPofileStatus.ACTIVE.value)


@with_service_account_jwt
def delete_vdi_profile(customer_id: str, profile_id: str, service_account_jwt: str = None) -> dict:
    """Delete a VDI profile."""
    VDIProfile.set_profile_status(customer_id, profile_id, VDIPofileStatus.DELETING.value)
    _purge_vdi_profile_and_standby_vms(service_account_jwt, customer_id, profile_id)


@with_service_account_jwt
def create_vdi_profile_session(
    customer_id: str,
    profile_id: str,
    user_info: dict,
    public_key: str,
    vco_id: str,
    service_account_jwt: str = None,
) -> str:
    """Create a new VDI session using a standby VM if available, atomically.
    Args:
        customer_id (str): The ID of the customer.
        profile_id (str): The ID of the VDI profile.
        user_info (dict): Information about the user.
        public_key (str): The public key for the VDI session.
        vco_id (str): The VCO ID for the VDI session.
        service_account_jwt (str): The service account JWT for authentication (injected by decorator).

    Returns:
        str: The ID of the created VDI session.
    """
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    if not profile:
        raise NotFound(f"VDI profile '{profile_id}' not found for customer '{customer_id}'")
    session_id = str(uuid.uuid4())
    session = VDISession(
        session_id=session_id,
        customer_id=customer_id,
        profile_id=profile_id,
        status=RDPStatus.INITIALIZED.value,
        rdp_target_ip="",
        created_at=time.time(),
        user=VDIUser(username=user_info.username, email=user_info.email),
    )
    session.save()
    _start_vdi_session(session, profile_id, public_key, customer_id, vco_id, service_account_jwt)
    return session_id


@job(
    "VDI - Create VDI session {profile_id}",
    block=False,
    timeout=3600,
    object_type="vdi session",
    object_id="{profile_id}",
)
def _start_vdi_session(
    session: VDISession,
    profile_id: str,
    public_key: str,
    customer_id: str,
    vco_id: str,
    jwt: str,
) -> None:
    """Start a VDI session asynchronously.

    Args:
        session (VDISession): The VDI session to start.
        profile (VDIProfile): The VDI profile associated with the session.
        public_key (str): The public key for the VDI session.
        customer_id (str): The ID of the customer.
        vco_id (str): The VCO ID for the VDI session.
        jwt (str): The JWT for authentication.
    """
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    try:
        location, cloudspace_id = decode_validate_cloudspace_id(profile.cloudspace_id)
        g8_client = G8Client(location, jwt=jwt)

        # Parallel best-effort fetches
        cs_details_g = gevent.spawn(g8_client.get_cloudspace_info, cloudspace_id=cloudspace_id)
        wireguard_details_g = gevent.spawn(
            get_remote_wireguard_interface,
            interface_id=profile.wireguard_id,
            location=location,
            cloudspace_id=cloudspace_id,
            jwt=jwt,
        )

        # 1. Try to atomically claim a standby VM
        profile_id = profile.profile_id
        claimed = _claim_one_standby_vm_atomic(customer_id, profile_id)

        if claimed:
            # claimed is the subdocument BEFORE update (status was "standby");
            # it's now "in-use" in the DB due to the atomic update.
            vm_id = claimed.get("vm_id")
            vm_name = claimed.get("vm_name") or f"{profile.name}-{vm_id[:8]}" if vm_id else profile.name
            rdp_host = claimed.get("rdp_host", "")
            vm_info = {"vm_id": vm_id, "name": vm_name}
            session.vm_id = vm_id
            session.status = RDPStatus.CONNECTING_VM.value
            session.save()

        else:
            # 2. No standby left: create a fresh VM
            session.status = RDPStatus.CREATING_VM.value
            session.save()
            created_vm = cs_business.create_vm(
                name=f"{profile.name}-{uuid.uuid4().hex[:8]}",
                account_id=get_g8_account_id(customer_id, location),
                location=location,
                cloudspace_id=cloudspace_id,
                customer_id=customer_id,
                memory=profile.vm_config.memory,
                vcpus=profile.vm_config.vcpus,
                disk_size=profile.vm_config.bootdisk,
                user_data=profile.vm_config.cloud_init_template,
                description="A virtual desktop instance",
                image_id=profile.vm_config.image_id,
                start_vm=True,
                vco_id=vco_id,
                jwt=jwt,
            )
            session.status = RDPStatus.PROVISIONING_VM.value
            session.save()
            _fetch_vm_info_and_persist(
                profile_id=profile_id,
                customer_id=customer_id,
                location=location,
                jwt=jwt,
                vm_id=created_vm["vm_id"],
            )
            session.vm_id = created_vm["vm_id"]
            session.save()
            # Reuse the just-saved profile entry instead of calling get_vm_info again
            prof_after = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
            saved = next((v for v in (prof_after.vms or []) if int(v.vm_id) == int(created_vm["vm_id"])), None)
            vm_info = saved.to_mongo().to_dict() or get_vm_info(location=location, jwt=jwt, vm_id=created_vm["vm_id"])
            rdp_host = vm_info["rdp_host"]

            # Persist this instance as "in-use" in the profile
            standby_entry = VDIStandbyVM(
                vm_id=created_vm["vm_id"],
                vm_name=vm_info.get("name", created_vm.get("name", "")),
                status="in-use",
                created_at=int(time.time()),
                rdp_host=vm_info["rdp_host"],
            )

            # Prefer atomic push to minimize races with other writers
            try:
                VDIProfile.objects(customer_id=customer_id, profile_id=profile_id).update_one(push__vms=standby_entry)
            except Exception:  # pylint: disable=broad-except
                # Fallback: load/save
                if not isinstance(profile.vms, list):
                    profile.vms = []
                profile.vms.append(standby_entry)
                profile.save()

            session.status = RDPStatus.CONNECTING_VM.value
            session.save()

        # 3. WireGuard peer for the agent
        wireguard_details = wireguard_details_g.get()
        cs_details = cs_details_g.get()

        agent_peer = next((p for p in wireguard_details.peers if p.public_key == public_key), None)

        if not agent_peer:
            peer_name = "_".join([vm_info["name"], "agent"]) if vm_info.get("name") else "agent"
            peer_allowed_ip = _get_wg_available_address(wireguard_details)
            agent_peer_dict = {
                "name": peer_name,
                "public_key": public_key,
                "allowed_ips": [{"network": peer_allowed_ip, "nat": True}],
                "keep_alive": 25,
            }
            add_wireguard_peer(
                location=location,
                cloudspace_id=cloudspace_id,
                jwt=jwt,
                interface_id=profile.wireguard_id,
                payload=agent_peer_dict,
            )
            agent_peer = agent_peer_dict  # Use dict below
        else:
            agent_peer = agent_peer.to_dict()
            peer_allowed_ip = agent_peer["allowed_ips"][0]["network"]

        agent_wg_peer = SessionWireguardPeer(
            public_key=wireguard_details.public_key,
            endpoint=cs_details["external_network_ip"] + f":{wireguard_details.port}",
            allowed_ips=f"{wireguard_details.address}, {cs_details['private_network']}",
        )
        agent_wg_config = SessionWireguardConfig(
            address=peer_allowed_ip,
            peers=[agent_wg_peer],
            mtu=wireguard_details.mtu,
        )

        session.wireguard_config = agent_wg_config
        session.rdp_target_ip = rdp_host
        session.status = RDPStatus.READY.value
        session.save()

    except Exception as exc:  # pylint: disable=broad-except
        session.status = RDPStatus.FAILED.value
        session.save()
        logger.exception("Failed to start VDI session %s: %s", session.session_id, exc)


def _get_wg_available_address(wireguard_interface) -> str:
    """Get first available IP in the interface network
    Args
        wireguard_interface: The WireGuard interface to check
    Returns:
        The first available IP address in the interface network
    """
    used_ips = []

    wg_network = ipaddress.ip_network(wireguard_interface.address, strict=False)

    # get the usable IP range
    network_hosts = list(wg_network.hosts()) if wg_network.num_addresses > 2 else [wg_network.network_address]
    if not network_hosts:
        raise ValueError("No usable IP addresses found for the profile WireGuard interface")

    start_ip = int(network_hosts[0])
    last_ip = int(network_hosts[-1]) if len(network_hosts) > 1 else int(network_hosts[0])

    # add network address to occupied IPs
    used_ips.append(int(ipaddress.ip_address(wireguard_interface.address.split("/")[0])))

    # collect occupied IPs from peers
    for peer in wireguard_interface.peers:
        for allowed_ip in peer.allowed_ips:
            try:
                host_ip = int(ipaddress.ip_address(allowed_ip.network.split("/")[0]))
                if start_ip <= host_ip <= last_ip:
                    used_ips.append(host_ip)
            except (ValueError, KeyError, TypeError):
                continue

    used_ips.sort()

    # check if first IP is available
    if used_ips[0] > start_ip:
        return f"{ipaddress.IPv4Address(start_ip)}/32"

    # look for gaps
    for i in range(len(used_ips) - 1):
        if used_ips[i + 1] - used_ips[i] > 1:
            return f"{ipaddress.IPv4Address(used_ips[i] + 1)}/32"

    # try next IP after last occupied
    if len(used_ips) < last_ip - start_ip + 1:
        next_ip = used_ips[-1] + 1
        return f"{ipaddress.IPv4Address(next_ip)}/32"


def grant_vdi_profile_role(customer_id: str, profile_id: str, role_id: str) -> bool:
    """Grant a role to a VDI profile

    Args:
        customer_id (str): Customer ID
        profile_id (str): VDI profile ID
        role (str): Role to grant

    Returns:
        bool: Success status
    """
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    if not profile:
        raise NotFound(f"VDI Profile '{profile_id}' not found for customer '{customer_id}'")

    customer_roles = Customer.get_roles(customer_id)

    if role_id not in [role.role_id for role in customer_roles]:
        raise ValueError(f"Role '{role_id}' does not exist for customer '{customer_id}'")

    profile.granted_roles.append(role_id)
    profile.save()

    return True


def revoke_vdi_profile_role(customer_id: str, profile_id: str, role_id: str) -> bool:
    """Revoke a role from a VDI profile

    Args:
        customer_id (str): Customer ID
        profile_id (str): VDI profile ID
        role (str): Role to revoke

    Returns:
        bool: Success status
    """
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    if not profile:
        raise NotFound(f"VDI Profile '{profile_id}' not found for customer '{customer_id}'")

    if role_id not in [role for role in profile.granted_roles]:
        raise ValueError(f"Role '{role_id}' is not assigned to profile '{profile_id}' for customer '{customer_id}'")

    profile.granted_roles.remove(role_id)
    profile.save()

    return True


def list_profile_roles(customer_id: str, profile_id: str) -> List:
    """Get roles associated with a VDI profile."""
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    if not profile:
        raise NotFound(f"VDI Profile '{profile_id}' not found for customer '{customer_id}'")
    roles = []
    for role_id in profile.granted_roles:
        role_details = Customer.get_role(customer_id=customer_id, role_id=role_id)
        if role_details:
            roles.append(role_details)

    return roles


def get_profile_role(customer_id: str, profile_id: str, role_id: str) -> str:
    """Get a specific role for a VDI profile

    Args:
        customer_id (str): Customer ID
        profile_id (str): VDI profile ID
        role_id (str): Role ID

    Returns:
        Role: Role details if found, otherwise raises NotFound
    """
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    if not profile:
        raise NotFound(f"VDI Profile '{profile_id}' not found for customer '{customer_id}'")

    role = next((r for r in profile.granted_roles if r == role_id), None)
    if not role:
        raise NotFound(f"Role '{role_id}' not found for VDI Profile '{profile_id}' of customer '{customer_id}'")
    role_details = Customer.get_role(customer_id=customer_id, role_id=role_id)
    if not role_details:
        raise NotFound(f"Role not found for Role '{role_id}' of VDI Profile '{profile_id}' of customer '{customer_id}'")

    return role_details


def get_installer_url(user_os: str = "") -> str:
    """Download the agent installer.
    Args
        user_os (str): The operating system for which to download the agent installer.
    Return
      url to download the agent
    """
    if user_os not in AgentOS.values():
        raise ValueError(f"Unsupported operating system: {os}")
    if EnvironmentName.current() != EnvironmentName.DEV:
        miniocl = MinioConnection.get_client()
    else:
        miniocl = MinioQaDevConnection.get_client()
    bucket = "clouddeskagent"

    agent = ""
    if user_os == AgentOS.WINDOWS:
        agent = "vdi-agent_latest_windows.exe"
    elif user_os == AgentOS.LINUX:
        agent = "amd64/vdi-agent_latest_linux_amd64"
    else:
        return
    object_path = f"{user_os}/{agent}"

    url = miniocl.presigned_get_object(bucket, object_path)
    return url


def update_vm_rdp_status(customer_id: str, profile_id: str, vm_id: int, status: RDPStatus) -> None:
    """Update the status of a VM in a VDI profile.

    Args:
        customer_id (str): Customer ID
        profile_id (str): VDI Profile ID
        vm_id (int): VM ID
        status (RDPStatus): New status for rdp_status field'
    """
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)

    vm_entry: VDIStandbyVM = next((vm for vm in (profile.vms or []) if int(vm.vm_id) == int(vm_id)), None)
    if not vm_entry:
        raise NotFound(f"VM ID '{vm_id}' not found in VDI Profile '{profile_id}'")

    vm_entry.rdp_status = status.value
    vm_entry.updated_at = time.time()
    profile.updated_at = time.time()
    profile.save()


# 1) render the PS1 from the Jinja template
def _render_status_reporter_ps1(vdi_profile: VDIProfile, vm_id: int, jwt: str) -> str:
    with open(STATUS_REPORTER_CONFIG_PATH, "r", encoding="utf-8") as file_handler:
        tmpl = Template(file_handler.read())

    # ensure JSON is compact & safely quoted for PowerShell single-quoted string
    scheme = "https" if EnvironmentName.current() != EnvironmentName.DEV else "http"
    endpoint = f"/customers/{vdi_profile.customer_id}/vdi-profiles/{vdi_profile.profile_id}/vms/{vm_id}/status-report"
    return tmpl.render(
        vco_base_url=f"{scheme}://{vdi_profile.vco}",
        status_endpoint=endpoint,
        jwt=jwt,
        customer_id=vdi_profile.customer_id,
        cloudspace_id=vdi_profile.cloudspace_id,
        vm_id=vm_id,
        profile_id=vdi_profile.profile_id,
    )


# 2) write the rendered file locally and deploy using your helpers
@with_service_account_jwt
def deploy_status_reporter_config(
    customer_id: str, vm_id: str, profile_id: str, service_account_jwt: str = None
) -> None:
    """Deploy the status_reporter_config.ps1 script to a VM and execute it."""
    logger.info("Deploying status reporter config for vm_id=%s", vm_id)
    profile = VDIProfile.get_by_id(profile_id=profile_id, customer_id=customer_id)
    location, _ = decode_validate_cloudspace_id(profile.cloudspace_id)
    g8_client = G8Client(location, jwt=service_account_jwt)
    script_dir = _get_base_dir()
    script_remote = f"{script_dir}\\status_reporter_config.ps1"

    ps_content = _render_status_reporter_ps1(profile, vm_id, service_account_jwt)

    # upload to the VM
    content = base64.b64encode(ps_content.encode("ascii")).decode()
    logger.info("Waiting for vm agent to be ready!")
    _wait_vm_agent_ready(location, service_account_jwt, vm_id)
    logger.info("Writing ps script to the vm!")
    g8_client.write_vm_file(vm_id, content, script_remote, False)
    logger.info("Executing ps script on the vm!")
    execute_command(
        g8_client,
        vm_id,
        "powershell.exe",
        ["/c", script_remote],
        timeout=600,
    )
    logger.info("Deployed status reporter config for vm_id=%s", vm_id)


def _get_base_dir() -> str:
    return "C:\\"


# ---------------------- helpers -----------------------------------------------


def _get_vm_last_update_ts(vm_entry) -> Optional[int]:
    """Return a best-effort 'last update' timestamp for a VM entry, or None."""
    for attr in ("updated_at", "last_seen_at", "heartbeat_at", "last_update_at"):
        val = getattr(vm_entry, attr, None)
        if val is None:
            continue
        if isinstance(val, (int, float)):
            return int(val)
        try:
            if hasattr(val, "timestamp"):
                return int(val.timestamp())
        except Exception:  # pylint: disable=broad-except
            pass
    return None


# --- small data helpers ------------------------------------------------------


def _last_connection(sess) -> Optional[object]:
    return sess.ended_at if sess and hasattr(sess, "ended_at") else None


# --- VM & session mutations --------------------------------------------------
def _set_vm_to_standby(customer_id: str, profile_id: str, vm_id: int, now: int) -> bool:
    """
    Atomically set a matched vms[] element to standby and clear its session binding.
    Returns True if Mongo acknowledged an update.
    """
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    for vm in profile.vms:
        if int(vm.vm_id) == int(vm_id):
            vm.status = "standby"
            vm.updated_at = now
            vm.rdp_status = RDPStatus.NOT_RUNNING
            vm.session_id = None
            return True
    logger.exception("❌ [standby] Failed to set VM %s to standby.", vm_id)
    return False


def _remove_standby_entry(profile, vm_id: int, now: int) -> None:
    """Remove a standby entry from the profile (best-effort)."""
    try:
        VDIProfile.objects(
            customer_id=profile.customer_id,
            profile_id=profile.profile_id,
        ).update_one(pull__vms__vm_id=int(vm_id), set__updated_at=now)
        logger.info("🧹 Removed standby entry for VM %s.", vm_id)
    except Exception:  # pylint: disable=broad-except
        logger.exception("❌ Failed removing standby entry for VM %s.", vm_id)


def _mark_session_not_running(sess, now: int) -> None:
    """Mark session status and save (best-effort)."""
    try:
        sess.status = RDPStatus.NOT_RUNNING
    except Exception:  # pylint: disable=broad-except
        pass
    sess.updated_at = now
    sess.ended_at = now
    sess.save()


def _clear_ended_at_if_active(sess, now: int, last_conn) -> bool:
    """
    If VM is ACTIVE and the session has a ended_at stamp, clear it.
    Returns True if it cleared, False otherwise.
    """
    if last_conn and getattr(last_conn, "ended_at", None):
        last_conn.ended_at = None
        sess.updated_at = now
        sess.save()
        return True
    return False


def _stamp_ended_at_if_missing(sess, now: int, last_conn) -> bool:
    """If missing ended_at, stamp it and save. Return True if stamped."""
    if last_conn and not getattr(last_conn, "ended_at", None):
        last_conn.ended_at = now
        sess.updated_at = now
        sess.save()
        return True
    return False


# --- VM resolution -----------------------------------------------------------
def _find_vm_entry_for_session(profile, sess):
    """
    Locate the standby VM entry associated with a session.
    Returns (vm_id, vm_entry, key_used['vm_id'|'host'|None]).
    """
    vm_id = getattr(sess, "vm_id", None)
    entry = None
    key_used = None

    # First, try by vm_id
    if vm_id is not None:
        entry = next((e for e in (profile.vms or []) if int(getattr(e, "vm_id", -1)) == int(vm_id)), None)
        key_used = "vm_id"

    # Fallback, try by rdp_host
    if not entry:
        host = getattr(sess, "rdp_target_ip", None)
        if host:
            entry = next((e for e in (profile.vms or []) if str(getattr(e, "rdp_host", "")) == str(host)), None)
            key_used = "host"
            if entry and vm_id is None:
                vm_id = getattr(entry, "vm_id", None)

    return vm_id, entry, key_used


def _wait_vm_agent_ready(location: str, jwt: str, vm_id: int, retries: int = 60, delay: int = 10) -> None:
    g8_client = G8Client(location, jwt=jwt)
    for _ in range(retries):
        vm_info = g8_client.get_vm_info(vm_id)
        if vm_info["agent_status"] == "RUNNING":
            return True
        gevent.sleep(delay)
    raise TimeoutError("Timeout waiting for VM Agent to be ready")


def _list_standby_entries(profile) -> list:
    """
    Returns list of standby-vm entries from profile.vms.
    We rely on entry.status == 'standby' (as used elsewhere in your code).
    """
    vms = getattr(profile, "vms", []) or []
    out = []
    for e in vms:
        status = str(getattr(e, "status", "") or "").lower()
        if status == "standby":
            out.append(e)
    return out


def _delete_standby_vm_entry(profile, vm_id: int, jwt: str, location: str, cs_id: int, now: int) -> bool:
    """
    Deletes the VM and removes its standby entry from the profile.
    """
    try:
        delete_virtual_machine(
            jwt=jwt,
            customer_id=profile.customer_id,
            location=location,
            cloudspace_id=int(cs_id),
            vm_id=int(vm_id),
            permanently=True,
            force=False,
            detach_disks=False,
            detach_gpu=False,
        )
        _remove_standby_entry(profile, int(vm_id), now)
        logger.info("🗑️ Deleted excess standby VM %s for profile=%s/%s", vm_id, profile.customer_id, profile.profile_id)
        return True
    except Exception as exc:  # pylint: disable=broad-except
        logger.exception("❌ delete excess standby VM %s failed: %s", vm_id, exc)
        return False


def _enqueue_create_standby_vms(profile, count: int, jwt: str, location: str, cs_id: int) -> int:
    """
    Best-effort enqueue of N standby VM provisions.
    Adjust the workload function to your existing 'create standby VM' task.
    Returns how many were successfully enqueued.
    """
    if count <= 0:
        return 0

    enq = 0
    client: Client = DynaqueueConnection.get_client()
    for i in range(count):
        try:
            t = Task(f"Provision standby VM [{i+1}/{count}] for {profile.profile_id}", timeout=3600)
            # TODO: replace 'provision_standby_vm_task' with your real function
            t.set_workload(
                _create_vm,  # <-- hook into your existing provisioning
                profile.profile_id,
                profile.customer_id,
                location,
                int(cs_id),
                jwt,
                profile.vco,
            )
            client.submit_task_async(t)
            enq += 1
        except Exception:  # pylint: disable=broad-except
            logger.exception(
                "❌ Failed to enqueue standby VM provision for profile=%s/%s", profile.customer_id, profile.profile_id
            )
    if enq:
        logger.info(
            "📥 Enqueued %d standby VM provisions for profile=%s/%s", enq, profile.customer_id, profile.profile_id
        )
    return enq


def _reconcile_profile_pool(profile, jwt: str, location: str, cs_id: int, now: int) -> dict:
    """
    Enforce pool-size rules based on:
      - profile.standby_pool_size
      - profile.maximum_pool_size (0 = unlimited)
      - current standby count (status == 'standby')
      - current total VM entries (len(profile.vms))

    Rules:
      1) If standby_count > standby_pool_size:
           delete oldest standby VMs until equal
      2) If maximum_pool_size == 0:
           create standby VMs to make standby_count == standby_pool_size
      3) If maximum_pool_size > 0:
           create standby VMs to make total_vms == maximum_pool_size (if total < max)

    Returns a dict summary.
    """
    standby_pool_size = int(getattr(profile, "standby_pool_size", 0) or 0)
    maximum_pool_size = int(getattr(profile, "maximum_pool_size", 0) or 0)

    vms = list(getattr(profile, "vms", []) or [])
    total_vms = len(vms)
    standby_entries = _list_standby_entries(profile)
    standby_count = len(standby_entries)

    summary = {
        "standby_pool_size": standby_pool_size,
        "maximum_pool_size": maximum_pool_size,
        "total_vms_before": total_vms,
        "standby_before": standby_count,
        "deleted_standby": 0,
        "created_standby_enqueued": 0,
        "total_vms_after_hint": None,  # hint; actual after depends on async deletions/creations
        "standby_after_hint": None,
        "mode": "unlimited" if maximum_pool_size == 0 else "capped",
    }

    # --- (1) Trim excess standby
    if standby_count > standby_pool_size:
        # Delete the *oldest* standby first (based on last update timestamp)
        # Fallback: arbitrary order if timestamps missing
        def _age_key(e):
            ts = _get_vm_last_update_ts(e)
            return ts if ts is not None else 0

        to_delete = standby_count - standby_pool_size
        victims = sorted(standby_entries, key=_age_key, reverse=True)[:to_delete]
        deleted = 0
        for e in victims:
            vm_id = int(getattr(e, "vm_id", 0) or 0)
            if vm_id:
                if _delete_standby_vm_entry(profile, vm_id, jwt, location, cs_id, now):
                    deleted += 1
        summary["deleted_standby"] = deleted
        # Update hints
        standby_count -= deleted
        total_vms -= deleted

    # --- (2) Creation path (top-up)
    if maximum_pool_size == 0:
        # Unlimited mode: keep standby == standby_pool_size
        need = max(0, standby_pool_size - standby_count)
        if need > 0:
            created = _enqueue_create_standby_vms(profile, need, jwt, location, cs_id)
            summary["created_standby_enqueued"] = created
            standby_count += created  # hint only
            total_vms += created  # hint only
    else:
        # Capped mode: total_vms should equal maximum_pool_size (create only; no deletion except standby trim above)
        if total_vms < maximum_pool_size:
            need = maximum_pool_size - total_vms
            created = _enqueue_create_standby_vms(profile, need, jwt, location, cs_id)
            summary["created_standby_enqueued"] = created
            total_vms += created  # hint only
            # We don't force standby == standby_pool_size here per your spec

    summary["total_vms_after_hint"] = total_vms
    summary["standby_after_hint"] = standby_count
    logger.info(
        "🧮 Pool reconcile for profile=%s/%s → %s | standby %d→%d (target=%d) | total %d→%d (max=%s) | -%d +%d",
        profile.customer_id,
        profile.profile_id,
        summary["mode"],
        summary["standby_before"],
        summary["standby_after_hint"],
        standby_pool_size,
        summary["total_vms_before"],
        summary["total_vms_after_hint"],
        "∞" if maximum_pool_size == 0 else maximum_pool_size,
        summary["deleted_standby"],
        summary["created_standby_enqueued"],
    )
    return summary


# -------- workloads (run inside tasks) --------------------------------------
def _process_single_session_for_recycler(
    *,
    profile,
    sess,
    location: str,
    cs_id: int,
    now: int,
    jwt: str,
) -> dict:
    """
    Encapsulates the per-session recycle logic:
      - If VM RDP is ACTIVE → clear ended_at stamp (if present) and exit
      - If VM RDP is NOT_RUNNING and stale → delete VM and remove standby entry
      - Else use ended_at timer:
          * If first time → stamp and exit
          * If elapsed >= recycle threshold:
              - SINGLE_USE → delete VM + remove standby entry
              - DEDICATED  → set VM to standby
    Returns a small dict describing the action taken.
    """
    sid = getattr(sess, "session_id", "?")
    result = {"session_id": sid, "action": "none", "details": ""}

    # Normalize config
    recycle_after = int(getattr(profile, "recycle_time_seconds", 0) or 0)
    behavior = str(getattr(profile, "instance_behavior", "") or "").upper()

    # Resolve VM entry for this session
    vm_id, vm_entry, key_used = _find_vm_entry_for_session(profile, sess)
    if not vm_entry:
        logger.info(
            "⏭️ [sess %s] no vm_entry (via=%s vm_id=%s host=%s)",
            sid,
            key_used,
            getattr(sess, "vm_id", None),
            getattr(sess, "rdp_target_ip", None),
        )
        result["details"] = "no_vm_entry"
        return result

    vm_rdp = str(getattr(vm_entry, "rdp_status", "") or "").upper()
    last_conn = _last_connection(sess)

    # (1) ACTIVE → clear ended_at if exists
    if vm_rdp == RDPStatus.ACTIVE:
        if _clear_ended_at_if_active(sess, now, last_conn):
            logger.info("✅ [sess %s] ACTIVE → cleared ended_at", sid)
            result.update(action="cleared_disconnect", details="rdp_active")
        else:
            result.update(action="noop", details="rdp_active_no_stamp")
        return result

    # (2) NOT_RUNNING & stale → delete as dead VM
    if vm_rdp == RDPStatus.NOT_RUNNING:
        vm_last = _get_vm_last_update_ts(vm_entry)
        if vm_last is not None:
            age = now - int(vm_last)
            if age >= profile.recycle_time_seconds:
                try:
                    delete_virtual_machine(
                        jwt=jwt,
                        customer_id=profile.customer_id,
                        location=location,
                        cloudspace_id=int(cs_id),
                        vm_id=int(vm_id),
                        permanently=True,
                        force=False,
                        detach_disks=False,
                        detach_gpu=False,
                    )
                    _remove_standby_entry(profile, int(vm_id), now)
                    _mark_session_not_running(sess, now)
                    logger.info("🗑️ [sess %s] Deleted stale NOT_RUNNING VM %s (age=%ss)", sid, vm_id, age)
                    return {"session_id": sid, "action": "deleted_not_running", "details": f"age={age}s"}
                except Exception as exc:  # pylint: disable=broad-except
                    logger.exception("❌ [sess %s] delete VM %s failed (NOT_RUNNING): %s", sid, vm_id, exc)
                    return {"session_id": sid, "action": "error", "details": "delete_failed_not_running"}
        # No timestamp or not stale enough → fall through to disconnect-timer

    # (3) Disconnect-timer path
    # First-time disconnected → stamp and exit
    if _stamp_ended_at_if_missing(sess, now, last_conn):
        logger.info("⏱️ [sess %s] first disconnect → stamped at %s", sid, now)
        return {"session_id": sid, "action": "stamped_disconnect", "details": f"stamp={now}"}

    # Already stamped? Check the threshold
    if last_conn and getattr(last_conn, "ended_at", None):
        elapsed = now - int(getattr(last_conn, "ended_at"))
        if elapsed < recycle_after:
            return {"session_id": sid, "action": "noop", "details": f"not_due_yet elapsed={elapsed}s"}

        # Due for recycle based on behavior
        if behavior == InstanceBehaviorEnum.SINGLE_USE:
            try:
                delete_virtual_machine(
                    jwt=jwt,
                    customer_id=profile.customer_id,
                    location=location,
                    cloudspace_id=int(cs_id),
                    vm_id=int(vm_id),
                    permanently=True,
                    force=False,
                    detach_disks=False,
                    detach_gpu=False,
                )
                _remove_standby_entry(profile, int(vm_id), now)
                _mark_session_not_running(sess, now)
                logger.info(
                    "🗑️ [sess %s] SINGLE_USE → deleted VM %s (elapsed=%ss ≥ %ss)", sid, vm_id, elapsed, recycle_after
                )
                return {"session_id": sid, "action": "deleted_single_use", "details": f"elapsed={elapsed}s"}
            except Exception as exc:  # pylint: disable=broad-except
                logger.exception("❌ [sess %s] delete VM %s failed (SINGLE_USE): %s", sid, vm_id, exc)
                return {"session_id": sid, "action": "error", "details": "delete_failed_single_use"}

        elif behavior == InstanceBehaviorEnum.DEDICATED:
            ok = _set_vm_to_standby(profile.customer_id, profile.profile_id, int(vm_id), now)
            if ok:
                _mark_session_not_running(sess, now)
                logger.info(
                    "♻️ [sess %s] DEDICATED → set VM %s to standby (elapsed=%ss ≥ %ss)",
                    sid,
                    vm_id,
                    elapsed,
                    recycle_after,
                )
                return {"session_id": sid, "action": "converted_to_standby", "details": f"elapsed={elapsed}s"}
            else:
                logger.info("❌ [sess %s] DEDICATED → failed to set VM %s to standby", sid, vm_id)
                return {"session_id": sid, "action": "error", "details": "standby_update_failed"}

        else:
            logger.info("⏭️ [sess %s] unknown behavior=%s; skipping", sid, behavior)
            return {"session_id": sid, "action": "skipped", "details": f"unknown_behavior={behavior}"}

    # No connections array at all or nothing actionable
    return {"session_id": sid, "action": "noop", "details": "no_connections"}


def _wf_log_init_profile_recycle(profile_id: str, customer_id: str) -> None:
    logger.info("▶️ [WF] Start recycle workflow for profile=%s/%s", customer_id, profile_id)


def _wf_scan_sessions(profile_id: str, customer_id: str):
    """
    Return a list of session_ids that belong to this profile.
    Kept fast & minimal to be a good 'scan' step in the workflow.
    """
    qs = VDISession.list(customer_id=customer_id, profile_id=profile_id).only("session_id")
    sessions = list(qs) if hasattr(qs, "__iter__") else []
    ids = [getattr(s, "session_id", "") for s in sessions if getattr(s, "session_id", "")]
    logger.info("ℹ️ [WF] Scan sessions: profile=%s/%s → %d sessions", customer_id, profile_id, len(ids))
    return ids


def _wf_fanout_recycle_session_tasks(profile_id: str, customer_id: str, jwt: str) -> dict:
    """
    Fan-out: fetch minimal profile context, then enqueue one async Task per session.
    Returns a small summary with child task IDs (best-effort).
    """
    # Lightweight profile context
    profile: VDIProfile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
    try:
        location, cs_id = decode_validate_cloudspace_id(profile.cloudspace_id)
    except Exception as exc:  # pylint: disable=broad-except
        logger.exception(
            "❌ [WF] bad cloudspace_id=%s for profile=%s/%s: %s", profile.cloudspace_id, customer_id, profile_id, exc
        )
        return {"enqueued": 0, "children": []}

    now = int(time.time())
    _reconcile_profile_pool(profile, jwt=jwt, location=location, cs_id=cs_id, now=now)

    # Re-scan here to avoid passing large data across task boundaries
    session_ids = _wf_scan_sessions(profile_id, customer_id)
    if not session_ids:
        return {"enqueued": 0, "children": []}

    client: Client = DynaqueueConnection.get_client()
    now = int(time.time())

    child_task_ids = []
    enqueued = 0
    for sid in session_ids:
        try:
            t = Task(f"Recycle session {sid}", timeout=1800)
            # Keep workload minimal: it will fetch session doc internally
            t.set_workload(_wf_recycle_single_session_wrapper, customer_id, profile_id, sid, location, cs_id, now, jwt)
            res = client.submit_task_async(t)
            child_task_ids.append(res.task_id)
            enqueued += 1
        except Exception:  # pylint: disable=broad-except
            logger.exception(
                "❌ [WF] failed to enqueue per-session task sid=%s (profile=%s/%s)", sid, customer_id, profile_id
            )

    logger.info("📤 [WF] Fan-out: profile=%s/%s → enqueued %d session tasks", customer_id, profile_id, enqueued)
    return {"enqueued": enqueued, "children": child_task_ids}


def _wf_recycle_single_session_wrapper(
    customer_id: str, profile_id: str, session_id: str, location: str, cs_id: int, now: int, jwt: str
) -> None:
    """
    Thin wrapper that loads the session and delegates to your existing per-session logic.
    Keeps the workflow code focused on orchestration, not business rules.
    """
    # You can expand fields if your per-session needs more
    sess = VDISession.get_by_id(customer_id=customer_id, profile_id=profile_id, session_id=session_id)
    profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)

    # Delegate to your existing per-session processor (adapt name if needed).
    # This should encapsulate the ACTIVE / NOT_RUNNING / disconnect-timer decisions.
    _process_single_session_for_recycler(profile=profile, sess=sess, location=location, cs_id=cs_id, now=now, jwt=jwt)


def _wf_finalize_profile_recycle(profile_id: str, customer_id: str) -> None:
    logger.info("⏹️ [WF] Done enqueueing per-session tasks for profile=%s/%s", customer_id, profile_id)


# -------- public API: enqueue workflow(s) -----------------------------------


def enqueue_profile_recycle_workflow(customer_id: str, profile_id: str) -> dict:
    """
    Builds a 1-root-task workflow for a single profile that:
      1) Logs init
      2) Fan-out enqueues async per-session tasks
      3) Finalizes (log) and exits immediately
    Returns the root workflow task id and a best-effort snapshot of fan-out info.
    """
    # Root "init" task
    init_task = Task("Init VDI profile recycle", timeout=3600)
    init_task.set_workload(_wf_log_init_profile_recycle, profile_id, customer_id)

    # Fan-out task (runs after init)
    fanout_task = Task("Enqueue per-session recyclers", timeout=3600)
    # The fanout task itself will enqueue children asynchronously
    location, _ = decode_validate_cloudspace_id(VDIProfile.get_by_id(customer_id, profile_id).cloudspace_id)
    fanout_task.set_workload(_wf_fanout_recycle_session_tasks, profile_id, customer_id, get_g8_jwt_from_db(location))

    # Finalize/log task (runs after fanout fires the children)
    finalize_task = Task("Finalize profile recycle")
    finalize_task.set_workload(_wf_finalize_profile_recycle, profile_id, customer_id)

    # Wire the chain: init -> fanout -> finalize
    init_task.on_success = fanout_task
    fanout_task.on_success = finalize_task

    # Submit the root and return immediately (children run async)
    client: Client = DynaqueueConnection.get_client()
    root = client.submit_task_async(init_task)

    logger.info("🧩 [WF] Submitted recycle workflow root=%s for profile=%s/%s", root.task_id, customer_id, profile_id)

    # (Optional) Run a quick local preview of fan-out count by calling the scanner.
    # This does NOT wait for remote execution; it’s just a hint to the caller.
    preview_count = len(_wf_scan_sessions(profile_id, customer_id))

    return {
        "workflow_task_id": root.task_id,
        "profile_id": profile_id,
        "customer_id": customer_id,
        "preview_session_count": preview_count,
    }


def enqueue_recycle_workflows_for_all_profiles() -> dict:
    """
    Iterate all profiles and submit one workflow per profile.
    Returns counts and root task IDs, without waiting for any to complete.
    """
    logger.info("🧭 [WF] Scanning all VDI profiles for recycle workflow scheduling")
    all_profiles = VDIProfile.list_all()
    profiles = [p for p in all_profiles if getattr(p, "status", None) == VDIPofileStatus.ACTIVE.value]
    total = len(profiles)
    logger.info("🧭 [WF] Scheduling recycle workflows for %s profiles", total)

    client: Client = DynaqueueConnection.get_client()
    roots = []  # (task_id, customer_id, profile_id)

    enqueued = 0
    for p in profiles:
        try:
            # Submit per-profile workflow root (init task)
            init_task = Task("Init VDI profile recycle", timeout=3600)
            init_task.set_workload(_wf_log_init_profile_recycle, p.profile_id, p.customer_id)

            fanout_task = Task("Enqueue per-session recyclers", timeout=3600)
            location, _ = decode_validate_cloudspace_id(p.cloudspace_id)
            jwt = get_g8_jwt_from_db(location)
            fanout_task.set_workload(_wf_fanout_recycle_session_tasks, p.profile_id, p.customer_id, jwt)

            finalize_task = Task("Finalize profile recycle")
            finalize_task.set_workload(_wf_finalize_profile_recycle, p.profile_id, p.customer_id)

            init_task.on_success = fanout_task
            fanout_task.on_success = finalize_task

            res = client.submit_task_async(init_task)
            roots.append((res.task_id, p.customer_id, p.profile_id))
            enqueued += 1
        except Exception:  # pylint: disable=broad-except
            logger.exception(
                "❌ [WF] Failed to submit workflow for profile=%s/%s",
                getattr(p, "customer_id", "?"),
                getattr(p, "profile_id", "?"),
            )

    logger.info("📦 [WF] Enqueued %d/%d profile workflows", enqueued, total)
    return {
        "total_profiles": total,
        "enqueued": enqueued,
        "workflow_roots": [{"task_id": tid, "customer_id": cid, "profile_id": pid} for tid, cid, pid in roots],
    }


@job(
    "VDI - Recycle all VDI profiles (workflow fan-out)",
    block=False,
    timeout=1800,
    object_type="vdi_profile_workflow",
    object_id="global",
)
def _run_recycle_workflow_for_all_profiles() -> None:
    """
    Kick off a workflow per profile to recycle stale sessions.
    Each workflow handles scanning, fan-out to session recyclers, and finalize logging.
    """
    logger.info("🌐 Starting global recycle workflow for all VDI profiles")
    try:
        result = enqueue_recycle_workflows_for_all_profiles()
        logger.info("🌐 Enqueued %d workflows for %d profiles", result["enqueued"], result["total_profiles"])
    except Exception:  # pylint: disable=broad-except
        logger.exception("❌ Global recycle workflow launch failed")


@schedule(cron="*/10 * * * *", description="Run VDI recycle workflows every 10 minutes")
def scheduled_recycle_workflows() -> None:
    """Run the per-profile recycle workflow every 10 minutes."""
    # You can pull JWT from config/env here if not passed in directly
    # jwt = get_g8_jwt_from_db()  # or however you normally retrieve it
    logger.info("🕑 Scheduled task: enqueue recycle workflows for all VDI profiles")
    _run_recycle_workflow_for_all_profiles()
