# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import inspect
import logging
from functools import wraps

from werkzeug.exceptions import BadRequest

from .service_accounts import get_service_account_jwt

logger = logging.getLogger(__name__)


def with_service_account_jwt(func):
    """
    Decorator that injects service account JWT for VDI operations.

    Extracts customer_id and profile_id from function arguments (positional or keyword),
    looks up the VDI profile to get the service_account_id, gets the JWT, and injects it.
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # Get function signature to map positional args to parameter names
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            # Extract required parameters
            customer_id = bound_args.arguments.get("customer_id")
            profile_id = bound_args.arguments.get("profile_id")

            if not customer_id or not profile_id:
                raise BadRequest("customer_id and profile_id are required for VDI operations")

            # Get VDI profile and service account JWT
            from meneja.model.vco.vdi import VDIProfile

            vdi_profile = VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)
            if not vdi_profile.service_account_id:
                raise BadRequest(f"VDI profile '{profile_id}' does not have an associated service account")

            # Use VCO ID from profile
            vco_id = vdi_profile.vco

            service_account_jwt = get_service_account_jwt(vco_id, customer_id, vdi_profile.service_account_id)

            # Add JWT to bound arguments and call function
            bound_args.arguments["service_account_jwt"] = service_account_jwt
            return func(*bound_args.args, **bound_args.kwargs)

        except Exception as e:
            logger.error("Service account JWT decorator failed: %s", e)
            raise BadRequest(f"Failed to authenticate VDI operation: {e}") from e

    return wrapper
