# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import logging
import random

from werkzeug.exceptions import BadRequest

from meneja.business import get_vco_iam_client
from meneja.lib.itsyouonline import ItsyouOnlineClient

logger = logging.getLogger(__name__)


def _get_service_account_api_key(customer_id: str, service_account_id: str, iam_vco_client: ItsyouOnlineClient) -> str:
    """
    Generate an API key for a service account

    Args:
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
        iam_vco_client (ItsyouOnlineClient): IAM VCO client

    Returns:
        str: Generated API key secret
    """
    try:
        from meneja.model.vco.customer import Customer

        # Get service account to retrieve its technical organization
        db_service_account = Customer.get_service_account(customer_id, service_account_id)
        tech_org = db_service_account.tech_iam_organization

        logger.info("Generating API key for service account %s with organization: %s", service_account_id, tech_org)

        # Generate random label for the API key
        label = f"api_key_{service_account_id}_{random.randint(0, 100000)}"
        api_key_secret = iam_vco_client.generate_api_key(organization=tech_org, label=label, grant_type=True)

        logger.info("Successfully generated API key for service account %s", service_account_id)
        return api_key_secret

    except Exception as e:
        logger.error("Failed to generate API key for service account %s: %s", service_account_id, e)
        raise


def get_service_account_jwt(vco_id: str, customer_id: str, service_account_id: str) -> str:
    """
    Get JWT token using service account API key

    Args:
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID

    Returns:
        str: JWT token
    """
    try:
        from meneja.model.vco.customer import Customer

        iam_vco_client = get_vco_iam_client(vco_id)
        api_key_secret = _get_service_account_api_key(customer_id, service_account_id, iam_vco_client)

        # Get service account to retrieve its technical organization
        db_service_account = Customer.get_service_account(customer_id, service_account_id)
        tech_org = db_service_account.tech_iam_organization

        logger.info(
            "Getting JWT token using API key for service account %s with organization: %s",
            service_account_id,
            tech_org,
        )

        iam_vco_client.set_custom_jwt_from_api_key(client_id=tech_org, client_secret=api_key_secret)
        service_jwt = iam_vco_client.jwt

        logger.info("Successfully obtained JWT token for service account %s", service_account_id)
        return service_jwt

    except Exception as e:
        logger.error("Failed to get JWT from API key for service account %s: %s", service_account_id, e)
        raise BadRequest(f"Failed to get JWT from API key for service account {service_account_id}: {e}") from e
