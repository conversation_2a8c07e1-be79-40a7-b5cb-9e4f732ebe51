# status_reporter_config.ps1.j2
Param()

$ErrorActionPreference = 'Stop'

function Require-Admin {
  $wi = [Security.Principal.WindowsIdentity]::GetCurrent()
  $wp = New-Object Security.Principal.WindowsPrincipal $wi
  if (-not $wp.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Error 'This script must run as Administrator.'
    exit 1
  }
}
Require-Admin

$KeyPaths = @(
  'Registry::HKEY_LOCAL_MACHINE\whitesky.cloud\status_reporter',
  'Registry::HKEY_LOCAL_MACHINE\SOFTWARE\whitesky.cloud\status_reporter'
)

# choose/create first writable key
$Key = $null
foreach ($kp in $KeyPaths) {
  try {
    New-Item -Path $kp -Force -ErrorAction Stop | Out-Null
    $Key = $kp; break
  } catch {}
}
if (-not $Key) { throw 'Unable to create or open target registry key.' }

# strings
Set-ItemProperty -Path $Key -Name vcoBaseUrl     -Type String -Value '{{ vco_base_url }}'
Set-ItemProperty -Path $Key -Name statusEndpoint -Type String -Value '{{ status_endpoint }}'
Set-ItemProperty -Path $Key -Name jwt            -Type String -Value '{{ jwt }}'
Set-ItemProperty -Path $Key -Name customerId     -Type String -Value '{{ customer_id }}'
Set-ItemProperty -Path $Key -Name cloudspaceId   -Type String -Value '{{ cloudspace_id }}'
Set-ItemProperty -Path $Key -Name vmId           -Type String -Value '{{ vm_id }}'
Set-ItemProperty -Path $Key -Name profileId      -Type String -Value '{{ profile_id }}'

# restart service so it picks up immediately
try {
  if (Get-Service -Name 'CloudDeskStatusReporter' -ErrorAction SilentlyContinue) {
    Restart-Service -Name 'CloudDeskStatusReporter' -Force -ErrorAction Stop
  }
} catch {
  Write-Warning "Could not restart service: $($_.Exception.Message)"
}

[pscustomobject]@{ ok=$true; key=$Key } | ConvertTo-Json -Compress | Out-Host