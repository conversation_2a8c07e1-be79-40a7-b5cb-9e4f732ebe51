# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REP<PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import logging

from meneja.lib.enumeration import EnvironmentName
from meneja.lib.itsyouonline import ItsyouOnlineClient
from meneja.model.vco import VCO

logger = logging.getLogger(__name__)


def get_vco_iam_client(vco_id: str) -> ItsyouOnlineClient:
    """Creates a vco iam client using the stored data

    Args:
        vco_id (str): The VCO id

    Returns:
        ItsyouOnlineClient
    """
    is_https = EnvironmentName.current() != EnvironmentName.TEST
    vco = VCO.get_by_id(vco_id, only=["iam_domain", "iam_root_organization", "iam_root_organization_api_key"])
    iam_client = ItsyouOnlineClient.new(
        vco.iam_domain, vco.iam_root_organization, vco.iam_root_organization_api_key, is_https
    )
    return iam_client
