package oauthservice

import (
	"crypto/ecdsa"
	"net/http"
	"strings"

	"github.com/gorilla/context"
	"github.com/gorilla/mux"
	"github.com/gorilla/sessions"
	log "github.com/sirupsen/logrus"

	"git.gig.tech/gig-meneja/iam/credentials/oauth2"
	"git.gig.tech/gig-meneja/iam/globalconfig"
	"git.gig.tech/gig-meneja/iam/identityservice/security"
	"github.com/justinas/alice"
)

// SessionService declares a context where you can have a logged in user
type SessionService interface {
	//GetLoggedInUser returns an authenticated user, or an empty string if there is none
	GetLoggedInUser(request *http.Request, w http.ResponseWriter) (username string, err error)
	//GetOauthUser returns a user in a protected oauth session, or an empty string if there is none
	GetOauthUser(request *http.Request, w http.ResponseWriter) (username string, err error)
	//SetAPIAccessToken sets the api access token for this session
	SetAPIAccessToken(w http.ResponseWriter, token string) (err error)
	//RequestEmailValidation requests email validation and returns the validation key
	RequestEmailValidation(request *http.Request, username string, email string, confirmationurl string, langKey string) (key string, err error)
}

// IdentityService provides some basic knowledge about authorizations required for the oauthservice
type IdentityService interface {
	//FilterAuthorizedScopes filters the requested scopes to the ones that are authorizated, if no authorization exists, authorizedScops is nil
	FilterAuthorizedScopes(r *http.Request, username string, grantedTo string, requestedscopes []string) (authorizedScopes []string, err error)
	//FilterPossibleScopes filters the requestedScopes to the relevant ones that are possible
	// For example, a `user:memberof:orgid1` is not possible if the user is not a member the `orgid1` organization and there is no outstanding invite for this organization
	// If allowInvitations is true, invitations to organizations allows the "user:memberof:organization" as possible scopes
	FilterPossibleScopes(r *http.Request, username string, requestedScopes []string, allowInvitations bool) (possibleScopes []string, err error)
}

// Service is the oauthserver http service
type Service struct {
	sessionService  SessionService
	identityService IdentityService
	router          *mux.Router
	jwtSigningKey   *ecdsa.PrivateKey
	issuer          string
}

// SessionStore is used to store the OIDC state between requests
var SessionStore *sessions.CookieStore

// initSessionStore initializes the session store with the given cookie secret
func initSessionStore(cookieSecret string) {
	SessionStore = sessions.NewCookieStore([]byte(cookieSecret))
	SessionStore.Options.HttpOnly = true
	SessionStore.Options.Secure = true
	SessionStore.Options.MaxAge = 3600
}

// NewService creates and initializes a Service
func NewService(sessionService SessionService, identityService IdentityService, ecdsaKey *ecdsa.PrivateKey, issuer string, cookieSecret string) (service *Service, err error) {
	service = &Service{
		sessionService:  sessionService,
		identityService: identityService,
		jwtSigningKey:   ecdsaKey,
		issuer:          issuer,
	}

	initSessionStore(cookieSecret)
	return
}

const (
	//AuthorizationGrantCodeType is the requested response_type for an 'authorization code' oauth2 flow
	AuthorizationGrantCodeType = "code"
	//ClientCredentialsGrantCodeType is the requested grant_type for a 'client credentials' oauth2 flow
	ClientCredentialsGrantCodeType = "client_credentials"
)

// GetWebuser returns the authenticated user if any or an empty string if not
func (service *Service) GetWebuser(r *http.Request, w http.ResponseWriter) (username string, err error) {
	username, err = service.sessionService.GetLoggedInUser(r, w)
	return
}

// GetOauthUser returns a user in a protected oauth session, or an empty string if there is none
func (service *Service) GetOauthUser(r *http.Request, w http.ResponseWriter) (username string, err error) {
	username, err = service.sessionService.GetOauthUser(r, w)
	return
}

func (service *Service) filterPossibleScopes(r *http.Request, username string, requestedScopes []string, allowInvitations bool) (possibleScopes []string, err error) {
	log.Info("=== SERVICE ACCOUNT DEBUG: oauthservice.filterPossibleScopes called ===")
	log.Info("SERVICE ACCOUNT DEBUG: Username: ", username)
	log.Info("SERVICE ACCOUNT DEBUG: Requested scopes: ", requestedScopes)
	log.Info("SERVICE ACCOUNT DEBUG: Allow invitations: ", allowInvitations)

	possibleScopes, err = service.identityService.FilterPossibleScopes(r, username, requestedScopes, allowInvitations)

	log.Info("SERVICE ACCOUNT DEBUG: Possible scopes result: ", possibleScopes)
	if err != nil {
		log.Error("SERVICE ACCOUNT DEBUG: FilterPossibleScopes error: ", err)
	}
	return
}

// CloudAdminMiddleware creates a new CloudAdminMiddleware
func AdminMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Get the root org from global config
		config := globalconfig.NewManager()
		rootOrg, err := config.GetByKey("rootOrg")
		if err != nil {
			log.Debug("Error while reading rootOrg from config", err.Error())
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}

		var username string

		// First try to get username from JWT token
		token, err := oauth2.GetValidJWT(r, security.JWTPublicKey)
		if err == nil && token != nil && token.Valid {
			if usernameFromToken, ok := token.Claims["username"].(string); ok && usernameFromToken != "" {
				username = usernameFromToken
			}
		}

		// If no username from JWT, try context
		if username == "" {
			if authenticatedUser := context.Get(r, "authenticateduser"); authenticatedUser != nil {
				if usernameFromContext, ok := authenticatedUser.(string); ok {
					username = usernameFromContext
				}
			}
		}

		// If still no username, return unauthorized
		if username == "" {
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}

		// Check if user has staff scope for root org
		if err != nil {
			log.Debug("Error while reading JWT", err.Error())
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}

		scopes := make([]string, 0, 0)
		rawclaims, _ := token.Claims["scope"].([]interface{})
		for _, rawclaim := range rawclaims {
			scope, _ := rawclaim.(string)
			scopes = append(scopes, scope)
		}

		scopestring := strings.Join(scopes, ",")
		staffScope := "user:memberof:" + rootOrg.Value + ".staff"

		// Check if user has the staff scope
		if !strings.Contains(scopestring, staffScope) {
			log.Debugf("User %s does not have required scope %s", username, staffScope)
			http.Error(w, http.StatusText(http.StatusForbidden), http.StatusForbidden)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// AddRoutes adds the routes and handlerfunctions to the router
func (service *Service) AddRoutes(router *mux.Router) {
	service.router = router
	router.HandleFunc("/v1/oauth/authorize", service.AuthorizeHandler).Methods("GET")
	router.HandleFunc("/v1/oauth/authorize",
		func(w http.ResponseWriter, r *http.Request) {
			w.Header().Add("Allow", "GET")
		}).Methods("OPTIONS")

	router.HandleFunc("/v1/oauth/access_token", service.AccessTokenHandler).Methods("POST")
	router.HandleFunc("/v1/oauth/access_token",
		func(w http.ResponseWriter, r *http.Request) {
			w.Header().Add("Allow", "POST")
			// Allow cors
			w.Header().Add("Access-Control-Allow-Origin", "*")
			w.Header().Add("Access-Control-Allow-Methods", "POST")
			// Allow all requested headers, we do not use them anyway
			w.Header().Add("Access-Control-Allow-Headers", r.Header.Get("Access-Control-Request-Headers"))
		}).Methods("OPTIONS")

	router.HandleFunc("/v1/oauth/jwt", service.JWTHandler).Methods("POST", "GET")
	router.HandleFunc("/v1/oauth/jwt",
		func(w http.ResponseWriter, r *http.Request) {
			w.Header().Add("Allow", "GET,POST")
		}).Methods("OPTIONS")
	router.HandleFunc("/v1/oauth/jwt/refresh", service.RefreshJWTHandler).Methods("POST", "GET")
	router.HandleFunc("/v1/oauth/jwt/refresh",
		func(w http.ResponseWriter, r *http.Request) {
			w.Header().Add("Allow", "GET,POST")
		}).Methods("OPTIONS")

	// Public OIDC routes - read only
	router.HandleFunc("/oidc_providers", service.ListPublicOIDCProvidersHandler).Methods("GET")
	router.HandleFunc("/password_login/status", service.GetPasswordLoginStatusHandler).Methods("GET")

	// OIDC login flow routes
	router.HandleFunc("/oidc/login/{id}", service.InitiateOIDCLoginHandler).Methods("GET")
	router.HandleFunc("/oidc/callback/{id}", service.OIDCCallbackHandler).Methods("GET")
	router.HandleFunc("/oidc/verify-email", service.GetOIDCVerificationInfo).Methods("GET")
	router.HandleFunc("/oidc/resend-validation", service.ResendOIDCValidation).Methods("POST")

	// Admin OIDC Provider routes - require authentication and admin access
	router.Handle("/admin/oidc_providers", alice.New(AdminMiddleware).Then(http.HandlerFunc(service.ListOIDCProvidersHandler))).Methods("GET")
	router.Handle("/admin/oidc_providers/{id}", alice.New(AdminMiddleware).Then(http.HandlerFunc(service.GetOIDCProviderHandler))).Methods("GET")
	router.Handle("/admin/oidc_provider_logs", alice.New(AdminMiddleware).Then(http.HandlerFunc(service.GetOIDCProviderLogsHandler))).Methods("GET")

	InitModels()
}
